#!/usr/bin/env python3
"""
Extract chat transcripts for all 4,297 customers
"""

import sys
from pathlib import Path
import requests
import json
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class ChatTranscriptExtractor:
    """Extract chat transcripts for all customers"""

    def __init__(self):
        self.customers = {}
        self.transcripts = {}
        self.errors = []
        self.progress_file = Path("data/transcript_progress.json")

    def run_transcript_extraction(self):
        """Run the complete transcript extraction process"""

        print("💬 CHAT TRANSCRIPT EXTRACTOR")
        print("=" * 60)
        print("📊 Extracting chat transcripts for all 4,297 customers")
        print("🔍 Looking for conversation sessions and detailed messages")
        print("=" * 60)

        # Load the customer data we just extracted
        if not self.load_customer_data():
            print("❌ Could not load customer data")
            return

        # Setup session and headers
        session, headers, base_url = self.setup_session()

        # Extract transcripts for customers with contact history
        customers_with_contacts = self.filter_customers_with_contacts()
        print(f"📊 Found {len(customers_with_contacts)} customers with contact history")

        # Extract transcripts
        self.extract_all_transcripts(session, headers, base_url, customers_with_contacts)

        # Save results
        self.save_transcript_data()

    def load_customer_data(self):
        """Load the customer data we extracted"""

        customer_file = Path("data/graphql_complete_customers.json")

        if not customer_file.exists():
            print(f"❌ Customer data file not found: {customer_file}")
            return False

        try:
            with open(customer_file, 'r') as f:
                data = json.load(f)

            self.customers = data.get('customers', {})
            print(f"✅ Loaded {len(self.customers)} customers")
            return True

        except Exception as e:
            print(f"❌ Error loading customer data: {e}")
            return False

    def setup_session(self):
        """Setup session with authentication"""

        config = ConfigLoader.load_config("config/config.yaml")
        keen_config = config['keen']

        # Setup session with cookies
        session = requests.Session()
        cookies = keen_config['cookies']
        for name, value in cookies.items():
            if value and not value.startswith('YOUR_'):
                session.cookies.set(name, value)

        # Headers for API requests
        headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'X-EpcApi-ID': '3a5d5fd4-cb38-f011-bf3f-98f2b31428e6',
            'X-Uid': '',
            'X-Domain-ID': '1',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'Origin': 'https://www.keen.com',
            'Connection': 'keep-alive',
            'Referer': 'https://www.keen.com/app/myaccount/customers',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Priority': 'u=0',
            'TE': 'trailers'
        }

        return session, headers, keen_config['base_url']

    def filter_customers_with_contacts(self):
        """Filter customers who have contact history (potential transcripts)"""

        customers_with_contacts = []

        for customer_id, customer_data in self.customers.items():
            # Check if customer has contact history
            contacts = customer_data.get('contacts', {})
            if contacts and contacts.get('last'):
                last_contact = contacts['last']

                # Look for session/transaction IDs that might have transcripts
                if (last_contact.get('masterTransactionId') or
                    last_contact.get('activityId') or
                    last_contact.get('id')):

                    customers_with_contacts.append({
                        'customer_id': customer_id,
                        'customer_data': customer_data,
                        'last_contact': last_contact
                    })

        return customers_with_contacts

    def extract_all_transcripts(self, session, headers, base_url, customers_with_contacts):
        """Extract transcripts for all customers with contact history"""

        print(f"\n💬 Extracting transcripts for {len(customers_with_contacts)} customers...")

        # Load progress
        progress = self.load_progress()
        start_index = progress.get('last_completed_index', -1) + 1

        if start_index > 0:
            print(f"🔄 Resuming from customer {start_index + 1}")

        for i, customer_info in enumerate(customers_with_contacts[start_index:], start_index):
            customer_id = customer_info['customer_id']
            customer_data = customer_info['customer_data']
            last_contact = customer_info['last_contact']

            username = customer_data.get('userName', 'Unknown')
            print(f"\n👤 Customer {i+1}/{len(customers_with_contacts)}: {username} (ID: {customer_id})")

            try:
                # Try different methods to get transcripts
                transcripts = self.extract_customer_transcripts(session, headers, base_url, customer_info)

                if transcripts:
                    self.transcripts[customer_id] = {
                        'customer_info': customer_data,
                        'transcripts': transcripts,
                        'transcript_count': len(transcripts),
                        'extraction_timestamp': datetime.now().isoformat()
                    }

                    print(f"    ✅ Extracted {len(transcripts)} transcripts")
                else:
                    print(f"    ℹ️  No transcripts found")

                # Save progress
                self.save_progress(i, len(customers_with_contacts))

            except Exception as e:
                error_msg = f"Error extracting transcripts for customer {customer_id}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)

            # Rate limiting
            time.sleep(1)

        print(f"\n📊 Transcript extraction complete!")
        print(f"✅ Extracted transcripts for {len(self.transcripts)} customers")

    def extract_customer_transcripts(self, session, headers, base_url, customer_info):
        """Extract transcripts for a specific customer"""

        customer_data = customer_info['customer_data']
        last_contact = customer_info['last_contact']

        transcripts = []

        # Method 1: Try to get transcripts using transaction/activity IDs
        transaction_id = last_contact.get('masterTransactionId')
        activity_id = last_contact.get('activityId')
        contact_id = last_contact.get('id')

        if transaction_id:
            transcript = self.get_transcript_by_transaction_id(session, headers, base_url, transaction_id)
            if transcript:
                transcripts.append(transcript)

        if activity_id:
            transcript = self.get_transcript_by_activity_id(session, headers, base_url, activity_id)
            if transcript:
                transcripts.append(transcript)

        if contact_id:
            transcript = self.get_transcript_by_contact_id(session, headers, base_url, contact_id)
            if transcript:
                transcripts.append(transcript)

        # Method 2: Try GraphQL queries for customer conversations
        graphql_transcripts = self.get_transcripts_via_graphql(session, headers, base_url, customer_info)
        if graphql_transcripts:
            transcripts.extend(graphql_transcripts)

        # Method 3: Try REST API endpoints
        rest_transcripts = self.get_transcripts_via_rest(session, headers, base_url, customer_info)
        if rest_transcripts:
            transcripts.extend(rest_transcripts)

        # Remove duplicates
        unique_transcripts = self.deduplicate_transcripts(transcripts)

        return unique_transcripts

    def get_transcript_by_transaction_id(self, session, headers, base_url, transaction_id):
        """Get transcript using transaction ID"""

        # Try different transaction-based endpoints
        endpoints = [
            f"/api/transactions/{transaction_id}/transcript",
            f"/api/transactions/{transaction_id}/messages",
            f"/api/transactions/{transaction_id}/conversation",
            f"/api/sessions/{transaction_id}/transcript",
            f"/api/sessions/{transaction_id}/messages",
        ]

        for endpoint in endpoints:
            try:
                url = f"{base_url}{endpoint}"
                response = session.get(url, headers=headers, timeout=15)

                if response.status_code == 200 and response.content:
                    data = response.json()
                    if self.is_valid_transcript_data(data):
                        return {
                            'source': 'transaction_id',
                            'transaction_id': transaction_id,
                            'endpoint': endpoint,
                            'data': data
                        }
            except:
                continue

        return None

    def get_transcript_by_activity_id(self, session, headers, base_url, activity_id):
        """Get transcript using activity ID"""

        endpoints = [
            f"/api/activities/{activity_id}/transcript",
            f"/api/activities/{activity_id}/messages",
            f"/api/activities/{activity_id}/conversation",
        ]

        for endpoint in endpoints:
            try:
                url = f"{base_url}{endpoint}"
                response = session.get(url, headers=headers, timeout=15)

                if response.status_code == 200 and response.content:
                    data = response.json()
                    if self.is_valid_transcript_data(data):
                        return {
                            'source': 'activity_id',
                            'activity_id': activity_id,
                            'endpoint': endpoint,
                            'data': data
                        }
            except:
                continue

        return None

    def get_transcript_by_contact_id(self, session, headers, base_url, contact_id):
        """Get transcript using contact ID"""

        endpoints = [
            f"/api/contacts/{contact_id}/transcript",
            f"/api/contacts/{contact_id}/messages",
            f"/api/contacts/{contact_id}/conversation",
        ]

        for endpoint in endpoints:
            try:
                url = f"{base_url}{endpoint}"
                response = session.get(url, headers=headers, timeout=15)

                if response.status_code == 200 and response.content:
                    data = response.json()
                    if self.is_valid_transcript_data(data):
                        return {
                            'source': 'contact_id',
                            'contact_id': contact_id,
                            'endpoint': endpoint,
                            'data': data
                        }
            except:
                continue

        return None

    def get_transcripts_via_graphql(self, session, headers, base_url, customer_info):
        """Get transcripts using GraphQL queries"""

        customer_id = customer_info['customer_id']

        # GraphQL query for customer conversations
        query = {
            "query": """
                query($customerId: String!) {
                    customer(id: $customerId) {
                        conversations {
                            id
                            date
                            type
                            messages {
                                id
                                content
                                sender
                                timestamp
                                type
                            }
                        }
                        sessions {
                            id
                            date
                            transcript
                            messages {
                                content
                                sender
                                timestamp
                            }
                        }
                    }
                }
            """,
            "variables": {
                "customerId": customer_id
            }
        }

        try:
            graphql_url = f"{base_url}/api/graphqlv0"
            response = session.post(graphql_url, headers=headers, json=query, timeout=30)

            if response.status_code == 200:
                data = response.json()

                if 'data' in data and data['data'] and 'customer' in data['data']:
                    customer_data = data['data']['customer']
                    transcripts = []

                    # Extract conversations
                    conversations = customer_data.get('conversations', [])
                    for conv in conversations:
                        if conv.get('messages'):
                            transcripts.append({
                                'source': 'graphql_conversation',
                                'conversation_id': conv.get('id'),
                                'date': conv.get('date'),
                                'type': conv.get('type'),
                                'messages': conv.get('messages')
                            })

                    # Extract sessions
                    sessions = customer_data.get('sessions', [])
                    for session_data in sessions:
                        if session_data.get('messages') or session_data.get('transcript'):
                            transcripts.append({
                                'source': 'graphql_session',
                                'session_id': session_data.get('id'),
                                'date': session_data.get('date'),
                                'transcript': session_data.get('transcript'),
                                'messages': session_data.get('messages')
                            })

                    return transcripts
        except:
            pass

        return []

    def get_transcripts_via_rest(self, session, headers, base_url, customer_info):
        """Get transcripts using REST API endpoints"""

        customer_id = customer_info['customer_id']

        # Try different REST endpoints for customer transcripts
        endpoints = [
            f"/api/customers/{customer_id}/conversations",
            f"/api/customers/{customer_id}/sessions",
            f"/api/customers/{customer_id}/transcripts",
            f"/api/customers/{customer_id}/messages",
            f"/api/advisors/56392386/customers/{customer_id}/conversations",
            f"/api/advisors/56392386/customers/{customer_id}/sessions",
        ]

        transcripts = []

        for endpoint in endpoints:
            try:
                url = f"{base_url}{endpoint}"
                response = session.get(url, headers=headers, timeout=15)

                if response.status_code == 200 and response.content:
                    data = response.json()

                    if self.is_valid_transcript_data(data):
                        transcripts.append({
                            'source': 'rest_api',
                            'endpoint': endpoint,
                            'data': data
                        })
            except:
                continue

        return transcripts

    def is_valid_transcript_data(self, data):
        """Check if data contains valid transcript information"""

        if not data:
            return False

        # Look for transcript indicators
        transcript_indicators = [
            'messages', 'transcript', 'conversation', 'chat',
            'dialogue', 'content', 'text', 'message'
        ]

        def search_for_transcripts(obj, depth=0):
            if depth > 3:
                return False

            if isinstance(obj, dict):
                for key, value in obj.items():
                    if any(indicator in key.lower() for indicator in transcript_indicators):
                        if isinstance(value, (list, str)) and value:
                            return True
                    elif isinstance(value, (dict, list)):
                        if search_for_transcripts(value, depth + 1):
                            return True
            elif isinstance(obj, list):
                if obj and isinstance(obj[0], dict):
                    # Check if it's a list of messages
                    first_item = obj[0]
                    message_fields = ['content', 'text', 'message', 'sender', 'timestamp']
                    if any(field in first_item for field in message_fields):
                        return True

                for item in obj[:3]:  # Check first 3 items
                    if search_for_transcripts(item, depth + 1):
                        return True

            return False

        return search_for_transcripts(data)

    def deduplicate_transcripts(self, transcripts):
        """Remove duplicate transcripts"""

        if not transcripts:
            return []

        unique_transcripts = []
        seen_signatures = set()

        for transcript in transcripts:
            # Create a signature for deduplication
            signature_parts = []

            if transcript.get('transaction_id'):
                signature_parts.append(f"tx_{transcript['transaction_id']}")
            if transcript.get('activity_id'):
                signature_parts.append(f"act_{transcript['activity_id']}")
            if transcript.get('contact_id'):
                signature_parts.append(f"con_{transcript['contact_id']}")
            if transcript.get('conversation_id'):
                signature_parts.append(f"conv_{transcript['conversation_id']}")
            if transcript.get('session_id'):
                signature_parts.append(f"sess_{transcript['session_id']}")

            # If no specific IDs, use content hash
            if not signature_parts:
                content_str = str(transcript.get('data', ''))[:100]
                signature_parts.append(f"content_{hash(content_str)}")

            signature = "_".join(signature_parts)

            if signature not in seen_signatures:
                seen_signatures.add(signature)
                unique_transcripts.append(transcript)

        return unique_transcripts

    def load_progress(self):
        """Load transcript extraction progress"""

        try:
            if self.progress_file.exists():
                with open(self.progress_file, 'r') as f:
                    return json.load(f)
        except:
            pass

        return {}

    def save_progress(self, current_index, total_customers):
        """Save transcript extraction progress"""

        try:
            progress = {
                'last_completed_index': current_index,
                'completed_customers': current_index + 1,
                'total_customers': total_customers,
                'timestamp': datetime.now().isoformat(),
                'transcripts_extracted': len(self.transcripts)
            }

            with open(self.progress_file, 'w') as f:
                json.dump(progress, f, indent=2)
        except:
            pass

    def save_transcript_data(self):
        """Save all extracted transcript data"""

        print(f"\n💾 Saving transcript data...")

        # Create comprehensive transcript dataset
        transcript_dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'total_customers_processed': len([c for c in self.customers.values() if c.get('contacts', {}).get('last')]),
                'customers_with_transcripts': len(self.transcripts),
                'total_transcripts': sum(len(t['transcripts']) for t in self.transcripts.values()),
                'extraction_method': 'multi_endpoint_transcript_extraction'
            },
            'transcripts': self.transcripts,
            'errors': self.errors
        }

        # Save complete transcript data
        transcript_file = Path("data/chat_transcripts_complete.json")
        with open(transcript_file, 'w') as f:
            json.dump(transcript_dataset, f, indent=2)

        print(f"💾 Transcript data saved to: {transcript_file}")

        # Create LLM training format for transcripts
        self.create_transcript_llm_format(transcript_dataset)

        # Generate transcript summary
        self.generate_transcript_summary(transcript_dataset)

    def create_transcript_llm_format(self, transcript_dataset):
        """Create LLM training format for transcripts"""

        print(f"🤖 Creating LLM training format for transcripts...")

        llm_conversations = []

        for customer_id, customer_transcripts in self.transcripts.items():
            customer_info = customer_transcripts['customer_info']
            transcripts = customer_transcripts['transcripts']

            for i, transcript in enumerate(transcripts):
                # Extract messages from transcript
                messages = self.extract_messages_from_transcript(transcript)

                if len(messages) >= 2:  # Need at least 2 messages for a conversation
                    # Resolve message ordering
                    ordered_messages = self.resolve_transcript_message_order(messages)

                    llm_conversation = {
                        'conversation_id': f"{customer_id}_transcript_{i}",
                        'customer_id': customer_id,
                        'customer_username': customer_info.get('userName'),
                        'source': transcript.get('source'),
                        'metadata': {
                            'date': transcript.get('date'),
                            'type': transcript.get('type'),
                            'message_count': len(ordered_messages),
                            'has_ordering_issues': any(msg.get('potential_order_issue') for msg in ordered_messages)
                        },
                        'messages': ordered_messages
                    }

                    llm_conversations.append(llm_conversation)

        # Save LLM format
        llm_dataset = {
            'format': 'chat_transcript_training',
            'version': '1.0',
            'metadata': {
                'total_conversations': len(llm_conversations),
                'extraction_timestamp': datetime.now().isoformat(),
                'source': 'keen.com_chat_transcripts'
            },
            'conversations': llm_conversations
        }

        llm_file = Path("data/chat_transcripts_llm_format.json")
        with open(llm_file, 'w') as f:
            json.dump(llm_dataset, f, indent=2)

        print(f"🤖 LLM transcript format saved to: {llm_file}")
        print(f"📊 LLM conversations: {len(llm_conversations)}")

    def extract_messages_from_transcript(self, transcript):
        """Extract individual messages from transcript data"""

        messages = []

        # Handle different transcript formats
        if 'messages' in transcript:
            # Direct message list
            raw_messages = transcript['messages']
            if isinstance(raw_messages, list):
                for msg in raw_messages:
                    if isinstance(msg, dict):
                        formatted_msg = self.format_message(msg)
                        if formatted_msg:
                            messages.append(formatted_msg)

        elif 'data' in transcript:
            # Extract from data field
            data = transcript['data']
            extracted_messages = self.extract_messages_from_data(data)
            messages.extend(extracted_messages)

        elif 'transcript' in transcript:
            # Parse transcript text
            transcript_text = transcript['transcript']
            if isinstance(transcript_text, str):
                parsed_messages = self.parse_transcript_text(transcript_text)
                messages.extend(parsed_messages)

        return messages

    def extract_messages_from_data(self, data):
        """Extract messages from various data structures"""

        messages = []

        def search_for_messages(obj, depth=0):
            if depth > 5:
                return

            if isinstance(obj, dict):
                for key, value in obj.items():
                    if 'message' in key.lower() and isinstance(value, list):
                        for msg in value:
                            if isinstance(msg, dict):
                                formatted_msg = self.format_message(msg)
                                if formatted_msg:
                                    messages.append(formatted_msg)
                    elif isinstance(value, (dict, list)):
                        search_for_messages(value, depth + 1)
            elif isinstance(obj, list):
                for item in obj:
                    if isinstance(item, dict):
                        # Check if this looks like a message
                        message_fields = ['content', 'text', 'message', 'sender']
                        if any(field in item for field in message_fields):
                            formatted_msg = self.format_message(item)
                            if formatted_msg:
                                messages.append(formatted_msg)
                        else:
                            search_for_messages(item, depth + 1)

        search_for_messages(data)
        return messages

    def format_message(self, raw_message):
        """Format a raw message into standard format"""

        try:
            # Extract content
            content = (
                raw_message.get('content') or
                raw_message.get('text') or
                raw_message.get('message') or
                raw_message.get('body') or
                str(raw_message)
            )

            if not content or len(content.strip()) < 3:
                return None

            # Extract sender
            sender = (
                raw_message.get('sender') or
                raw_message.get('from') or
                raw_message.get('author') or
                'unknown'
            )

            # Determine sender type
            sender_type = self.determine_sender_type(sender, content)

            # Extract timestamp
            timestamp = (
                raw_message.get('timestamp') or
                raw_message.get('time') or
                raw_message.get('date') or
                raw_message.get('created_at')
            )

            formatted_message = {
                'content': content.strip(),
                'sender': sender,
                'sender_type': sender_type,
                'timestamp': timestamp,
                'raw_data': raw_message
            }

            return formatted_message

        except Exception as e:
            return None

    def determine_sender_type(self, sender, content):
        """Determine if sender is advisor or customer"""

        if isinstance(sender, str):
            sender_lower = sender.lower()
            if 'advisor' in sender_lower or 'psychic' in sender_lower:
                return 'advisor'
            elif 'customer' in sender_lower or 'user' in sender_lower:
                return 'customer'

        # Analyze content patterns
        content_lower = content.lower()

        # Advisor patterns
        advisor_patterns = [
            'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',
            'let me', 'i can help', 'what i\'m getting', 'i\'m seeing',
            'the universe', 'your guides', 'i\'m picking up'
        ]

        # Customer patterns
        customer_patterns = [
            'will i', 'should i', 'when will', 'what about', 'can you tell me',
            'i want to know', 'my question', 'help me understand', 'what do you see'
        ]

        advisor_score = sum(1 for pattern in advisor_patterns if pattern in content_lower)
        customer_score = sum(1 for pattern in customer_patterns if pattern in content_lower)

        if advisor_score > customer_score:
            return 'advisor'
        elif customer_score > advisor_score:
            return 'customer'
        else:
            return 'unknown'

    def parse_transcript_text(self, transcript_text):
        """Parse transcript from plain text"""

        messages = []

        # Split by common delimiters
        lines = transcript_text.split('\n')

        for line in lines:
            line = line.strip()
            if len(line) < 3:
                continue

            # Look for speaker indicators
            if ':' in line:
                parts = line.split(':', 1)
                if len(parts) == 2:
                    speaker = parts[0].strip()
                    content = parts[1].strip()

                    if content:
                        sender_type = self.determine_sender_type(speaker, content)

                        message = {
                            'content': content,
                            'sender': speaker,
                            'sender_type': sender_type,
                            'timestamp': None,
                            'source': 'parsed_text'
                        }

                        messages.append(message)

        return messages

    def resolve_transcript_message_order(self, messages):
        """Resolve message ordering issues in transcripts"""

        # Sort by timestamp if available
        timestamped_messages = [msg for msg in messages if msg.get('timestamp')]
        non_timestamped_messages = [msg for msg in messages if not msg.get('timestamp')]

        if timestamped_messages:
            try:
                # Sort by timestamp
                timestamped_messages.sort(key=lambda x: self.parse_timestamp(x['timestamp']))

                # Add sequence numbers
                for i, msg in enumerate(timestamped_messages):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = True

            except:
                # If sorting fails, use original order
                for i, msg in enumerate(timestamped_messages):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = False

        # Handle non-timestamped messages
        for i, msg in enumerate(non_timestamped_messages):
            msg['sequence'] = len(timestamped_messages) + i + 1
            msg['order_resolved'] = False

        # Combine all messages
        all_messages = timestamped_messages + non_timestamped_messages

        # Analyze conversation flow for ordering issues
        self.analyze_message_flow(all_messages)

        return all_messages

    def parse_timestamp(self, timestamp_str):
        """Parse timestamp for sorting"""

        from datetime import datetime

        if not timestamp_str:
            return datetime.min

        # Try different formats
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%m/%d/%Y %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%m/%d/%Y %H:%M',
            '%Y-%m-%d',
            '%m/%d/%Y',
        ]

        for fmt in formats:
            try:
                return datetime.strptime(str(timestamp_str), fmt)
            except:
                continue

        return datetime.min

    def analyze_message_flow(self, messages):
        """Analyze message flow for potential ordering issues"""

        for i, msg in enumerate(messages):
            content = msg.get('content', '').lower()

            # Classify message types
            if '?' in content:
                msg['message_type'] = 'question'
            elif any(word in content for word in ['yes', 'no', 'i think', 'i see']):
                msg['message_type'] = 'response'
            elif any(word in content for word in ['hello', 'hi', 'good']):
                msg['message_type'] = 'greeting'
            else:
                msg['message_type'] = 'statement'

            # Check for potential ordering issues
            if i > 0:
                prev_msg = messages[i-1]

                # Flag potential issues
                if (msg.get('message_type') == 'response' and
                    prev_msg.get('message_type') == 'response' and
                    msg.get('sender_type') == prev_msg.get('sender_type')):
                    msg['potential_order_issue'] = 'consecutive_responses_same_sender'

                if (msg.get('sender_type') == 'customer' and
                    prev_msg.get('sender_type') == 'customer' and
                    msg.get('message_type') == 'question' and
                    prev_msg.get('message_type') == 'question'):
                    msg['potential_order_issue'] = 'consecutive_customer_questions'

        return messages

    def generate_transcript_summary(self, transcript_dataset):
        """Generate summary report for transcript extraction"""

        metadata = transcript_dataset['metadata']

        report = f"""
CHAT TRANSCRIPT EXTRACTION REPORT
=================================

EXTRACTION SUMMARY:
- Timestamp: {metadata['extraction_timestamp']}
- Customers Processed: {metadata['total_customers_processed']}
- Customers with Transcripts: {metadata['customers_with_transcripts']}
- Total Transcripts: {metadata['total_transcripts']}
- Success Rate: {metadata['customers_with_transcripts']/metadata['total_customers_processed']*100:.1f}%

ERRORS ENCOUNTERED:
{chr(10).join(f"- {error}" for error in transcript_dataset['errors']) if transcript_dataset['errors'] else "- None"}

NEXT STEPS:
1. Correlate chat transcripts with Google Voice recordings
2. Use customer IDs and timestamps for matching
3. Transcribe Google Voice recordings with Whisper
4. Combine chat and voice data for comprehensive dataset
5. Apply message ordering resolution to all conversations

FILES CREATED:
- chat_transcripts_complete.json: Complete transcript data
- chat_transcripts_llm_format.json: LLM-ready format
- transcript_progress.json: Progress tracking
"""

        # Save report
        report_file = Path("data/transcript_extraction_report.txt")
        with open(report_file, 'w') as f:
            f.write(report)

        print(f"\n📋 TRANSCRIPT EXTRACTION COMPLETE!")
        print(f"📊 Extracted transcripts for {metadata['customers_with_transcripts']} customers")
        print(f"💬 Total transcripts: {metadata['total_transcripts']}")
        print(f"📋 Full report saved to: {report_file}")

if __name__ == "__main__":
    extractor = ChatTranscriptExtractor()
    extractor.run_transcript_extraction()
