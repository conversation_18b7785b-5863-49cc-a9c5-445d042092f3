# Whisper Audio Transcription Integration

## Overview

The AstralSubstance Clone now includes **advanced Whisper transcription capabilities** that convert Google Voice recordings into high-quality text transcriptions with speaker diarization, enabling rich multimodal datasets for LLM training.

## 🎯 Key Features

### **Advanced Whisper Integration**
- **Multiple Model Sizes**: Support for tiny, base, small, medium, large Whisper models
- **GPU Acceleration**: Automatic CUDA/MPS detection for faster processing
- **Batch Processing**: Efficient processing of multiple recordings
- **Quality Scoring**: Confidence metrics for transcription quality

### **Speaker Diarization**
- **Customer/Advisor Separation**: Automatic identification of speakers
- **Role Mapping**: Maps speakers to customer/advisor roles
- **Timestamp Alignment**: Precise timing for each speaker segment
- **Conversation Threading**: Maintains conversation flow

### **Audio Preprocessing**
- **Format Conversion**: Handles WebM, OGG, WAV formats from Google Voice
- **Audio Normalization**: Optimizes audio for transcription accuracy
- **Silence Removal**: Removes dead air for better processing
- **Quality Enhancement**: Preprocessing for optimal Whisper performance

## 🔧 Configuration

### **Whisper Model Selection**
```yaml
transcription:
  whisper_model: "base"  # Options: tiny, base, small, medium, large
  
  # Model characteristics:
  # tiny:   39 MB, ~32x realtime, lowest accuracy
  # base:   74 MB, ~16x realtime, good accuracy
  # small:  244 MB, ~6x realtime, better accuracy  
  # medium: 769 MB, ~2x realtime, high accuracy
  # large:  1550 MB, ~1x realtime, highest accuracy
```

### **Processing Settings**
```yaml
transcription:
  batch_size: 5                # Recordings per batch
  enable_diarization: true     # Speaker separation
  min_confidence: 0.6          # Quality threshold
  remove_silence: true         # Audio preprocessing
  normalize_audio: true        # Audio normalization
  use_gpu: true               # GPU acceleration
```

## 🚀 Usage Examples

### **Basic Transcription**
```bash
# Transcribe all untranscribed recordings
python src/main.py transcribe

# Transcribe specific recordings
python src/main.py transcribe --recording-ids 1,2,3,4,5
```

### **Full Pipeline with Transcription**
```bash
# Complete pipeline including transcription
python src/main.py run-pipeline

# Individual steps
python src/main.py scrape-gvoice    # Download recordings
python src/main.py transcribe       # Transcribe audio
python src/main.py correlate        # Match with call logs
python src/main.py build-datasets   # Create LLM datasets
```

### **Check Transcription Status**
```bash
python src/main.py status
# Shows transcription statistics:
# - Total recordings: 25
# - Transcribed: 20 (80%)
# - Average confidence: 0.85
# - Total duration: 2.5 hours
```

## 📊 Output Formats

### **Database Storage**
```sql
-- Enhanced voice_recordings table
CREATE TABLE voice_recordings (
    id INTEGER PRIMARY KEY,
    file_path TEXT,
    transcription TEXT,                    -- Full transcription
    transcription_confidence DECIMAL,      -- Quality score (0-1)
    transcription_method TEXT,             -- 'whisper'
    transcription_details JSON,            -- Detailed segments and speakers
    phone_number TEXT,                     -- Keen.com number filtering
    -- ... other fields
);
```

### **Transcription Details JSON**
```json
{
  "segments": [
    {
      "start": 0.0,
      "end": 3.5,
      "text": "Hello, thank you for calling Keen.",
      "avg_logprob": -0.2
    }
  ],
  "speaker_segments": [
    {
      "start": 0.0,
      "end": 3.5,
      "text": "Hello, thank you for calling Keen.",
      "speaker": "speaker_1",
      "role": "advisor",
      "confidence": 0.85
    },
    {
      "start": 4.0,
      "end": 8.2,
      "text": "Hi, I need help with my relationship.",
      "speaker": "speaker_2", 
      "role": "customer",
      "confidence": 0.92
    }
  ],
  "language": "en",
  "duration": 45.6,
  "word_count": 234
}
```

## 🎯 LLM Dataset Enhancement

### **Enriched Conversation Metadata**
```json
{
  "conversation_id": "keen_chat_12345",
  "messages": [...],
  "metadata": {
    "call_recording_available": true,
    "recording_transcribed": true,
    "transcription_confidence": 0.87,
    "transcription_method": "whisper",
    "transcription_length": 1250,
    "speaker_segments_available": true,
    "speaker_segments_count": 24,
    "recording_duration": 45.6
  }
}
```

### **Multimodal Training Data**
- **Text + Audio**: Chat transcripts with corresponding audio transcriptions
- **Speaker Attribution**: Clear customer/advisor role identification
- **Quality Metrics**: Confidence scores for data filtering
- **Temporal Alignment**: Precise timing for multimodal training

## ⚡ Performance Optimization

### **Hardware Acceleration**
```python
# Automatic device detection
if torch.cuda.is_available():
    device = "cuda"          # NVIDIA GPU
elif torch.backends.mps.is_available():
    device = "mps"           # Apple Silicon
else:
    device = "cpu"           # CPU fallback
```

### **Processing Speed Estimates**
- **GPU (RTX 4090)**: ~10x realtime (base model)
- **Apple M2 Max**: ~5x realtime (base model)  
- **CPU (Intel i7)**: ~2x realtime (base model)

### **Memory Requirements**
- **Base Model**: ~2GB VRAM/RAM
- **Large Model**: ~6GB VRAM/RAM
- **Batch Processing**: +500MB per recording

## 🔍 Quality Assurance

### **Confidence Scoring**
- **High Confidence**: >0.8 (excellent quality)
- **Medium Confidence**: 0.6-0.8 (good quality)
- **Low Confidence**: <0.6 (review recommended)

### **Quality Metrics**
```python
transcription_stats = {
    'total_recordings': 25,
    'transcribed_recordings': 20,
    'transcription_rate': 80.0,
    'average_confidence': 0.85,
    'confidence_distribution': {
        'high_confidence': 15,    # >=0.8
        'medium_confidence': 4,   # 0.6-0.8
        'low_confidence': 1       # <0.6
    }
}
```

### **Error Handling**
- **Audio Format Issues**: Automatic conversion and fallback
- **Model Loading Errors**: Clear error messages and solutions
- **Memory Issues**: Batch size adjustment recommendations
- **GPU Issues**: Automatic CPU fallback

## 🎨 Speaker Diarization

### **Simple Heuristic Method**
```python
# Speaker change detection based on:
# 1. Pause duration (>2 seconds = speaker change)
# 2. Audio characteristics analysis
# 3. Role mapping (first speaker = customer)

speaker_segments = [
    {
        "start": 0.0,
        "end": 3.5,
        "text": "Hello, thank you for calling Keen.",
        "speaker": "speaker_1",
        "role": "advisor"
    },
    {
        "start": 4.0, 
        "end": 8.2,
        "text": "Hi, I need help with my relationship.",
        "speaker": "speaker_2",
        "role": "customer"
    }
]
```

### **Advanced Diarization (Future)**
- **PyAnnote Integration**: Professional speaker diarization
- **Voice Embeddings**: Speaker identification across calls
- **Custom Training**: Keen.com specific speaker models

## 🔧 Troubleshooting

### **Common Issues**

#### **Model Download Failures**
```bash
# Manual model download
python -c "import whisper; whisper.load_model('base')"
```

#### **GPU Memory Issues**
```yaml
# Reduce batch size
transcription:
  batch_size: 1
  whisper_model: "tiny"  # Use smaller model
```

#### **Audio Format Issues**
```bash
# Install additional codecs
pip install ffmpeg-python
```

#### **Low Transcription Quality**
- **Check Audio Quality**: Ensure clear recordings
- **Adjust Model Size**: Use larger model for better accuracy
- **Review Preprocessing**: Disable silence removal if needed

### **Performance Tuning**
```yaml
# For speed (lower quality)
transcription:
  whisper_model: "tiny"
  batch_size: 10
  remove_silence: true

# For quality (slower)
transcription:
  whisper_model: "large"
  batch_size: 1
  remove_silence: false
```

## 📈 Expected Results

### **Transcription Quality**
- **Keen.com Calls**: 85-95% accuracy (clear phone audio)
- **Speaker Identification**: 80-90% accuracy (simple diarization)
- **Processing Speed**: 5-10x realtime (GPU with base model)

### **Dataset Enhancement**
- **Multimodal Training**: Text + audio transcription pairs
- **Rich Metadata**: Confidence scores, speaker roles, timing
- **Quality Filtering**: Confidence-based data selection
- **Conversation Context**: Complete customer-advisor interactions

This Whisper integration transforms raw audio recordings into high-quality transcribed conversations, significantly enhancing the richness and utility of your LLM training datasets!
