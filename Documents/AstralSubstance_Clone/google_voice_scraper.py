#!/usr/bin/env python3
"""
Google Voice scraper to extract call recordings from (*************
"""

import sys
from pathlib import Path
import requests
import json
import time
from datetime import datetime, timedelta
import re
import os

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class GoogleVoiceScraper:
    """Scrape Google Voice recordings for Keen.com calls"""
    
    def __init__(self):
        self.calls = {}
        self.recordings = {}
        self.errors = []
        self.keen_phone = "(*************"
        self.progress_file = Path("data/voice_progress.json")
        
    def run_voice_scrape(self):
        """Run the complete Google Voice scraping process"""
        
        print("📞 GOOGLE VOICE SCRAPER")
        print("=" * 60)
        print(f"📱 Extracting calls from {self.keen_phone}")
        print("🎵 Downloading recordings for transcription")
        print("=" * 60)
        
        # Setup session and headers
        session, headers = self.setup_google_voice_session()
        
        # Get all calls from Keen.com number
        print(f"\n📞 Fetching call list from {self.keen_phone}...")
        self.fetch_all_calls(session, headers)
        
        # Download recordings
        print(f"\n🎵 Downloading call recordings...")
        self.download_all_recordings(session, headers)
        
        # Save results
        self.save_voice_data()
    
    def setup_google_voice_session(self):
        """Setup Google Voice session with authentication"""
        
        config = ConfigLoader.load_config("config/config.yaml")
        google_voice_config = config['google_voice']
        
        # Setup session with Google Voice cookies
        session = requests.Session()
        cookies = google_voice_config['cookies']
        
        for name, value in cookies.items():
            if value and not value.startswith('YOUR_'):
                session.cookies.set(name, value, domain='.google.com')
        
        # Headers for Google Voice requests
        headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json',
            'Origin': 'https://voice.google.com',
            'Connection': 'keep-alive',
            'Referer': 'https://voice.google.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Priority': 'u=0'
        }
        
        return session, headers
    
    def fetch_all_calls(self, session, headers):
        """Fetch all calls from the Keen.com phone number"""
        
        # Google Voice calls API endpoint
        calls_url = "https://voice.google.com/u/0/calls"
        
        # Parameters to filter by phone number and get recent calls
        params = {
            'q': self.keen_phone.replace('(', '').replace(')', '').replace(' ', '').replace('-', ''),
            'limit': 100,  # Get up to 100 calls at a time
            'start': 0
        }
        
        all_calls = []
        page = 0
        
        while True:
            print(f"  📦 Fetching calls page {page + 1}...")
            
            try:
                # Update start parameter for pagination
                params['start'] = page * 100
                
                response = session.get(calls_url, headers=headers, params=params, timeout=30)
                
                if response.status_code == 200:
                    # Google Voice returns data in a specific format
                    data = self.parse_google_voice_response(response.text)
                    
                    if data and 'calls' in data:
                        calls = data['calls']
                        
                        if not calls:
                            print(f"    ℹ️  No more calls found")
                            break
                        
                        print(f"    ✅ Found {len(calls)} calls")
                        
                        # Filter calls from Keen.com number
                        keen_calls = self.filter_keen_calls(calls)
                        all_calls.extend(keen_calls)
                        
                        print(f"    📞 {len(keen_calls)} calls from Keen.com")
                        
                        # If we got fewer than 100 calls, we're done
                        if len(calls) < 100:
                            break
                        
                    else:
                        print(f"    ❌ No call data in response")
                        break
                        
                else:
                    print(f"    ❌ HTTP error: {response.status_code}")
                    break
                    
            except Exception as e:
                print(f"    ❌ Error fetching calls: {e}")
                break
            
            page += 1
            time.sleep(2)  # Rate limiting
        
        print(f"\n📊 Total Keen.com calls found: {len(all_calls)}")
        
        # Process and store calls
        for call in all_calls:
            call_id = call.get('id')
            if call_id:
                self.calls[call_id] = call
    
    def parse_google_voice_response(self, response_text):
        """Parse Google Voice response format"""
        
        try:
            # Google Voice often returns data wrapped in a specific format
            # Look for JSON data in the response
            
            # Try direct JSON parsing first
            if response_text.startswith('{'):
                return json.loads(response_text)
            
            # Look for JSON within the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
            # If it's a JavaScript response, extract the data
            if 'calls' in response_text:
                # Extract calls data using regex
                calls_match = re.search(r'"calls":\s*(\[.*?\])', response_text, re.DOTALL)
                if calls_match:
                    calls_data = json.loads(calls_match.group(1))
                    return {'calls': calls_data}
            
        except Exception as e:
            print(f"    ⚠️  Error parsing response: {e}")
        
        return None
    
    def filter_keen_calls(self, calls):
        """Filter calls to only include those from Keen.com number"""
        
        keen_calls = []
        
        # Normalize the Keen phone number for comparison
        keen_normalized = self.normalize_phone_number(self.keen_phone)
        
        for call in calls:
            # Check various phone number fields
            phone_fields = ['phoneNumber', 'displayNumber', 'number', 'from', 'to']
            
            for field in phone_fields:
                if field in call:
                    call_number = self.normalize_phone_number(str(call[field]))
                    
                    if call_number == keen_normalized:
                        # Add metadata
                        call['keen_call'] = True
                        call['extraction_timestamp'] = datetime.now().isoformat()
                        keen_calls.append(call)
                        break
        
        return keen_calls
    
    def normalize_phone_number(self, phone):
        """Normalize phone number for comparison"""
        
        if not phone:
            return ""
        
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', str(phone))
        
        # Handle US numbers (remove leading 1 if present)
        if len(digits_only) == 11 and digits_only.startswith('1'):
            digits_only = digits_only[1:]
        
        return digits_only
    
    def download_all_recordings(self, session, headers):
        """Download recordings for all calls"""
        
        if not self.calls:
            print("❌ No calls to download recordings for")
            return
        
        # Create recordings directory
        recordings_dir = Path("data/recordings")
        recordings_dir.mkdir(exist_ok=True)
        
        print(f"📁 Saving recordings to: {recordings_dir}")
        
        for call_id, call_data in self.calls.items():
            print(f"\n🎵 Processing call {call_id}...")
            
            try:
                # Get recording URL
                recording_url = self.get_recording_url(session, headers, call_id, call_data)
                
                if recording_url:
                    # Download the recording
                    recording_file = self.download_recording(session, headers, call_id, recording_url, recordings_dir)
                    
                    if recording_file:
                        self.recordings[call_id] = {
                            'call_data': call_data,
                            'recording_file': str(recording_file),
                            'recording_url': recording_url,
                            'download_timestamp': datetime.now().isoformat()
                        }
                        
                        print(f"    ✅ Downloaded: {recording_file.name}")
                    else:
                        print(f"    ❌ Failed to download recording")
                else:
                    print(f"    ℹ️  No recording available")
                    
            except Exception as e:
                error_msg = f"Error processing call {call_id}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)
            
            # Rate limiting
            time.sleep(1)
        
        print(f"\n📊 Downloaded {len(self.recordings)} recordings")
    
    def get_recording_url(self, session, headers, call_id, call_data):
        """Get the recording URL for a specific call"""
        
        # Try different methods to get recording URL
        
        # Method 1: Direct recording URL from call data
        if 'recordingUrl' in call_data:
            return call_data['recordingUrl']
        
        if 'recording' in call_data and 'url' in call_data['recording']:
            return call_data['recording']['url']
        
        # Method 2: Fetch recording details via API
        recording_url = f"https://voice.google.com/u/0/a/cr/{call_id}"
        
        try:
            response = session.get(recording_url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                # Parse response for recording URL
                recording_data = self.parse_google_voice_response(response.text)
                
                if recording_data and 'recordingUrl' in recording_data:
                    return recording_data['recordingUrl']
                
                # Look for recording URL in the response text
                url_match = re.search(r'https://[^"]*\.mp3[^"]*', response.text)
                if url_match:
                    return url_match.group()
                
        except Exception as e:
            print(f"      ⚠️  Error fetching recording details: {e}")
        
        # Method 3: Construct recording URL based on call ID
        # Google Voice recording URLs often follow a pattern
        constructed_url = f"https://voice.google.com/u/0/a/recording/{call_id}.mp3"
        
        # Test if the constructed URL works
        try:
            test_response = session.head(constructed_url, headers=headers, timeout=10)
            if test_response.status_code == 200:
                return constructed_url
        except:
            pass
        
        return None
    
    def download_recording(self, session, headers, call_id, recording_url, recordings_dir):
        """Download a recording file"""
        
        try:
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"keen_call_{call_id}_{timestamp}.mp3"
            file_path = recordings_dir / filename
            
            # Download the recording
            response = session.get(recording_url, headers=headers, timeout=60, stream=True)
            
            if response.status_code == 200:
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                # Verify file was downloaded
                if file_path.exists() and file_path.stat().st_size > 0:
                    return file_path
                else:
                    print(f"      ❌ Downloaded file is empty")
                    if file_path.exists():
                        file_path.unlink()
                    return None
            else:
                print(f"      ❌ HTTP error downloading recording: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"      ❌ Error downloading recording: {e}")
            return None
    
    def save_voice_data(self):
        """Save all Google Voice data"""
        
        print(f"\n💾 Saving Google Voice data...")
        
        # Create comprehensive voice dataset
        voice_dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'keen_phone_number': self.keen_phone,
                'total_calls': len(self.calls),
                'calls_with_recordings': len(self.recordings),
                'extraction_method': 'google_voice_api_scraping'
            },
            'calls': self.calls,
            'recordings': self.recordings,
            'errors': self.errors
        }
        
        # Save complete voice data
        voice_file = Path("data/google_voice_complete.json")
        with open(voice_file, 'w') as f:
            json.dump(voice_dataset, f, indent=2)
        
        print(f"💾 Voice data saved to: {voice_file}")
        
        # Generate voice summary
        self.generate_voice_summary(voice_dataset)
    
    def generate_voice_summary(self, voice_dataset):
        """Generate summary report for Google Voice extraction"""
        
        metadata = voice_dataset['metadata']
        
        report = f"""
GOOGLE VOICE EXTRACTION REPORT
==============================

EXTRACTION SUMMARY:
- Timestamp: {metadata['extraction_timestamp']}
- Keen Phone Number: {metadata['keen_phone_number']}
- Total Calls Found: {metadata['total_calls']}
- Calls with Recordings: {metadata['calls_with_recordings']}
- Recording Success Rate: {metadata['calls_with_recordings']/max(metadata['total_calls'], 1)*100:.1f}%

ERRORS ENCOUNTERED:
{chr(10).join(f"- {error}" for error in voice_dataset['errors']) if voice_dataset['errors'] else "- None"}

NEXT STEPS:
1. Transcribe recordings using Whisper
2. Correlate with Keen.com customer data using timestamps
3. Match call recordings with chat transcripts
4. Create comprehensive conversation dataset
5. Apply message ordering resolution across all data sources

FILES CREATED:
- google_voice_complete.json: Complete call and recording data
- data/recordings/: Directory with downloaded MP3 files
- voice_extraction_report.txt: This report
"""
        
        # Save report
        report_file = Path("data/voice_extraction_report.txt")
        with open(report_file, 'w') as f:
            f.write(report)
        
        print(f"\n📋 GOOGLE VOICE EXTRACTION COMPLETE!")
        print(f"📞 Found {metadata['total_calls']} calls from Keen.com")
        print(f"🎵 Downloaded {metadata['calls_with_recordings']} recordings")
        print(f"📋 Full report saved to: {report_file}")

if __name__ == "__main__":
    scraper = GoogleVoiceScraper()
    scraper.run_voice_scrape()
