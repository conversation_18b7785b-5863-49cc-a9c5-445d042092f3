# AstralSubstance Clone - LLM Dataset Builder

## Project Overview
Comprehensive data extraction and dataset preparation system for building LLM training datasets from:
- Keen.com customer interaction data (chat transcripts, call logs, profiles)
- Google Voice call recordings
- Correlated and structured for LLM fine-tuning

## Architecture
```
├── src/
│   ├── keen_scraper/          # Keen.com API interaction
│   ├── google_voice/          # Google Voice integration
│   ├── data_correlation/      # Data matching and correlation
│   ├── dataset_builder/       # LLM dataset formatting
│   └── utils/                 # Shared utilities
├── data/
│   ├── raw/                   # Raw scraped data
│   ├── processed/             # Cleaned and correlated data
│   └── datasets/              # Final LLM-ready datasets
├── config/                    # Configuration files
├── logs/                      # Application logs
└── tests/                     # Test suite
```

## Key Features
- **No PII Filtering**: Preserves all conversation data exactly as extracted
- **Robust Authentication**: Cookie-based session management for Keen.com
- **Smart Correlation**: Timestamp and metadata-based matching
- **Multiple Output Formats**: JSONL, conversational, instruction-following
- **Production Ready**: Comprehensive error handling and logging

## Quick Start
```bash
# Install dependencies
pip install -r requirements.txt

# Configure authentication
cp config/config.example.yaml config/config.yaml
# Edit config.yaml with your credentials

# Run extraction
python src/main.py --extract-keen --extract-voice --correlate --build-dataset
```
