"""
Setup script for AstralSubstance Clone
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    requirements = requirements_path.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="astral-substance-clone",
    version="1.0.0",
    description="LLM Dataset Builder for Keen.com and Google Voice Data",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="AstralSubstance Team",
    author_email="<EMAIL>",
    url="https://github.com/astralsubstance/astral-substance-clone",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "black>=23.11.0",
            "flake8>=6.1.0",
            "mypy>=1.7.1",
        ],
        "transcription": [
            "openai-whisper>=20231117",
            "torch>=2.0.0",
            "torchaudio>=2.0.0",
        ],
        "browser": [
            "selenium>=4.15.2",
            "playwright>=1.40.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "astral-clone=main:cli",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    keywords="llm dataset machine-learning data-extraction web-scraping",
    project_urls={
        "Bug Reports": "https://github.com/astralsubstance/astral-substance-clone/issues",
        "Source": "https://github.com/astralsubstance/astral-substance-clone",
        "Documentation": "https://github.com/astralsubstance/astral-substance-clone/wiki",
    },
)
