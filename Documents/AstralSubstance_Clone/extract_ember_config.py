#!/usr/bin/env python3
"""
Extract Ember.js configuration from Keen.com pages to find API endpoints
"""

import sys
from pathlib import Path
import json
import urllib.parse

def extract_ember_config():
    """Extract and analyze Ember.js configuration"""
    
    print("🔍 EXTRACTING EMBER.JS CONFIGURATION")
    print("=" * 60)
    
    # Read the customer page content
    page_path = "data/page_content__app_myaccount_customers.html"
    
    try:
        with open(page_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the meta tag with configuration
        import re
        meta_pattern = r'<meta name="ingenio-web/config/environment" content="([^"]*)"'
        match = re.search(meta_pattern, content)
        
        if match:
            encoded_config = match.group(1)
            print(f"✅ Found encoded configuration ({len(encoded_config)} chars)")
            
            # URL decode the configuration
            decoded_config = urllib.parse.unquote(encoded_config)
            print(f"✅ Decoded configuration ({len(decoded_config)} chars)")
            
            # Parse JSON
            config = json.loads(decoded_config)
            print(f"✅ Parsed JSON configuration")
            
            # Analyze the configuration
            analyze_config(config)
            
        else:
            print("❌ No configuration found in meta tag")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def analyze_config(config):
    """Analyze the Ember.js configuration for useful endpoints"""
    
    print(f"\n📊 CONFIGURATION ANALYSIS")
    print("=" * 40)
    
    # Extract key information
    ember_env = config.get('EmberENV', {})
    
    print(f"🏢 Domain: {ember_env.get('domainName', 'Unknown')}")
    print(f"🔗 API Root: {ember_env.get('apiRoot', 'Unknown')}")
    print(f"🌐 Environment: {config.get('environment', 'Unknown')}")
    
    # Extract URLs
    urls = ember_env.get('urls', {})
    if urls:
        print(f"\n🔗 AVAILABLE ENDPOINTS:")
        for key, url in urls.items():
            print(f"  {key}: {url}")
    
    # Look for API-related configuration
    api_root = ember_env.get('apiRoot', '/api')
    clover_api_root = ember_env.get('cloverApiRoot', '')
    log_api_root = ember_env.get('logApiRoot', '/api')
    
    print(f"\n🔌 API CONFIGURATION:")
    print(f"  Main API Root: {api_root}")
    print(f"  Clover API Root: {clover_api_root}")
    print(f"  Log API Root: {log_api_root}")
    
    # Extract other useful information
    print(f"\n💰 PRICING CONFIGURATION:")
    print(f"  Minimum listing price: ${config.get('minimumListingPrice', 'Unknown')}")
    print(f"  Minimum deposit: ${config.get('minimumDepositAmount', 'Unknown')}")
    print(f"  Maximum deposit: ${config.get('maximumDepositAmount', 'Unknown')}")
    
    # Look for authentication info
    should_auth = config.get('shouldAuthenticate', False)
    print(f"\n🔐 AUTHENTICATION:")
    print(f"  Should authenticate: {should_auth}")
    
    # Extract tracking/analytics info
    print(f"\n📈 TRACKING:")
    print(f"  Google Analytics: {config.get('gaProfile', 'Unknown')}")
    print(f"  MixPanel Token: {config.get('mixPanelToken', 'Unknown')}")
    
    # Save the full configuration for reference
    with open('data/ember_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    print(f"\n💾 Full configuration saved to: data/ember_config.json")
    
    # Generate potential API endpoints to test
    generate_api_endpoints(api_root, clover_api_root)

def generate_api_endpoints(api_root, clover_api_root):
    """Generate potential API endpoints to test"""
    
    print(f"\n🎯 POTENTIAL API ENDPOINTS TO TEST:")
    print("=" * 40)
    
    # Common API endpoints for customer management
    customer_endpoints = [
        '/customers',
        '/customers/list',
        '/customers/search',
        '/customers/details',
        '/user/customers',
        '/advisor/customers',
        '/sessions',
        '/sessions/list',
        '/conversations',
        '/conversations/list',
        '/calls',
        '/calls/list',
        '/ratings',
        '/reviews',
        '/feedback',
        '/transactions',
        '/earnings',
    ]
    
    print(f"📊 Main API ({api_root}):")
    for endpoint in customer_endpoints:
        full_endpoint = f"{api_root}{endpoint}"
        print(f"  {full_endpoint}")
    
    if clover_api_root:
        print(f"\n🍀 Clover API ({clover_api_root}):")
        for endpoint in customer_endpoints:
            full_endpoint = f"{clover_api_root}{endpoint}"
            print(f"  {full_endpoint}")
    
    # GraphQL endpoints
    graphql_endpoints = [
        '/graphql',
        '/graphql2',
        '/graphqlv1',
        '/graphqlv2',
        '/graphqlv3',
    ]
    
    print(f"\n🔍 GraphQL Endpoints:")
    for endpoint in graphql_endpoints:
        full_endpoint = f"{api_root}{endpoint}"
        print(f"  {full_endpoint}")
    
    # Save endpoints to file for testing
    all_endpoints = []
    for endpoint in customer_endpoints + graphql_endpoints:
        all_endpoints.append(f"{api_root}{endpoint}")
        if clover_api_root:
            all_endpoints.append(f"{clover_api_root}{endpoint}")
    
    with open('data/potential_endpoints.txt', 'w') as f:
        for endpoint in all_endpoints:
            f.write(f"{endpoint}\n")
    
    print(f"\n💾 All endpoints saved to: data/potential_endpoints.txt")

if __name__ == "__main__":
    extract_ember_config()
