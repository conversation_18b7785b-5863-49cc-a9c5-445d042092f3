#!/usr/bin/env python3
"""
Investigate the discovered customer-lists endpoint to find all customers
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def investigate_customer_lists():
    """Investigate the customer-lists endpoint to find all customers"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    base_url = keen_config['base_url']
    
    print("🔍 INVESTIGATING CUSTOMER LISTS ENDPOINT")
    print("=" * 60)
    
    # Step 1: Analyze the customer-lists endpoint
    print("\n📋 Step 1: Analyzing customer-lists endpoint...")
    customer_lists = analyze_customer_lists_endpoint(session, headers, base_url)
    
    if customer_lists:
        # Step 2: Investigate each customer list
        print("\n👥 Step 2: Investigating individual customer lists...")
        all_customers = investigate_individual_lists(session, headers, base_url, customer_lists)
        
        # Step 3: Look for additional customer endpoints
        print("\n🔍 Step 3: Looking for additional customer endpoints...")
        additional_customers = find_additional_customer_endpoints(session, headers, base_url)
        
        # Step 4: Combine and analyze all customer data
        print("\n📊 Step 4: Combining and analyzing all customer data...")
        final_analysis(all_customers, additional_customers)

def analyze_customer_lists_endpoint(session, headers, base_url):
    """Analyze the customer-lists endpoint"""
    
    endpoint = f"{base_url}/api/advisors/56392386/customer-lists"
    
    try:
        response = session.get(endpoint, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"  ✅ Customer lists endpoint successful!")
            print(f"  📄 Response size: {len(response.content)} bytes")
            
            # Save the raw response
            with open('data/customer_lists_response.json', 'w') as f:
                json.dump(data, f, indent=2)
            print(f"  💾 Raw response saved to: data/customer_lists_response.json")
            
            # Analyze the structure
            print(f"  📊 Response structure:")
            if isinstance(data, dict):
                print(f"    Type: Dictionary with {len(data)} keys")
                print(f"    Keys: {list(data.keys())}")
                
                # Look for customer lists
                for key, value in data.items():
                    if isinstance(value, list):
                        print(f"    📋 {key}: List with {len(value)} items")
                        
                        if value and isinstance(value[0], dict):
                            sample_keys = list(value[0].keys())
                            print(f"      Sample item keys: {sample_keys}")
                            
                            # Show sample customer list
                            for i, item in enumerate(value[:3]):
                                list_name = item.get('name', f'List {i+1}')
                                list_id = item.get('id', 'Unknown ID')
                                customer_count = item.get('customerCount', item.get('count', 'Unknown'))
                                print(f"      📋 {list_name} (ID: {list_id}) - {customer_count} customers")
                    else:
                        print(f"    {key}: {type(value)} = {value}")
            
            return data
            
        else:
            print(f"  ❌ Failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return None

def investigate_individual_lists(session, headers, base_url, customer_lists_data):
    """Investigate individual customer lists to get all customers"""
    
    all_customers = {}
    
    # Extract customer lists from the response
    customer_lists = []
    if isinstance(customer_lists_data, dict):
        for key, value in customer_lists_data.items():
            if isinstance(value, list) and value:
                if isinstance(value[0], dict) and 'id' in value[0]:
                    customer_lists = value
                    break
    
    if not customer_lists:
        print("  ❌ No customer lists found in response")
        return all_customers
    
    print(f"  📋 Found {len(customer_lists)} customer lists to investigate")
    
    for i, customer_list in enumerate(customer_lists):
        list_id = customer_list.get('id')
        list_name = customer_list.get('name', f'List {i+1}')
        
        print(f"\n  📋 Investigating list: {list_name} (ID: {list_id})")
        
        if list_id:
            # Try different endpoints for accessing customer list data
            list_endpoints = [
                f"/api/advisors/56392386/customer-lists/{list_id}",
                f"/api/advisors/56392386/customer-lists/{list_id}/customers",
                f"/api/customer-lists/{list_id}",
                f"/api/customer-lists/{list_id}/customers",
                f"/api/advisors/56392386/customers?listId={list_id}",
                f"/api/customers?listId={list_id}",
            ]
            
            for endpoint in list_endpoints:
                customers = test_customer_list_endpoint(session, headers, base_url, endpoint, list_name)
                if customers:
                    print(f"    🎉 Found {len(customers)} customers in {list_name}!")
                    
                    # Add customers to our collection
                    for customer in customers:
                        customer_id = customer.get('id') or customer.get('customerId')
                        if customer_id:
                            all_customers[customer_id] = customer
                    
                    break  # Found working endpoint for this list
    
    print(f"\n  📊 Total unique customers from all lists: {len(all_customers)}")
    return all_customers

def test_customer_list_endpoint(session, headers, base_url, endpoint, list_name):
    """Test a customer list endpoint"""
    
    url = f"{base_url}{endpoint}"
    
    try:
        # Try different request methods and parameters
        test_requests = [
            {'method': 'GET', 'params': {}},
            {'method': 'GET', 'params': {'limit': 1000}},
            {'method': 'GET', 'params': {'pageSize': 1000, 'page': 1}},
            {'method': 'POST', 'data': {}},
            {'method': 'POST', 'data': {'limit': 1000}},
            {'method': 'POST', 'data': {'pageSize': 1000, 'page': 1}},
        ]
        
        for test_req in test_requests:
            try:
                if test_req['method'] == 'GET':
                    response = session.get(url, headers=headers, params=test_req.get('params'), timeout=15)
                else:
                    response = session.post(url, headers=headers, json=test_req.get('data'), timeout=15)
                
                if response.status_code == 200 and response.content:
                    try:
                        data = response.json()
                        customers = extract_customers_from_response(data)
                        
                        if customers:
                            print(f"    ✅ {test_req['method']} successful: {len(customers)} customers")
                            
                            # Save this successful response
                            filename = f"data/customer_list_{list_name.replace(' ', '_')}_{endpoint.replace('/', '_')}.json"
                            with open(filename, 'w') as f:
                                json.dump(data, f, indent=2)
                            print(f"    💾 Saved to: {filename}")
                            
                            return customers
                            
                    except json.JSONDecodeError:
                        pass
                        
            except Exception:
                pass  # Ignore individual request errors
                
    except Exception as e:
        pass  # Ignore endpoint errors
    
    return []

def extract_customers_from_response(data):
    """Extract customer data from API response"""
    
    customers = []
    
    def search_for_customers(obj, depth=0):
        if depth > 5:
            return
            
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'customer' in key.lower() and isinstance(value, list):
                    for customer in value:
                        if isinstance(customer, dict) and customer.get('id'):
                            customers.append(customer)
                elif isinstance(value, (dict, list)):
                    search_for_customers(value, depth + 1)
        elif isinstance(obj, list):
            # Check if this is a list of customers
            if obj and isinstance(obj[0], dict):
                first_item = obj[0]
                customer_fields = ['id', 'customerId', 'userName', 'email', 'name']
                if any(field in first_item for field in customer_fields):
                    customers.extend(obj)
                else:
                    for item in obj:
                        search_for_customers(item, depth + 1)
    
    search_for_customers(data)
    return customers

def find_additional_customer_endpoints(session, headers, base_url):
    """Look for additional customer endpoints"""
    
    print("  🔍 Testing additional customer endpoint patterns...")
    
    additional_customers = {}
    
    # Additional endpoint patterns to try
    additional_endpoints = [
        "/api/advisors/56392386/customers",
        "/api/advisors/56392386/customers/all",
        "/api/advisors/56392386/all-customers",
        "/api/customers/advisor/56392386",
        "/api/customers/all",
        "/api/users/56392386/customers",
        "/api/advisor-customers",
        "/api/advisor-customers/56392386",
    ]
    
    for endpoint in additional_endpoints:
        print(f"    🧪 Testing: {endpoint}")
        
        url = f"{base_url}{endpoint}"
        
        try:
            # Try with different parameters
            test_params = [
                {},
                {'limit': 5000},
                {'pageSize': 5000, 'page': 1},
                {'all': True},
                {'includeAll': True},
            ]
            
            for params in test_params:
                try:
                    response = session.get(url, headers=headers, params=params, timeout=30)
                    
                    if response.status_code == 200 and response.content:
                        try:
                            data = response.json()
                            customers = extract_customers_from_response(data)
                            
                            if customers:
                                print(f"      🎉 Found {len(customers)} customers!")
                                
                                # Add to our collection
                                for customer in customers:
                                    customer_id = customer.get('id') or customer.get('customerId')
                                    if customer_id:
                                        additional_customers[customer_id] = customer
                                
                                # Save this response
                                filename = f"data/additional_customers_{endpoint.replace('/', '_')}.json"
                                with open(filename, 'w') as f:
                                    json.dump(data, f, indent=2)
                                print(f"      💾 Saved to: {filename}")
                                
                                if len(customers) > 1000:
                                    print(f"      🚀 LARGE CUSTOMER SET FOUND!")
                                    return additional_customers
                                
                                break  # Found working params for this endpoint
                                
                        except json.JSONDecodeError:
                            pass
                            
                except Exception:
                    pass
                    
        except Exception:
            pass
        
        time.sleep(0.5)
    
    print(f"    📊 Additional customers found: {len(additional_customers)}")
    return additional_customers

def final_analysis(all_customers, additional_customers):
    """Final analysis of all discovered customer data"""
    
    # Combine all customer data
    combined_customers = {}
    combined_customers.update(all_customers)
    combined_customers.update(additional_customers)
    
    print(f"📊 FINAL CUSTOMER DISCOVERY ANALYSIS")
    print("=" * 50)
    
    print(f"👥 CUSTOMER COUNTS:")
    print(f"  From customer lists: {len(all_customers)}")
    print(f"  From additional endpoints: {len(additional_customers)}")
    print(f"  Total unique customers: {len(combined_customers)}")
    
    if combined_customers:
        # Analyze data quality
        customers_list = list(combined_customers.values())
        
        with_username = sum(1 for c in customers_list if c.get('userName'))
        with_email = sum(1 for c in customers_list if c.get('email') or c.get('emailAddress'))
        with_id = sum(1 for c in customers_list if c.get('id') or c.get('customerId'))
        
        print(f"\n📊 DATA QUALITY:")
        print(f"  Customers with ID: {with_id}/{len(customers_list)} ({with_id/len(customers_list)*100:.1f}%)")
        print(f"  Customers with username: {with_username}/{len(customers_list)} ({with_username/len(customers_list)*100:.1f}%)")
        print(f"  Customers with email: {with_email}/{len(customers_list)} ({with_email/len(customers_list)*100:.1f}%)")
        
        # Show sample customers
        print(f"\n👤 SAMPLE CUSTOMERS:")
        for i, customer in enumerate(list(customers_list)[:5], 1):
            customer_id = customer.get('id') or customer.get('customerId', 'Unknown ID')
            username = customer.get('userName', 'Unknown')
            email = customer.get('email') or customer.get('emailAddress', 'No email')
            print(f"  {i}. ID: {customer_id}, Username: {username}, Email: {email}")
        
        # Save combined customer data
        output_data = {
            'discovery_method': 'customer_lists_investigation',
            'total_customers': len(combined_customers),
            'from_lists': len(all_customers),
            'from_additional': len(additional_customers),
            'customers': customers_list
        }
        
        with open('data/all_discovered_customers.json', 'w') as f:
            json.dump(output_data, f, indent=2)
        print(f"\n💾 All discovered customers saved to: data/all_discovered_customers.json")
        
        # Assessment
        if len(combined_customers) > 1000:
            print(f"\n🎉 SUCCESS! Found {len(combined_customers)} customers!")
            print(f"✅ This appears to be a significant portion of the customer base!")
        elif len(combined_customers) > 100:
            print(f"\n✅ Good progress! Found {len(combined_customers)} customers!")
            print(f"📋 May need to investigate more endpoints for the full 4,297 customers")
        else:
            print(f"\n⚠️  Limited success: Only found {len(combined_customers)} customers")
            print(f"📋 Need to find additional customer access methods")
    
    print(f"\n🎯 NEXT STEPS:")
    if len(combined_customers) > 500:
        print(f"✅ Proceed with Google Voice correlation using discovered customers")
        print(f"✅ Build initial LLM dataset with high-quality customer data")
        print(f"✅ Continue searching for remaining customers in parallel")
    else:
        print(f"📋 Continue investigating customer access methods")
        print(f"📋 Consider browser automation with more interaction")
        print(f"📋 Look for session-based customer discovery")

if __name__ == "__main__":
    investigate_customer_lists()
