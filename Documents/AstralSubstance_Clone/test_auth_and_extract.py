#!/usr/bin/env python3
"""
Test authentication and extract chat transcripts using your valid cookies
"""

import sys
from pathlib import Path
import json
import requests
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import <PERSON>fi<PERSON><PERSON><PERSON><PERSON>

def test_authentication():
    """Test if your cookies are still valid"""
    
    print("🔐 Testing authentication with your cookies...")
    
    # Load your cookies
    config = ConfigLoader.load_config("config/config.yaml")
    keen_cookies = config['keen']['cookies']
    
    # Create session
    session = requests.Session()
    
    # Set cookies
    for name, value in keen_cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value, domain='.keen.com')
    
    # Set headers
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
    })
    
    # Test authentication
    try:
        # Test 1: Access customer page
        response = session.get('https://www.keen.com/app/#/myaccount/customers', timeout=10)
        print(f"  📊 Customer page status: {response.status_code}")
        
        if response.status_code == 200:
            if 'login' in response.url.lower() or 'signin' in response.url.lower():
                print("  ❌ Redirected to login - cookies may be expired")
                return False, session
            else:
                print("  ✅ Customer page accessible")
        
        # Test 2: Try a specific chat URL
        test_chat_url = "https://www.keen.com/myaccount/transactions/chat-details?id=********"
        response = session.get(test_chat_url, timeout=10)
        print(f"  💬 Chat page status: {response.status_code}")
        
        if response.status_code == 200:
            print("  ✅ Chat pages accessible")
            return True, session
        else:
            print(f"  ⚠️  Chat page returned {response.status_code}")
            return True, session  # Customer page worked, so auth is valid
            
    except Exception as e:
        print(f"  ❌ Error testing authentication: {e}")
        return False, session

def extract_sample_chat(session):
    """Extract a sample chat transcript to test the process"""
    
    print("\n💬 Testing chat transcript extraction...")
    
    # Test with the known chat ID from your example
    test_interactions = [
        {
            "customer_id": "********",
            "customer_username": "User97925248",
            "interaction_id": "********",
            "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********"
        },
        {
            "customer_id": "********", 
            "customer_username": "User50690268",
            "interaction_id": "********",
            "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********"
        }
    ]
    
    extracted_transcripts = []
    
    for interaction in test_interactions:
        print(f"\n  🎯 Testing: {interaction['customer_username']} - Chat {interaction['interaction_id']}")
        
        try:
            response = session.get(interaction['chat_url'], timeout=15)
            print(f"    📊 Response status: {response.status_code}")
            print(f"    📏 Response length: {len(response.text)} characters")
            
            if response.status_code == 200:
                # Look for signs of chat content
                content = response.text.lower()
                
                # Check for chat indicators
                chat_indicators = ['message', 'chat', 'conversation', 'transcript', 'dialogue']
                found_indicators = [indicator for indicator in chat_indicators if indicator in content]
                
                print(f"    🔍 Found indicators: {found_indicators}")
                
                # Try to extract some sample content
                if found_indicators:
                    # Look for JSON data
                    import re
                    json_patterns = [
                        r'"messages":\s*\[(.*?)\]',
                        r'"content":\s*"([^"]+)"',
                        r'"text":\s*"([^"]+)"'
                    ]
                    
                    messages_found = []
                    for pattern in json_patterns:
                        matches = re.findall(pattern, response.text, re.DOTALL)
                        if matches:
                            print(f"    ✅ Found {len(matches)} potential messages with pattern")
                            messages_found.extend(matches[:3])  # Sample first 3
                    
                    if messages_found:
                        transcript = {
                            'interaction_id': interaction['interaction_id'],
                            'customer_id': interaction['customer_id'],
                            'customer_username': interaction['customer_username'],
                            'sample_messages': messages_found,
                            'extraction_timestamp': datetime.now().isoformat()
                        }
                        extracted_transcripts.append(transcript)
                        print(f"    ✅ Successfully extracted sample content")
                    else:
                        print(f"    ℹ️  No structured messages found, but page accessible")
                else:
                    print(f"    ℹ️  No chat indicators found in content")
            else:
                print(f"    ❌ Failed to access chat page")
        
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(1)  # Rate limiting
    
    return extracted_transcripts

def run_full_extraction(session):
    """Run extraction on multiple chat transcripts"""
    
    print("\n🚀 Running full chat transcript extraction...")
    
    # Load the interaction data we prepared
    try:
        interactions_file = Path("data/colab_interactions.json")
        with open(interactions_file, 'r') as f:
            data = json.load(f)
        interactions = data['interactions'][:10]  # First 10 for testing
    except:
        # Fallback to known interactions
        interactions = [
            {
                "customer_id": "********",
                "customer_username": "User97925248", 
                "interaction_id": "********",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********"
            },
            {
                "customer_id": "********",
                "customer_username": "User50690268",
                "interaction_id": "********", 
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********"
            }
        ]
    
    print(f"📋 Extracting {len(interactions)} chat transcripts...")
    
    chat_transcripts = {}
    errors = []
    
    for i, interaction in enumerate(interactions, 1):
        customer_username = interaction['customer_username']
        interaction_id = interaction['interaction_id']
        
        print(f"\n👤 {i}/{len(interactions)}: {customer_username} (Chat {interaction_id})")
        
        try:
            response = session.get(interaction['chat_url'], timeout=15)
            
            if response.status_code == 200:
                # Extract messages using multiple methods
                messages = extract_messages_from_html(response.text)
                
                if messages:
                    customer_id = interaction['customer_id']
                    
                    if customer_id not in chat_transcripts:
                        chat_transcripts[customer_id] = {
                            'customer_info': {
                                'customer_id': customer_id,
                                'username': customer_username
                            },
                            'transcripts': []
                        }
                    
                    transcript = {
                        'interaction_id': interaction_id,
                        'customer_id': customer_id,
                        'customer_username': customer_username,
                        'chat_url': interaction['chat_url'],
                        'messages': messages,
                        'message_count': len(messages),
                        'extraction_timestamp': datetime.now().isoformat()
                    }
                    
                    chat_transcripts[customer_id]['transcripts'].append(transcript)
                    print(f"    ✅ Extracted {len(messages)} messages")
                else:
                    print(f"    ℹ️  No messages extracted")
            else:
                print(f"    ❌ HTTP {response.status_code}")
                
        except Exception as e:
            error_msg = f"Error processing {interaction_id}: {e}"
            print(f"    ❌ {error_msg}")
            errors.append(error_msg)
        
        time.sleep(2)  # Rate limiting
    
    # Save results
    if chat_transcripts:
        dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'authenticated_http_requests',
                'customers_with_transcripts': len(chat_transcripts),
                'total_transcripts': sum(len(c['transcripts']) for c in chat_transcripts.values()),
                'total_messages': sum(
                    sum(len(t.get('messages', [])) for t in c['transcripts']) 
                    for c in chat_transcripts.values()
                )
            },
            'chat_transcripts': chat_transcripts,
            'errors': errors
        }
        
        results_file = Path("data/authenticated_chat_transcripts.json")
        with open(results_file, 'w') as f:
            json.dump(dataset, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")
        print(f"📊 Customers with transcripts: {len(chat_transcripts)}")
        print(f"💬 Total transcripts: {dataset['metadata']['total_transcripts']}")
        print(f"📝 Total messages: {dataset['metadata']['total_messages']}")
        
        # Show sample
        if chat_transcripts:
            print(f"\n📋 SAMPLE RESULTS:")
            for customer_id, data in list(chat_transcripts.items())[:2]:
                username = data['customer_info']['username']
                transcripts = data['transcripts']
                print(f"  • {username} ({customer_id}): {len(transcripts)} transcripts")
                
                if transcripts and transcripts[0].get('messages'):
                    sample_messages = transcripts[0]['messages'][:2]
                    for msg in sample_messages:
                        sender = msg.get('sender_type', 'unknown').title()
                        content = msg.get('content', '')[:60] + "..." if len(msg.get('content', '')) > 60 else msg.get('content', '')
                        print(f"    - {sender}: {content}")
    
    return chat_transcripts

def extract_messages_from_html(html_content):
    """Extract messages from HTML content"""
    
    import re
    messages = []
    
    try:
        # Method 1: Look for JSON message data
        json_patterns = [
            r'"messages":\s*\[(.*?)\]',
            r'"content":\s*"([^"]+)"',
            r'"text":\s*"([^"]+)"',
            r'"message":\s*"([^"]+)"'
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, html_content, re.DOTALL)
            for i, match in enumerate(matches):
                if len(match) > 10:  # Reasonable message length
                    message = {
                        'sequence': i + 1,
                        'content': match.strip(),
                        'sender_type': infer_sender_type(match),
                        'extraction_method': 'json_pattern'
                    }
                    messages.append(message)
        
        # Method 2: Look for HTML message elements
        if not messages:
            html_patterns = [
                r'<div[^>]*class="[^"]*message[^"]*"[^>]*>(.*?)</div>',
                r'<p[^>]*class="[^"]*chat[^"]*"[^>]*>(.*?)</p>',
                r'<span[^>]*class="[^"]*dialogue[^"]*"[^>]*>(.*?)</span>'
            ]
            
            for pattern in html_patterns:
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                for i, match in enumerate(matches):
                    clean_text = re.sub(r'<[^>]+>', '', match).strip()
                    if len(clean_text) > 10:
                        message = {
                            'sequence': i + 1,
                            'content': clean_text,
                            'sender_type': infer_sender_type(clean_text),
                            'extraction_method': 'html_pattern'
                        }
                        messages.append(message)
        
        # Method 3: Look for text blocks
        if not messages:
            text_blocks = re.findall(r'>([^<]{20,})<', html_content)
            for i, block in enumerate(text_blocks):
                clean_text = re.sub(r'\s+', ' ', block).strip()
                if (len(clean_text) > 20 and 
                    not any(skip in clean_text.lower() for skip in ['navigation', 'menu', 'header', 'footer'])):
                    message = {
                        'sequence': i + 1,
                        'content': clean_text,
                        'sender_type': infer_sender_type(clean_text),
                        'extraction_method': 'text_block'
                    }
                    messages.append(message)
        
    except Exception as e:
        print(f"      ❌ Error extracting messages: {e}")
    
    return messages[:20]  # Limit to first 20 messages

def infer_sender_type(text):
    """Infer if message is from advisor or customer"""
    
    text_lower = text.lower()
    
    advisor_patterns = [
        'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',
        'let me', 'i can help', 'what i\'m getting', 'i\'m seeing'
    ]
    
    customer_patterns = [
        'will i', 'should i', 'when will', 'what about', 'can you tell me',
        'i want to know', 'my question', 'help me understand'
    ]
    
    advisor_score = sum(1 for pattern in advisor_patterns if pattern in text_lower)
    customer_score = sum(1 for pattern in customer_patterns if pattern in text_lower)
    
    if advisor_score > customer_score:
        return 'advisor'
    elif customer_score > advisor_score:
        return 'customer'
    else:
        return 'unknown'

def main():
    """Main function"""
    
    print("🔐 AUTHENTICATION TEST & CHAT EXTRACTION")
    print("=" * 60)
    
    # Test authentication
    auth_valid, session = test_authentication()
    
    if not auth_valid:
        print("❌ Authentication failed - cookies may be expired")
        return
    
    print("✅ Authentication successful!")
    
    # Test sample extraction
    sample_transcripts = extract_sample_chat(session)
    
    if sample_transcripts:
        print(f"✅ Sample extraction successful - found {len(sample_transcripts)} transcripts")
        
        # Run full extraction
        full_transcripts = run_full_extraction(session)
        
        if full_transcripts:
            print(f"\n🎉 EXTRACTION COMPLETE!")
            print(f"✅ Successfully extracted chat transcripts using your valid cookies")
        else:
            print(f"\n⚠️  No transcripts extracted in full run")
    else:
        print(f"⚠️  Sample extraction found no transcripts")

if __name__ == "__main__":
    main()
