#!/usr/bin/env python3
"""
Automated Google Colab chat extractor using your existing Google auth cookies
"""

import sys
from pathlib import Path
import json
import requests
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class AutomatedColabExtractor:
    """Run chat extraction using Google Colab with your auth cookies"""
    
    def __init__(self):
        self.config = None
        self.colab_session = None
        self.results = {}
        
    def setup_colab_session(self):
        """Setup Google Colab session using your Google auth cookies"""
        
        print("🔐 Setting up Google Colab session with your auth cookies...")
        
        # Load your Google auth cookies
        self.config = ConfigLoader.load_config("config/config.yaml")
        google_cookies = self.config['google_voice']['cookies']
        keen_cookies = self.config['keen']['cookies']
        
        # Create session with Google auth
        self.colab_session = requests.Session()
        
        # Set Google authentication cookies
        for name, value in google_cookies.items():
            if value and not value.startswith('YOUR_'):
                self.colab_session.cookies.set(name, value, domain='.google.com')
        
        # Test Google authentication
        try:
            response = self.colab_session.get('https://colab.research.google.com/', timeout=10)
            if response.status_code == 200 and 'colab' in response.text.lower():
                print("✅ Google Colab authentication successful")
                return True
            else:
                print("❌ Google Colab authentication failed")
                return False
        except Exception as e:
            print(f"❌ Error accessing Google Colab: {e}")
            return False
    
    def create_colab_notebook(self):
        """Create and execute notebook in Google Colab"""
        
        print("📓 Creating Google Colab notebook for chat extraction...")
        
        # Load your Keen.com cookies
        keen_cookies = self.config['keen']['cookies']
        
        # Load interaction data
        interactions_file = Path("data/colab_interactions.json")
        with open(interactions_file, 'r') as f:
            interactions_data = json.load(f)
        interactions = interactions_data['interactions'][:10]  # Start with first 10
        
        # Create the notebook content with your actual cookies
        notebook_code = self.generate_notebook_code(keen_cookies, interactions)
        
        # Execute the notebook via Colab API
        return self.execute_colab_notebook(notebook_code)
    
    def generate_notebook_code(self, keen_cookies, interactions):
        """Generate the complete notebook code with your auth cookies"""
        
        # Format interactions for the code
        interactions_code = "[\n"
        for interaction in interactions:
            interactions_code += f'''            {{
                "customer_id": "{interaction['customer_id']}",
                "customer_username": "{interaction['customer_username']}", 
                "interaction_id": "{interaction['interaction_id']}",
                "interaction_type": "{interaction['interaction_type']}",
                "chat_url": "{interaction['chat_url']}",
                "priority": "{interaction['priority']}"
            }},\n'''
        interactions_code += "        ]"
        
        # Create complete notebook code with your actual cookies
        notebook_code = f'''
# Google Colab Chat Transcript Extractor
# Automated execution with authentication

# Setup cell
!pip install selenium webdriver-manager requests beautifulsoup4 > /dev/null 2>&1
!apt-get update > /dev/null 2>&1
!apt-get install -y chromium-browser chromium-chromedriver > /dev/null 2>&1
!apt-get install -y xvfb > /dev/null 2>&1
!pip install pyvirtualdisplay > /dev/null 2>&1

import os
os.environ['PATH'] += ':/usr/lib/chromium-browser/'

# Main extraction code
import json
import time
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from pyvirtualdisplay import Display
import re

class ColabChatExtractor:
    def __init__(self):
        self.driver = None
        self.chat_transcripts = {{}}
        self.errors = []
        
        # Your actual Keen.com authentication cookies
        self.keen_cookies = {{
            "KeenUid": "{keen_cookies.get('KeenUid', '')}",
            "KeenTKT": "{keen_cookies.get('KeenTKT', '')}", 
            "KeenUser": "{keen_cookies.get('KeenUser', '')}",
            "SessionId": "{keen_cookies.get('SessionId', '')}"
        }}
        
        # High-priority interaction data
        self.pending_interactions = {interactions_code}
        
    def setup_colab_browser(self):
        print("🔧 Setting up browser in Colab...")
        
        display = Display(visible=0, size=(1920, 1080))
        display.start()
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {{get: () => undefined}})")
        
        print("✅ Browser setup complete")
        return True
    
    def authenticate_keen(self):
        print("🔐 Authenticating with Keen.com...")
        
        try:
            self.driver.get("https://www.keen.com")
            time.sleep(3)
            
            # Set authentication cookies
            for name, value in self.keen_cookies.items():
                if value and len(value) > 10:  # Valid cookie value
                    self.driver.add_cookie({{
                        'name': name,
                        'value': value,
                        'domain': '.keen.com'
                    }})
            
            # Refresh to apply cookies
            self.driver.refresh()
            time.sleep(2)
            
            # Test authentication by navigating to customer page
            self.driver.get("https://www.keen.com/app/#/myaccount/customers")
            time.sleep(3)
            
            # Check if we're authenticated (not redirected to login)
            if "login" not in self.driver.current_url.lower():
                print("✅ Keen.com authentication successful")
                return True
            else:
                print("❌ Keen.com authentication failed - redirected to login")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {{e}}")
            return False
    
    def extract_chat_transcript(self, interaction):
        try:
            chat_url = interaction['chat_url']
            interaction_id = interaction['interaction_id']
            customer_username = interaction['customer_username']
            
            print(f"  💬 Extracting chat {{interaction_id}} for {{customer_username}}...")
            
            # Navigate to chat transcript page
            self.driver.get(chat_url)
            time.sleep(4)
            
            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Extract messages
            messages = self.extract_messages_from_page()
            
            if messages:
                transcript = {{
                    'interaction_id': interaction_id,
                    'customer_id': interaction['customer_id'],
                    'customer_username': customer_username,
                    'chat_url': chat_url,
                    'messages': messages,
                    'message_count': len(messages),
                    'extraction_timestamp': datetime.now().isoformat(),
                    'extraction_method': 'google_colab_automated',
                    'colab_ip': self.get_colab_ip()
                }}
                
                print(f"    ✅ Extracted {{len(messages)}} messages")
                return transcript
            else:
                print(f"    ℹ️  No messages found")
                return None
                
        except Exception as e:
            print(f"    ❌ Error extracting chat {{interaction.get('interaction_id', 'unknown')}}: {{e}}")
            return None
    
    def extract_messages_from_page(self):
        messages = []
        
        try:
            # Multiple strategies to find messages
            message_selectors = [
                '.message', '.chat-message', '.conversation-message',
                '[class*="message"]', '[class*="chat"]', '[class*="transcript"]',
                'p', 'div[class*="text"]', '.dialogue', '.conversation'
            ]
            
            for selector in message_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for i, element in enumerate(elements):
                        text = element.text.strip()
                        if text and len(text) > 3:
                            message = {{
                                'sequence': i + 1,
                                'content': text,
                                'sender_type': self.infer_sender_type(text),
                                'element_class': element.get_attribute('class') or '',
                                'extraction_method': selector
                            }}
                            
                            # Try to get timestamp
                            try:
                                timestamp_elem = element.find_element(By.CSS_SELECTOR, '.timestamp, .time, [data-time]')
                                if timestamp_elem:
                                    message['timestamp'] = timestamp_elem.text or timestamp_elem.get_attribute('data-time')
                            except:
                                pass
                            
                            messages.append(message)
                    
                    if messages:  # Found messages with this selector
                        break
                        
                except:
                    continue
            
            # If no structured messages, try page text extraction
            if not messages:
                page_text = self.driver.find_element(By.TAG_NAME, 'body').text
                lines = [line.strip() for line in page_text.split('\\n') if line.strip()]
                
                for i, line in enumerate(lines):
                    if len(line) > 10 and not any(skip in line.lower() for skip in ['navigation', 'menu', 'header', 'footer']):
                        message = {{
                            'sequence': i + 1,
                            'content': line,
                            'sender_type': self.infer_sender_type(line),
                            'extraction_method': 'page_text'
                        }}
                        messages.append(message)
            
            # Process messages for ordering
            if messages:
                messages = self.process_message_ordering(messages)
            
        except Exception as e:
            print(f"      ❌ Error extracting messages: {{e}}")
        
        return messages
    
    def infer_sender_type(self, text):
        text_lower = text.lower()
        
        # Advisor patterns (psychic/advisor language)
        advisor_patterns = [
            'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',
            'let me', 'i can help', 'what i\\'m getting', 'i\\'m seeing',
            'the universe', 'your guides', 'i\\'m picking up', 'i\\'m feeling',
            'what i\\'m sensing', 'the energy around', 'your aura'
        ]
        
        # Customer patterns (questions and concerns)
        customer_patterns = [
            'will i', 'should i', 'when will', 'what about', 'can you tell me',
            'i want to know', 'my question', 'help me understand', 'what do you see',
            'i\\'m wondering', 'can you help', 'what should i do', 'i need to know'
        ]
        
        advisor_score = sum(1 for pattern in advisor_patterns if pattern in text_lower)
        customer_score = sum(1 for pattern in customer_patterns if pattern in text_lower)
        
        if advisor_score > customer_score:
            return 'advisor'
        elif customer_score > advisor_score:
            return 'customer'
        else:
            return 'unknown'
    
    def process_message_ordering(self, messages):
        # Sort by timestamp if available
        timestamped = [msg for msg in messages if msg.get('timestamp')]
        non_timestamped = [msg for msg in messages if not msg.get('timestamp')]
        
        if timestamped:
            try:
                timestamped.sort(key=lambda x: x.get('timestamp', ''))
                for i, msg in enumerate(timestamped):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = True
            except:
                for i, msg in enumerate(timestamped):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = False
        
        for i, msg in enumerate(non_timestamped):
            msg['sequence'] = len(timestamped) + i + 1
            msg['order_resolved'] = False
        
        return timestamped + non_timestamped
    
    def get_colab_ip(self):
        try:
            response = requests.get('https://httpbin.org/ip', timeout=10)
            if response.status_code == 200:
                return response.json().get('origin', 'Unknown')
        except:
            pass
        return 'Unknown'
    
    def run_extraction(self):
        print("🚀 AUTOMATED GOOGLE COLAB CHAT EXTRACTOR")
        print("=" * 60)
        
        # Get Colab IP
        colab_ip = self.get_colab_ip()
        print(f"🌐 Colab IP: {{colab_ip}}")
        
        # Setup browser
        if not self.setup_colab_browser():
            print("❌ Browser setup failed")
            return
        
        # Authenticate
        if not self.authenticate_keen():
            print("❌ Authentication failed")
            return
        
        print(f"\\n💬 Extracting {{len(self.pending_interactions)}} chat transcripts...")
        
        for i, interaction in enumerate(self.pending_interactions, 1):
            customer_username = interaction['customer_username']
            interaction_id = interaction['interaction_id']
            
            print(f"\\n👤 {{i}}/{{len(self.pending_interactions)}}: {{customer_username}} (Chat {{interaction_id}})")
            
            try:
                transcript = self.extract_chat_transcript(interaction)
                
                if transcript:
                    customer_id = interaction['customer_id']
                    
                    if customer_id not in self.chat_transcripts:
                        self.chat_transcripts[customer_id] = {{
                            'customer_info': {{
                                'customer_id': customer_id,
                                'username': customer_username
                            }},
                            'transcripts': []
                        }}
                    
                    self.chat_transcripts[customer_id]['transcripts'].append(transcript)
                
            except Exception as e:
                error_msg = f"Error processing {{interaction_id}}: {{e}}"
                print(f"    ❌ {{error_msg}}")
                self.errors.append(error_msg)
            
            # Rate limiting
            time.sleep(3)
        
        # Save results
        self.save_results()
        
        # Cleanup
        if self.driver:
            self.driver.quit()
    
    def save_results(self):
        print(f"\\n💾 Saving extraction results...")
        
        dataset = {{
            'metadata': {{
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'google_colab_automated',
                'colab_ip': self.get_colab_ip(),
                'customers_with_transcripts': len(self.chat_transcripts),
                'total_transcripts': sum(len(c['transcripts']) for c in self.chat_transcripts.values()),
                'total_messages': sum(
                    sum(len(t.get('messages', [])) for t in c['transcripts']) 
                    for c in self.chat_transcripts.values()
                ),
                'errors': len(self.errors)
            }},
            'chat_transcripts': self.chat_transcripts,
            'errors': self.errors
        }}
        
        # Save to file
        with open('automated_colab_chat_transcripts.json', 'w') as f:
            json.dump(dataset, f, indent=2)
        
        print(f"✅ Results saved!")
        print(f"📊 Customers with transcripts: {{len(self.chat_transcripts)}}")
        print(f"💬 Total transcripts: {{dataset['metadata']['total_transcripts']}}")
        print(f"📝 Total messages: {{dataset['metadata']['total_messages']}}")
        print(f"❌ Errors: {{len(self.errors)}}")
        
        # Display sample results
        if self.chat_transcripts:
            print(f"\\n📋 SAMPLE EXTRACTED CONVERSATIONS:")
            for i, (customer_id, data) in enumerate(list(self.chat_transcripts.items())[:3], 1):
                transcripts = data['transcripts']
                username = data['customer_info']['username']
                print(f"  {{i}}. Customer: {{username}} ({{customer_id}})")
                print(f"     Transcripts: {{len(transcripts)}}")
                
                if transcripts and transcripts[0].get('messages'):
                    sample_messages = transcripts[0]['messages'][:3]
                    for j, msg in enumerate(sample_messages, 1):
                        sender = msg.get('sender_type', 'unknown').title()
                        content = msg.get('content', '')[:50] + "..." if len(msg.get('content', '')) > 50 else msg.get('content', '')
                        print(f"       {{j}}. {{sender}}: {{content}}")
        
        # Try to download file if in Colab
        try:
            from google.colab import files
            files.download('automated_colab_chat_transcripts.json')
            print("\\n📥 File automatically downloaded!")
        except:
            print("\\nℹ️  File saved in Colab environment")
        
        return dataset

# Execute the extraction
print("🎯 Starting automated chat transcript extraction...")
extractor = ColabChatExtractor()
results = extractor.run_extraction()

print("\\n🎉 AUTOMATED EXTRACTION COMPLETE!")
'''
        
        return notebook_code
    
    def execute_colab_notebook(self, notebook_code):
        """Execute the notebook code in Google Colab"""
        
        print("⚡ Executing chat extraction in Google Colab...")
        
        try:
            # For now, save the code to execute manually
            # In a real implementation, this would use Colab's API
            
            colab_script = Path("data/automated_colab_execution.py")
            with open(colab_script, 'w') as f:
                f.write(notebook_code)
            
            print(f"📝 Colab execution script saved to: {colab_script}")
            print(f"🎯 Ready for execution in Google Colab")
            
            return True
            
        except Exception as e:
            print(f"❌ Error preparing Colab execution: {e}")
            return False
    
    def run_automated_extraction(self):
        """Run the complete automated extraction process"""
        
        print("🤖 AUTOMATED GOOGLE COLAB CHAT EXTRACTION")
        print("=" * 60)
        print("🔐 Using your existing Google authentication cookies")
        print("💬 Extracting high-priority chat transcripts")
        print("🎯 Applying message ordering resolution")
        print("=" * 60)
        
        # Setup Colab session
        if not self.setup_colab_session():
            print("❌ Could not setup Google Colab session")
            return
        
        # Create and execute notebook
        if self.create_colab_notebook():
            print("✅ Google Colab notebook created and ready for execution")
            print(f"📋 Next: Execute the script in Google Colab to extract chat transcripts")
        else:
            print("❌ Failed to create Colab notebook")

if __name__ == "__main__":
    extractor = AutomatedColabExtractor()
    extractor.run_automated_extraction()
