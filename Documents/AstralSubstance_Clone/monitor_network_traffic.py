#!/usr/bin/env python3
"""
Monitor network traffic to find the real customer API endpoints
"""

import sys
from pathlib import Path
import requests
import json
import re
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Confi<PERSON><PERSON><PERSON><PERSON>

def monitor_network_traffic():
    """Monitor network traffic to find real customer endpoints"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    base_url = keen_config['base_url']
    
    print("🔍 MONITORING NETWORK TRAFFIC FOR CUSTOMER ENDPOINTS")
    print("=" * 60)
    
    # Step 1: Analyze the customer page JavaScript for API calls
    print("\n📄 Step 1: Analyzing customer page JavaScript...")
    analyze_customer_page_javascript(session, base_url)
    
    # Step 2: Test common customer API patterns
    print("\n🔍 Step 2: Testing common customer API patterns...")
    test_common_api_patterns(session, base_url)
    
    # Step 3: Try to reverse engineer from the Ember.js app
    print("\n⚙️  Step 3: Reverse engineering Ember.js routes...")
    reverse_engineer_ember_routes(session, base_url)

def analyze_customer_page_javascript(session, base_url):
    """Analyze the customer page JavaScript for API endpoints"""
    
    # Headers for web requests
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    # Load the customer page
    customer_url = f"{base_url}/app/myaccount/customers"
    
    try:
        response = session.get(customer_url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            content = response.text
            print(f"  ✅ Loaded customer page ({len(content)} chars)")
            
            # Look for JavaScript files that might contain API calls
            js_files = re.findall(r'<script[^>]*src=["\']([^"\']*\.js[^"\']*)["\']', content)
            
            print(f"  📄 Found {len(js_files)} JavaScript files:")
            for js_file in js_files[:5]:  # Show first 5
                print(f"    - {js_file}")
            
            # Download and analyze the main JavaScript files
            for js_file in js_files[:3]:  # Analyze first 3 JS files
                analyze_javascript_file(session, base_url, js_file)
                
        else:
            print(f"  ❌ Failed to load customer page: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ Error: {e}")

def analyze_javascript_file(session, base_url, js_file_path):
    """Analyze a JavaScript file for API endpoints"""
    
    # Construct full URL
    if js_file_path.startswith('http'):
        js_url = js_file_path
    elif js_file_path.startswith('/'):
        js_url = f"{base_url}{js_file_path}"
    else:
        js_url = f"{base_url}/{js_file_path}"
    
    print(f"    📄 Analyzing: {js_file_path}")
    
    try:
        response = session.get(js_url, timeout=30)
        
        if response.status_code == 200:
            js_content = response.text
            
            # Look for API endpoints in the JavaScript
            api_patterns = [
                r'["\']([^"\']*api[^"\']*customers[^"\']*)["\']',
                r'["\']([^"\']*customers[^"\']*api[^"\']*)["\']',
                r'["\']([^"\']*\/api\/[^"\']*)["\']',
                r'url\s*:\s*["\']([^"\']*)["\']',
                r'endpoint\s*:\s*["\']([^"\']*)["\']',
                r'fetch\s*\(\s*["\']([^"\']*)["\']',
                r'ajax\s*\(\s*["\']([^"\']*)["\']',
            ]
            
            found_endpoints = set()
            
            for pattern in api_patterns:
                matches = re.findall(pattern, js_content, re.IGNORECASE)
                for match in matches:
                    if 'customer' in match.lower() or 'api' in match.lower():
                        found_endpoints.add(match)
            
            if found_endpoints:
                print(f"      🔗 Found {len(found_endpoints)} potential endpoints:")
                for endpoint in list(found_endpoints)[:5]:  # Show first 5
                    print(f"        - {endpoint}")
                    
                # Test these endpoints
                test_discovered_endpoints(session, base_url, found_endpoints)
            else:
                print(f"      ℹ️  No obvious API endpoints found")
                
        else:
            print(f"      ❌ Failed to load JS file: {response.status_code}")
            
    except Exception as e:
        print(f"      ❌ Error analyzing JS file: {e}")

def test_discovered_endpoints(session, base_url, endpoints):
    """Test discovered endpoints"""
    
    # API headers
    api_headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    for endpoint in list(endpoints)[:3]:  # Test first 3 endpoints
        print(f"      🧪 Testing endpoint: {endpoint}")
        
        # Construct full URL
        if endpoint.startswith('http'):
            url = endpoint
        elif endpoint.startswith('/'):
            url = f"{base_url}{endpoint}"
        else:
            url = f"{base_url}/{endpoint}"
        
        try:
            # Try GET first
            response = session.get(url, headers=api_headers, timeout=15)
            
            if response.status_code == 200:
                print(f"        ✅ GET successful ({len(response.content)} bytes)")
                
                try:
                    data = response.json()
                    if has_customer_data(data):
                        print(f"        🎉 CONTAINS CUSTOMER DATA!")
                        customer_count = count_customers(data)
                        print(f"        👥 Estimated customers: {customer_count}")
                        
                        if customer_count > 1000:
                            print(f"        🚀 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                            save_endpoint_data(endpoint, data)
                            
                except json.JSONDecodeError:
                    print(f"        📄 Non-JSON response")
                    
            elif response.status_code == 405:
                # Try POST
                post_response = session.post(url, headers=api_headers, json={}, timeout=15)
                if post_response.status_code == 200:
                    print(f"        ✅ POST successful ({len(post_response.content)} bytes)")
                    
            else:
                print(f"        ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"        ❌ Error: {e}")

def test_common_api_patterns(session, base_url):
    """Test common API patterns that might exist"""
    
    # API headers
    api_headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    # Common API patterns for customer management systems
    common_patterns = [
        # REST API patterns
        '/api/v1/customers',
        '/api/v2/customers',
        '/api/customers',
        '/api/customers/list',
        '/api/customers/search',
        '/api/customers/all',
        '/api/advisor/customers',
        '/api/advisor/********/customers',
        '/api/users/customers',
        
        # Ember.js API patterns
        '/api/advisor-customers',
        '/api/advisor_customers',
        '/api/customer-lists',
        '/api/customer_lists',
        
        # Legacy patterns
        '/customers/api/list',
        '/customers/list.json',
        '/customers/search.json',
        '/advisor/customers.json',
        
        # AJAX patterns
        '/ajax/customers',
        '/ajax/customers/list',
        '/ajax/advisor/customers',
    ]
    
    for pattern in common_patterns:
        print(f"  🧪 Testing: {pattern}")
        
        url = f"{base_url}{pattern}"
        
        try:
            # Try different request methods and parameters
            test_requests = [
                {'method': 'GET', 'params': {}},
                {'method': 'GET', 'params': {'limit': 100}},
                {'method': 'GET', 'params': {'pageSize': 100, 'page': 1}},
                {'method': 'POST', 'data': {}},
                {'method': 'POST', 'data': {'limit': 100}},
                {'method': 'POST', 'data': {'pageSize': 100, 'page': 1}},
            ]
            
            for test_req in test_requests:
                try:
                    if test_req['method'] == 'GET':
                        response = session.get(url, headers=api_headers, params=test_req.get('params'), timeout=10)
                    else:
                        response = session.post(url, headers=api_headers, json=test_req.get('data'), timeout=10)
                    
                    if response.status_code == 200:
                        print(f"    ✅ {test_req['method']} successful!")
                        
                        try:
                            data = response.json()
                            if has_customer_data(data):
                                print(f"    🎉 FOUND CUSTOMER DATA!")
                                customer_count = count_customers(data)
                                print(f"    👥 Customers: {customer_count}")
                                
                                if customer_count > 1000:
                                    print(f"    🚀 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                                    save_endpoint_data(pattern, data)
                                    return pattern
                                    
                        except json.JSONDecodeError:
                            pass
                            
                    elif response.status_code not in [404, 405, 500]:
                        print(f"    ⚠️  {test_req['method']} returned {response.status_code}")
                        
                except Exception:
                    pass  # Ignore individual request errors
                    
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(0.5)
    
    return None

def reverse_engineer_ember_routes(session, base_url):
    """Try to reverse engineer Ember.js routes"""
    
    print(f"  ⚙️  Ember.js applications typically use these patterns:")
    print(f"    - Model names become API endpoints")
    print(f"    - Routes like /customers become /api/customers")
    print(f"    - Adapters define the API structure")
    
    # Try Ember.js specific patterns
    ember_patterns = [
        '/api/advisor-customers',
        '/api/customer-lists',
        '/api/customer-notes',
        '/api/customer-sessions',
        '/api/advisor-customer-lists',
    ]
    
    # Test these patterns with Ember.js specific headers
    ember_headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': 'application/vnd.api+json',  # JSON API format used by Ember
        'Content-Type': 'application/vnd.api+json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    for pattern in ember_patterns:
        print(f"    🧪 Testing Ember pattern: {pattern}")
        
        url = f"{base_url}{pattern}"
        
        try:
            response = session.get(url, headers=ember_headers, timeout=15)
            
            if response.status_code == 200:
                print(f"      ✅ Success! ({len(response.content)} bytes)")
                
                try:
                    data = response.json()
                    if has_customer_data(data):
                        print(f"      🎉 FOUND CUSTOMER DATA!")
                        customer_count = count_customers(data)
                        print(f"      👥 Customers: {customer_count}")
                        
                        if customer_count > 1000:
                            print(f"      🚀 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                            save_endpoint_data(pattern, data)
                            return pattern
                            
                except json.JSONDecodeError:
                    print(f"      📄 Non-JSON response")
                    
            elif response.status_code not in [404, 405]:
                print(f"      ⚠️  Status: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
        
        time.sleep(0.5)

def has_customer_data(data):
    """Check if response contains customer data"""
    
    def search_for_customers(obj, depth=0):
        if depth > 5:
            return False
            
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'customer' in key.lower() and isinstance(value, (list, dict)) and value:
                    return True
                elif isinstance(value, (dict, list)):
                    if search_for_customers(value, depth + 1):
                        return True
        elif isinstance(obj, list):
            if obj and isinstance(obj[0], dict):
                first_item = obj[0]
                customer_fields = ['id', 'name', 'email', 'username', 'customerId', 'userName']
                if any(field in first_item for field in customer_fields):
                    return True
            
            for item in obj[:3]:
                if search_for_customers(item, depth + 1):
                    return True
                    
        return False
    
    return search_for_customers(data)

def count_customers(data):
    """Count potential customers in the data"""
    
    def count_items(obj, depth=0):
        if depth > 5:
            return 0
            
        count = 0
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'customer' in key.lower() and isinstance(value, list):
                    count += len(value)
                elif isinstance(value, (dict, list)):
                    count += count_items(value, depth + 1)
        elif isinstance(obj, list):
            if obj and isinstance(obj[0], dict):
                first_item = obj[0]
                customer_fields = ['id', 'name', 'email', 'username', 'customerId', 'userName']
                if any(field in first_item for field in customer_fields):
                    count += len(obj)
            
            for item in obj:
                count += count_items(item, depth + 1)
                
        return count
    
    return count_items(data)

def save_endpoint_data(endpoint, data):
    """Save endpoint data for analysis"""
    
    filename = f"data/endpoint_{endpoint.replace('/', '_')}.json"
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)
    print(f"      💾 Data saved to: {filename}")

if __name__ == "__main__":
    monitor_network_traffic()
