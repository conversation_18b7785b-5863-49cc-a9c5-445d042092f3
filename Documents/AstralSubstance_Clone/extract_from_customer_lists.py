#!/usr/bin/env python3
"""
Extract all customers from the discovered customer lists
Focus on "All Customers NEW" (3,861 customers) and other lists
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON>oa<PERSON>

def extract_from_customer_lists():
    """Extract all customers from the discovered customer lists"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    base_url = keen_config['base_url']
    
    print("🎯 EXTRACTING ALL CUSTOMERS FROM CUSTOMER LISTS")
    print("=" * 60)
    
    # Customer lists to extract from (prioritized by size)
    customer_lists = [
        {'id': 5591276, 'name': 'All Customers NEW', 'count': 3861},
        {'id': 5576887, 'name': 'New Customers', 'count': 336},
        {'id': 5576889, 'name': 'Blocked', 'count': 66},
        {'id': 5590133, 'name': 'All customers', 'count': 100},
    ]
    
    all_customers = {}
    
    for customer_list in customer_lists:
        print(f"\n📋 Extracting from: {customer_list['name']} ({customer_list['count']} customers)")
        
        customers = extract_customers_from_list(session, headers, base_url, customer_list)
        
        if customers:
            print(f"  🎉 Successfully extracted {len(customers)} customers!")
            
            # Add to our collection
            for customer in customers:
                customer_id = customer.get('id') or customer.get('customerId')
                if customer_id:
                    all_customers[customer_id] = customer
            
            print(f"  📊 Total unique customers so far: {len(all_customers)}")
        else:
            print(f"  ❌ No customers extracted from {customer_list['name']}")
    
    # Final analysis and save
    print(f"\n📊 FINAL EXTRACTION RESULTS")
    print("=" * 40)
    
    if all_customers:
        analyze_and_save_customers(all_customers)
    else:
        print("❌ No customers were successfully extracted")
        print("📋 Need to investigate alternative access methods")

def extract_customers_from_list(session, headers, base_url, customer_list):
    """Extract customers from a specific customer list"""
    
    list_id = customer_list['id']
    list_name = customer_list['name']
    expected_count = customer_list['count']
    
    # Try multiple endpoint patterns for accessing customer list data
    endpoint_patterns = [
        # Direct customer list endpoints
        f"/api/advisors/56392386/customer-lists/{list_id}/customers",
        f"/api/customer-lists/{list_id}/customers",
        f"/api/customer-lists/{list_id}",
        
        # Query parameter approaches
        f"/api/advisors/56392386/customers?listId={list_id}",
        f"/api/customers?listId={list_id}",
        f"/api/customers?customerListId={list_id}",
        
        # Alternative patterns
        f"/api/advisors/56392386/customer-lists/{list_id}/members",
        f"/api/customer-lists/{list_id}/members",
        f"/api/lists/{list_id}/customers",
        f"/api/lists/{list_id}/members",
    ]
    
    for endpoint in endpoint_patterns:
        print(f"    🧪 Testing: {endpoint}")
        
        customers = test_customer_list_endpoint_comprehensive(session, headers, base_url, endpoint, expected_count)
        
        if customers:
            print(f"    ✅ SUCCESS! Found {len(customers)} customers")
            
            # Save this successful extraction
            save_customer_list_data(customers, list_name, endpoint)
            
            return customers
    
    return []

def test_customer_list_endpoint_comprehensive(session, headers, base_url, endpoint, expected_count):
    """Comprehensively test a customer list endpoint"""
    
    url = f"{base_url}{endpoint}"
    
    # Try different request methods and parameters
    test_configurations = [
        # GET requests with various parameters
        {'method': 'GET', 'params': {}},
        {'method': 'GET', 'params': {'limit': expected_count}},
        {'method': 'GET', 'params': {'pageSize': expected_count}},
        {'method': 'GET', 'params': {'count': expected_count}},
        {'method': 'GET', 'params': {'limit': 5000}},  # Try higher than expected
        {'method': 'GET', 'params': {'pageSize': 5000}},
        {'method': 'GET', 'params': {'all': True}},
        {'method': 'GET', 'params': {'includeAll': True}},
        
        # POST requests with various payloads
        {'method': 'POST', 'data': {}},
        {'method': 'POST', 'data': {'limit': expected_count}},
        {'method': 'POST', 'data': {'pageSize': expected_count}},
        {'method': 'POST', 'data': {'count': expected_count}},
        {'method': 'POST', 'data': {'limit': 5000}},
        {'method': 'POST', 'data': {'pageSize': 5000}},
        {'method': 'POST', 'data': {'all': True}},
        {'method': 'POST', 'data': {'includeAll': True}},
        
        # Pagination attempts
        {'method': 'GET', 'params': {'page': 1, 'limit': 1000}},
        {'method': 'GET', 'params': {'pageNumber': 1, 'pageSize': 1000}},
        {'method': 'GET', 'params': {'offset': 0, 'limit': 1000}},
        {'method': 'POST', 'data': {'page': 1, 'limit': 1000}},
        {'method': 'POST', 'data': {'pageNumber': 1, 'pageSize': 1000}},
        {'method': 'POST', 'data': {'offset': 0, 'limit': 1000}},
    ]
    
    for config in test_configurations:
        try:
            if config['method'] == 'GET':
                response = session.get(url, headers=headers, params=config.get('params', {}), timeout=30)
            else:
                response = session.post(url, headers=headers, json=config.get('data', {}), timeout=30)
            
            if response.status_code == 200 and response.content:
                try:
                    data = response.json()
                    customers = extract_customers_from_response(data)
                    
                    if customers:
                        print(f"      ✅ {config['method']} with {config.get('params') or config.get('data')}: {len(customers)} customers")
                        
                        # If we got a significant number of customers, this is likely correct
                        if len(customers) >= min(expected_count * 0.8, 50):  # At least 80% or 50 customers
                            return customers
                        elif len(customers) > 0:
                            print(f"      ⚠️  Got {len(customers)} customers, expected ~{expected_count}")
                            # Continue trying other configurations
                            
                except json.JSONDecodeError:
                    pass
                    
            elif response.status_code == 200:
                print(f"      ℹ️  {config['method']} returned empty response")
            elif response.status_code not in [404, 405, 500]:
                print(f"      ⚠️  {config['method']} returned {response.status_code}")
                
        except Exception as e:
            pass  # Ignore individual request errors
        
        time.sleep(0.5)  # Rate limiting
    
    return []

def extract_customers_from_response(data):
    """Extract customer data from API response"""
    
    customers = []
    
    def search_for_customers(obj, depth=0):
        if depth > 5:
            return
            
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'customer' in key.lower() and isinstance(value, list):
                    for customer in value:
                        if isinstance(customer, dict) and (customer.get('id') or customer.get('customerId')):
                            customers.append(customer)
                elif key.lower() in ['data', 'results', 'items', 'members'] and isinstance(value, list):
                    # Check if this is a list of customers
                    if value and isinstance(value[0], dict):
                        first_item = value[0]
                        customer_fields = ['id', 'customerId', 'userName', 'email', 'name', 'nickname']
                        if any(field in first_item for field in customer_fields):
                            customers.extend(value)
                        else:
                            search_for_customers(value, depth + 1)
                elif isinstance(value, (dict, list)):
                    search_for_customers(value, depth + 1)
        elif isinstance(obj, list):
            # Check if this is a direct list of customers
            if obj and isinstance(obj[0], dict):
                first_item = obj[0]
                customer_fields = ['id', 'customerId', 'userName', 'email', 'name', 'nickname']
                if any(field in first_item for field in customer_fields):
                    customers.extend(obj)
                else:
                    for item in obj:
                        search_for_customers(item, depth + 1)
    
    search_for_customers(data)
    
    # Remove duplicates based on ID
    unique_customers = {}
    for customer in customers:
        customer_id = customer.get('id') or customer.get('customerId')
        if customer_id:
            unique_customers[customer_id] = customer
    
    return list(unique_customers.values())

def save_customer_list_data(customers, list_name, endpoint):
    """Save customer list data"""
    
    filename = f"data/customers_from_{list_name.replace(' ', '_').replace('/', '_')}.json"
    
    customer_data = {
        'list_name': list_name,
        'endpoint': endpoint,
        'customer_count': len(customers),
        'extraction_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'customers': customers
    }
    
    with open(filename, 'w') as f:
        json.dump(customer_data, f, indent=2)
    
    print(f"      💾 Saved to: {filename}")

def analyze_and_save_customers(all_customers):
    """Analyze and save all extracted customers"""
    
    customers_list = list(all_customers.values())
    
    print(f"👥 TOTAL CUSTOMERS EXTRACTED: {len(customers_list)}")
    
    # Analyze data quality
    with_id = sum(1 for c in customers_list if c.get('id') or c.get('customerId'))
    with_username = sum(1 for c in customers_list if c.get('userName'))
    with_email = sum(1 for c in customers_list if c.get('email') or c.get('emailAddress'))
    with_nickname = sum(1 for c in customers_list if c.get('nickname'))
    
    print(f"\n📊 DATA QUALITY:")
    print(f"  Customers with ID: {with_id}/{len(customers_list)} ({with_id/len(customers_list)*100:.1f}%)")
    print(f"  Customers with username: {with_username}/{len(customers_list)} ({with_username/len(customers_list)*100:.1f}%)")
    print(f"  Customers with email: {with_email}/{len(customers_list)} ({with_email/len(customers_list)*100:.1f}%)")
    print(f"  Customers with nickname: {with_nickname}/{len(customers_list)} ({with_nickname/len(customers_list)*100:.1f}%)")
    
    # Show sample customers
    print(f"\n👤 SAMPLE CUSTOMERS:")
    for i, customer in enumerate(customers_list[:5], 1):
        customer_id = customer.get('id') or customer.get('customerId', 'Unknown ID')
        username = customer.get('userName', 'Unknown')
        email = customer.get('email') or customer.get('emailAddress', 'No email')
        print(f"  {i}. ID: {customer_id}, Username: {username}, Email: {email}")
    
    # Save all customer data
    output_data = {
        'extraction_method': 'customer_lists',
        'total_customers': len(customers_list),
        'extraction_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'data_quality': {
            'with_id': with_id,
            'with_username': with_username,
            'with_email': with_email,
            'with_nickname': with_nickname
        },
        'customers': customers_list
    }
    
    with open('data/all_customers_from_lists.json', 'w') as f:
        json.dump(output_data, f, indent=2)
    print(f"\n💾 All customers saved to: data/all_customers_from_lists.json")
    
    # Assessment
    if len(customers_list) > 3000:
        print(f"\n🎉 EXCELLENT SUCCESS!")
        print(f"✅ Extracted {len(customers_list)} customers - this is likely the complete dataset!")
        print(f"🚀 Ready to proceed with Google Voice correlation and transcription!")
    elif len(customers_list) > 1000:
        print(f"\n✅ GOOD SUCCESS!")
        print(f"✅ Extracted {len(customers_list)} customers - significant portion of the dataset!")
        print(f"📋 May want to investigate remaining customer lists")
    elif len(customers_list) > 100:
        print(f"\n⚠️  PARTIAL SUCCESS")
        print(f"✅ Extracted {len(customers_list)} customers")
        print(f"📋 Need to find access to the larger customer lists")
    else:
        print(f"\n❌ LIMITED SUCCESS")
        print(f"⚠️  Only extracted {len(customers_list)} customers")
        print(f"📋 Need alternative approach to access customer list data")

if __name__ == "__main__":
    extract_from_customer_lists()
