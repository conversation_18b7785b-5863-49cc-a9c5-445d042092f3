#!/usr/bin/env python3
"""
Test the successful endpoints to see if they contain customer data
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import <PERSON>fi<PERSON><PERSON><PERSON><PERSON>

def test_successful_endpoints():
    """Test the endpoints that returned successful responses"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # API headers
    api_headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    base_url = keen_config['base_url']
    
    print("🔍 TESTING SUCCESSFUL ENDPOINTS FOR CUSTOMER DATA")
    print("=" * 60)
    
    # Endpoints that returned successful responses
    successful_endpoints = [
        '/customers/api/list',
        '/customers/list.json',
        '/customers/search.json',
        '/advisor/customers.json',
        '/ajax/customers',
        '/ajax/customers/list',
        '/ajax/advisor/customers',
    ]
    
    for endpoint in successful_endpoints:
        print(f"\n📊 Testing endpoint: {endpoint}")
        test_endpoint_thoroughly(session, api_headers, base_url, endpoint)

def test_endpoint_thoroughly(session, headers, base_url, endpoint):
    """Test an endpoint thoroughly with different parameters"""
    
    url = f"{base_url}{endpoint}"
    
    # Different parameter combinations to try
    test_payloads = [
        {},  # Empty
        {'page': 1, 'limit': 100},
        {'page': 1, 'pageSize': 100},
        {'pageNumber': 1, 'pageSize': 100},
        {'offset': 0, 'limit': 100},
        {'start': 0, 'count': 100},
        {'first': 100},
        {'limit': 1000},
        {'pageSize': 1000},
        {'count': 1000},
        {'all': True},
        {'includeAll': True},
        {'search': ''},
        {'query': ''},
        {'filter': ''},
    ]
    
    for i, payload in enumerate(test_payloads):
        print(f"  Test {i+1}: {payload}")
        
        try:
            response = session.post(url, headers=headers, json=payload, timeout=30)
            
            print(f"    Status: {response.status_code}")
            print(f"    Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            print(f"    Content-Length: {len(response.content)}")
            
            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '').lower()
                
                if 'json' in content_type:
                    try:
                        data = response.json()
                        print(f"    ✅ JSON response!")
                        analyze_response_data(data, payload, endpoint, i+1)
                        
                        # Check if this contains customer data
                        if is_customer_data(data):
                            customer_count = count_customers_in_data(data)
                            print(f"    🎉 FOUND {customer_count} CUSTOMERS!")
                            
                            if customer_count > 100:
                                print(f"    🚀 SIGNIFICANT CUSTOMER DATA FOUND!")
                                save_customer_response(endpoint, payload, data, i+1)
                                
                                if customer_count > 1000:
                                    print(f"    🎯 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                                    return True
                                    
                    except json.JSONDecodeError:
                        print(f"    ❌ Invalid JSON")
                        print(f"    📄 Response preview: {response.text[:200]}...")
                        
                elif 'xml' in content_type:
                    print(f"    📄 XML response")
                    print(f"    📄 Response preview: {response.text[:200]}...")
                    
                else:
                    print(f"    📄 Text response")
                    print(f"    📄 Response preview: {response.text[:200]}...")
                    
            elif response.status_code == 500:
                print(f"    ⚠️  Server error - might need different parameters")
                print(f"    📄 Error preview: {response.text[:200]}...")
            else:
                print(f"    ❌ Failed")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(1)
    
    return False

def analyze_response_data(data, payload, endpoint, test_num):
    """Analyze the response data structure"""
    
    print(f"    📊 Response analysis:")
    
    if isinstance(data, dict):
        print(f"      Type: Dictionary with {len(data)} keys")
        print(f"      Keys: {list(data.keys())[:10]}")  # Show first 10 keys
        
        # Look for customer-related keys
        customer_keys = [k for k in data.keys() if 'customer' in k.lower()]
        if customer_keys:
            print(f"      👥 Customer-related keys: {customer_keys}")
            
            for key in customer_keys:
                value = data[key]
                if isinstance(value, list):
                    print(f"        {key}: List with {len(value)} items")
                    if value and isinstance(value[0], dict):
                        print(f"          Sample item keys: {list(value[0].keys())[:5]}")
                elif isinstance(value, dict):
                    print(f"        {key}: Dictionary with keys {list(value.keys())[:5]}")
                else:
                    print(f"        {key}: {type(value)} = {value}")
        
        # Look for data arrays
        for key, value in data.items():
            if isinstance(value, list) and len(value) > 10:
                print(f"      📋 Large array '{key}': {len(value)} items")
                if value and isinstance(value[0], dict):
                    sample_keys = list(value[0].keys())
                    print(f"        Sample item keys: {sample_keys[:10]}")
                    
                    # Check if this looks like customer data
                    customer_indicators = ['id', 'name', 'email', 'username', 'customerId', 'userName']
                    if any(indicator in sample_keys for indicator in customer_indicators):
                        print(f"        🎯 THIS LOOKS LIKE CUSTOMER DATA!")
                        
    elif isinstance(data, list):
        print(f"      Type: List with {len(data)} items")
        if data and isinstance(data[0], dict):
            print(f"      Sample item keys: {list(data[0].keys())[:10]}")
            
            # Check if this looks like customer data
            customer_indicators = ['id', 'name', 'email', 'username', 'customerId', 'userName']
            if any(indicator in data[0] for indicator in customer_indicators):
                print(f"      🎯 THIS LOOKS LIKE CUSTOMER DATA!")
    else:
        print(f"      Type: {type(data)} = {data}")

def is_customer_data(data):
    """Check if data contains customer information"""
    
    def search_for_customers(obj, depth=0):
        if depth > 5:
            return False
            
        if isinstance(obj, dict):
            for key, value in obj.items():
                # Look for customer-related keys
                if 'customer' in key.lower() and isinstance(value, (list, dict)) and value:
                    return True
                elif isinstance(value, (dict, list)):
                    if search_for_customers(value, depth + 1):
                        return True
        elif isinstance(obj, list):
            # If it's a list of objects with customer-like fields
            if obj and isinstance(obj[0], dict):
                first_item = obj[0]
                customer_fields = ['id', 'name', 'email', 'username', 'customerId', 'userName', 'userId']
                if any(field in first_item for field in customer_fields):
                    return True
            
            for item in obj[:3]:  # Check first 3 items
                if search_for_customers(item, depth + 1):
                    return True
                    
        return False
    
    return search_for_customers(data)

def count_customers_in_data(data):
    """Count potential customers in the data"""
    
    def count_items(obj, depth=0):
        if depth > 5:
            return 0
            
        count = 0
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'customer' in key.lower() and isinstance(value, list):
                    count += len(value)
                elif isinstance(value, (dict, list)):
                    count += count_items(value, depth + 1)
        elif isinstance(obj, list):
            # Check if this looks like a list of customers
            if obj and isinstance(obj[0], dict):
                first_item = obj[0]
                customer_fields = ['id', 'name', 'email', 'username', 'customerId', 'userName', 'userId']
                if any(field in first_item for field in customer_fields):
                    count += len(obj)
            
            for item in obj:
                count += count_items(item, depth + 1)
                
        return count
    
    return count_items(data)

def save_customer_response(endpoint, payload, data, test_num):
    """Save customer response for analysis"""
    
    filename = f"data/customer_response_{endpoint.replace('/', '_')}_test_{test_num}.json"
    
    response_data = {
        'endpoint': endpoint,
        'payload': payload,
        'customer_count': count_customers_in_data(data),
        'data': data
    }
    
    with open(filename, 'w') as f:
        json.dump(response_data, f, indent=2)
    
    print(f"    💾 Customer response saved to: {filename}")

if __name__ == "__main__":
    test_successful_endpoints()
