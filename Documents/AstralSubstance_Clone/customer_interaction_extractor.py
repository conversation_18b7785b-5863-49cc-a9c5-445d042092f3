#!/usr/bin/env python3
"""
Extract interaction IDs from customer detail pages, then fetch chat transcripts
"""

import sys
from pathlib import Path
import json
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import re

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class CustomerInteractionExtractor:
    """Extract interaction IDs from customer pages, then get chat transcripts"""
    
    def __init__(self):
        self.driver = None
        self.customers = {}
        self.customer_interactions = {}
        self.chat_transcripts = {}
        self.errors = []
        self.progress_file = Path("data/interaction_extraction_progress.json")
        
    def run_interaction_extraction(self):
        """Run the complete interaction extraction process"""
        
        print("🔍 CUSTOMER INTERACTION EXTRACTOR")
        print("=" * 60)
        print("📋 Step 1: Extract interaction IDs from customer detail pages")
        print("💬 Step 2: Fetch chat transcripts using those IDs")
        print("🎯 Target: /myaccount/transactions/chat-details?id={interaction_id}")
        print("=" * 60)
        
        # Setup browser
        if not self.setup_browser():
            return
        
        # Load customer data
        if not self.load_customer_data():
            return
        
        # Step 1: Extract interaction IDs from customer pages
        self.extract_customer_interactions()
        
        # Step 2: Extract chat transcripts using interaction IDs
        self.extract_chat_transcripts_from_interactions()
        
        # Save results
        self.save_interaction_data()
        
        # Cleanup
        self.cleanup()
    
    def setup_browser(self):
        """Setup Chrome browser with authentication"""
        
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Load config and set cookies
            config = ConfigLoader.load_config("config/config.yaml")
            keen_config = config['keen']
            
            # Navigate to Keen.com first
            self.driver.get(keen_config['base_url'])
            time.sleep(3)
            
            # Set authentication cookies
            cookies = keen_config['cookies']
            for name, value in cookies.items():
                if value and not value.startswith('YOUR_'):
                    self.driver.add_cookie({
                        'name': name,
                        'value': value,
                        'domain': '.keen.com'
                    })
            
            print("✅ Browser setup complete with authentication")
            return True
            
        except Exception as e:
            print(f"❌ Error setting up browser: {e}")
            return False
    
    def load_customer_data(self):
        """Load the customer data we extracted"""
        
        customer_file = Path("data/graphql_complete_customers.json")
        
        if not customer_file.exists():
            print(f"❌ Customer data file not found: {customer_file}")
            return False
        
        try:
            with open(customer_file, 'r') as f:
                data = json.load(f)
            
            self.customers = data.get('customers', {})
            print(f"✅ Loaded {len(self.customers)} customers")
            
            # Filter customers with contact history (potential chat customers)
            customers_with_contacts = {
                cid: cdata for cid, cdata in self.customers.items()
                if cdata.get('contacts', {}).get('last')
            }
            
            print(f"📊 {len(customers_with_contacts)} customers have contact history")
            return True
            
        except Exception as e:
            print(f"❌ Error loading customer data: {e}")
            return False
    
    def extract_customer_interactions(self):
        """Extract interaction IDs from customer detail pages"""
        
        print(f"\n📋 STEP 1: Extracting interaction IDs from customer pages...")
        
        # Load progress
        progress = self.load_progress()
        completed_customers = set(progress.get('completed_customers', []))
        
        # Filter customers with contact history
        target_customers = [
            (cid, cdata) for cid, cdata in self.customers.items()
            if cdata.get('contacts', {}).get('last') and cid not in completed_customers
        ]
        
        if completed_customers:
            print(f"🔄 Resuming extraction ({len(completed_customers)} already completed)")
        
        print(f"🎯 Processing {len(target_customers)} customers with contact history")
        
        for i, (customer_id, customer_data) in enumerate(target_customers, 1):
            username = customer_data.get('userName', 'Unknown')
            print(f"\n👤 Customer {i}/{len(target_customers)}: {username} (ID: {customer_id})")
            
            try:
                # Navigate to customer detail page
                customer_url = f"https://www.keen.com/app/#/myaccount/customers/{customer_id}"
                print(f"    🌐 Navigating to: {customer_url}")
                
                self.driver.get(customer_url)
                time.sleep(4)  # Give page time to load
                
                # Extract interaction IDs from this customer's page
                interactions = self.extract_interactions_from_customer_page(customer_id)
                
                if interactions:
                    self.customer_interactions[customer_id] = {
                        'customer_info': customer_data,
                        'interactions': interactions,
                        'interaction_count': len(interactions),
                        'extraction_timestamp': datetime.now().isoformat()
                    }
                    
                    print(f"    ✅ Found {len(interactions)} interactions")
                    
                    # Show sample interactions
                    for j, interaction in enumerate(interactions[:3], 1):
                        print(f"      {j}. ID: {interaction.get('id', 'Unknown')} - Type: {interaction.get('type', 'Unknown')}")
                else:
                    print(f"    ℹ️  No interactions found")
                
                # Save progress
                completed_customers.add(customer_id)
                self.save_progress(list(completed_customers))
                
            except Exception as e:
                error_msg = f"Error extracting interactions for customer {customer_id}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)
            
            # Rate limiting
            time.sleep(2)
        
        print(f"\n📊 Interaction extraction complete!")
        print(f"✅ Found interactions for {len(self.customer_interactions)} customers")
    
    def extract_interactions_from_customer_page(self, customer_id):
        """Extract all interaction IDs from a customer's detail page"""
        
        interactions = []
        
        try:
            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Take a screenshot for debugging
            screenshot_path = Path(f"data/screenshots/customer_{customer_id}_interactions.png")
            screenshot_path.parent.mkdir(exist_ok=True)
            self.driver.save_screenshot(str(screenshot_path))
            
            # Method 1: Look for interaction/transaction tables or lists
            interactions.extend(self.find_interactions_in_tables())
            
            # Method 2: Look for clickable interaction elements
            interactions.extend(self.find_clickable_interactions())
            
            # Method 3: Search page source for interaction IDs
            interactions.extend(self.find_interactions_in_page_source())
            
            # Method 4: Look for specific chat/call history sections
            interactions.extend(self.find_interactions_in_history_sections())
            
            # Remove duplicates
            unique_interactions = self.deduplicate_interactions(interactions)
            
            return unique_interactions
            
        except Exception as e:
            print(f"      ❌ Error extracting interactions: {e}")
            return []
    
    def find_interactions_in_tables(self):
        """Find interactions in table elements"""
        
        interactions = []
        
        try:
            # Look for tables that might contain interaction data
            tables = self.driver.find_elements(By.CSS_SELECTOR, 'table, .table, [class*="table"]')
            
            for table in tables:
                # Look for rows with interaction data
                rows = table.find_elements(By.CSS_SELECTOR, 'tr, .row, [class*="row"]')
                
                for row in rows:
                    # Look for interaction IDs in the row
                    row_text = row.text
                    row_html = row.get_attribute('innerHTML')
                    
                    # Search for numeric IDs that could be interaction IDs
                    id_matches = re.findall(r'\b(\d{7,12})\b', row_text + ' ' + row_html)
                    
                    for interaction_id in id_matches:
                        # Try to determine interaction type
                        interaction_type = self.determine_interaction_type(row_text, row_html)
                        
                        interactions.append({
                            'id': interaction_id,
                            'type': interaction_type,
                            'source': 'table_extraction',
                            'row_text': row_text[:100],  # First 100 chars for context
                            'found_in': 'table'
                        })
        
        except Exception as e:
            print(f"        ⚠️  Error finding table interactions: {e}")
        
        return interactions
    
    def find_clickable_interactions(self):
        """Find clickable interaction elements"""
        
        interactions = []
        
        try:
            # Look for clickable elements that might lead to interactions
            clickable_selectors = [
                'a[href*="chat-details"]',
                'a[href*="transaction"]',
                'a[href*="call"]',
                'button[data-id]',
                '[data-transaction-id]',
                '[data-interaction-id]',
                '.clickable',
                '[onclick*="transaction"]',
                '[onclick*="chat"]'
            ]
            
            for selector in clickable_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        # Extract interaction ID from href or data attributes
                        href = element.get_attribute('href') or ''
                        onclick = element.get_attribute('onclick') or ''
                        data_id = element.get_attribute('data-id') or ''
                        data_transaction = element.get_attribute('data-transaction-id') or ''
                        
                        # Look for IDs in these attributes
                        all_text = f"{href} {onclick} {data_id} {data_transaction}"
                        id_matches = re.findall(r'\b(\d{7,12})\b', all_text)
                        
                        for interaction_id in id_matches:
                            interaction_type = self.determine_interaction_type(element.text, all_text)
                            
                            interactions.append({
                                'id': interaction_id,
                                'type': interaction_type,
                                'source': 'clickable_element',
                                'element_text': element.text[:50],
                                'href': href,
                                'found_in': 'clickable'
                            })
                
                except:
                    continue
        
        except Exception as e:
            print(f"        ⚠️  Error finding clickable interactions: {e}")
        
        return interactions
    
    def find_interactions_in_page_source(self):
        """Search page source for interaction IDs"""
        
        interactions = []
        
        try:
            page_source = self.driver.page_source
            
            # Look for patterns that indicate interaction IDs
            patterns = [
                r'(?:transaction|chat|call|interaction).*?id["\s]*[:=]["\s]*(\d{7,12})',
                r'(?:id|ID)["\s]*[:=]["\s]*["\'](\d{7,12})["\']',
                r'/chat-details\?id=(\d+)',
                r'/transaction.*?(\d{7,12})',
                r'masterTransactionId["\s]*[:=]["\s]*["\']?(\d{7,12})'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                
                for interaction_id in matches:
                    if len(interaction_id) >= 7:  # Reasonable ID length
                        interactions.append({
                            'id': interaction_id,
                            'type': 'unknown',
                            'source': 'page_source_regex',
                            'pattern': pattern,
                            'found_in': 'page_source'
                        })
        
        except Exception as e:
            print(f"        ⚠️  Error searching page source: {e}")
        
        return interactions
    
    def find_interactions_in_history_sections(self):
        """Look for interactions in chat/call history sections"""
        
        interactions = []
        
        try:
            # Look for sections that might contain interaction history
            history_selectors = [
                '[class*="history"]',
                '[class*="chat"]',
                '[class*="conversation"]',
                '[class*="transaction"]',
                '[class*="call"]',
                '.interactions',
                '#chat-history',
                '#call-history'
            ]
            
            for selector in history_selectors:
                try:
                    sections = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for section in sections:
                        section_text = section.text
                        section_html = section.get_attribute('innerHTML')
                        
                        # Look for interaction IDs in this section
                        id_matches = re.findall(r'\b(\d{7,12})\b', section_text + ' ' + section_html)
                        
                        for interaction_id in id_matches:
                            interaction_type = self.determine_interaction_type(section_text, section_html)
                            
                            interactions.append({
                                'id': interaction_id,
                                'type': interaction_type,
                                'source': 'history_section',
                                'section_class': section.get_attribute('class'),
                                'found_in': 'history_section'
                            })
                
                except:
                    continue
        
        except Exception as e:
            print(f"        ⚠️  Error finding history interactions: {e}")
        
        return interactions
    
    def determine_interaction_type(self, text, html):
        """Determine the type of interaction (chat, call, etc.)"""
        
        text_lower = (text + ' ' + html).lower()
        
        if any(word in text_lower for word in ['chat', 'message', 'text']):
            return 'chat'
        elif any(word in text_lower for word in ['call', 'phone', 'voice']):
            return 'call'
        elif any(word in text_lower for word in ['email', 'mail']):
            return 'email'
        else:
            return 'unknown'
    
    def deduplicate_interactions(self, interactions):
        """Remove duplicate interactions"""
        
        unique_interactions = []
        seen_ids = set()
        
        for interaction in interactions:
            interaction_id = interaction['id']
            
            if interaction_id not in seen_ids:
                seen_ids.add(interaction_id)
                unique_interactions.append(interaction)
        
        return unique_interactions
    
    def extract_chat_transcripts_from_interactions(self):
        """Extract chat transcripts using the interaction IDs we found"""
        
        print(f"\n💬 STEP 2: Extracting chat transcripts using interaction IDs...")
        
        total_interactions = sum(len(data['interactions']) for data in self.customer_interactions.values())
        print(f"🎯 Processing {total_interactions} interactions from {len(self.customer_interactions)} customers")
        
        interaction_count = 0
        
        for customer_id, customer_data in self.customer_interactions.items():
            username = customer_data['customer_info'].get('userName', 'Unknown')
            interactions = customer_data['interactions']
            
            print(f"\n👤 Customer: {username} (ID: {customer_id}) - {len(interactions)} interactions")
            
            customer_transcripts = []
            
            for i, interaction in enumerate(interactions, 1):
                interaction_count += 1
                interaction_id = interaction['id']
                interaction_type = interaction['type']
                
                print(f"    💬 Interaction {i}/{len(interactions)} (#{interaction_count}/{total_interactions}): ID {interaction_id} ({interaction_type})")
                
                # Only try to extract chat transcripts for chat interactions
                if interaction_type in ['chat', 'unknown']:  # Include unknown in case we missed some chats
                    transcript = self.extract_chat_transcript(interaction_id, interaction)
                    
                    if transcript:
                        customer_transcripts.append(transcript)
                        print(f"      ✅ Extracted transcript ({len(transcript.get('messages', []))} messages)")
                    else:
                        print(f"      ℹ️  No transcript found")
                else:
                    print(f"      ⏭️  Skipping non-chat interaction")
                
                time.sleep(1)  # Rate limiting
            
            if customer_transcripts:
                self.chat_transcripts[customer_id] = {
                    'customer_info': customer_data['customer_info'],
                    'transcripts': customer_transcripts,
                    'transcript_count': len(customer_transcripts),
                    'extraction_timestamp': datetime.now().isoformat()
                }
        
        print(f"\n📊 Chat transcript extraction complete!")
        print(f"✅ Extracted transcripts for {len(self.chat_transcripts)} customers")
    
    def extract_chat_transcript(self, interaction_id, interaction_info):
        """Extract a single chat transcript"""
        
        try:
            # Navigate to chat transcript page
            chat_url = f"https://www.keen.com/myaccount/transactions/chat-details?id={interaction_id}"
            
            self.driver.get(chat_url)
            time.sleep(3)
            
            # Wait for page to load
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except:
                return None
            
            # Extract messages from the chat page
            messages = self.extract_messages_from_chat_page()
            
            if messages:
                transcript = {
                    'interaction_id': interaction_id,
                    'interaction_info': interaction_info,
                    'chat_url': chat_url,
                    'messages': messages,
                    'message_count': len(messages),
                    'extraction_timestamp': datetime.now().isoformat(),
                    'source': 'chat_details_page'
                }
                
                return transcript
            
        except Exception as e:
            print(f"        ❌ Error extracting transcript for {interaction_id}: {e}")
        
        return None
    
    def extract_messages_from_chat_page(self):
        """Extract messages from a chat transcript page"""
        
        messages = []
        
        try:
            # Look for message containers
            message_selectors = [
                '.message',
                '.chat-message',
                '.conversation-message',
                '[class*="message"]',
                '[class*="chat"]',
                'p',
                'div[class*="text"]',
                '.transcript-line'
            ]
            
            for selector in message_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        text = element.text.strip()
                        if text and len(text) > 3:
                            message = {
                                'content': text,
                                'sender_type': self.infer_sender_from_content(text),
                                'element_class': element.get_attribute('class') or '',
                                'extraction_method': selector
                            }
                            
                            # Try to get timestamp
                            try:
                                timestamp_elem = element.find_element(By.CSS_SELECTOR, '.timestamp, .time, [data-time]')
                                if timestamp_elem:
                                    message['timestamp'] = timestamp_elem.text or timestamp_elem.get_attribute('data-time')
                            except:
                                pass
                            
                            messages.append(message)
                    
                    if messages:  # Found messages with this selector
                        break
                        
                except:
                    continue
            
            # If no structured messages found, try page text extraction
            if not messages:
                page_text = self.driver.find_element(By.TAG_NAME, 'body').text
                lines = [line.strip() for line in page_text.split('\n') if line.strip()]
                
                for i, line in enumerate(lines):
                    if len(line) > 10:
                        message = {
                            'content': line,
                            'sender_type': self.infer_sender_from_content(line),
                            'line_number': i,
                            'extraction_method': 'page_text'
                        }
                        messages.append(message)
            
            # Process messages
            if messages:
                messages = self.process_messages(messages)
            
        except Exception as e:
            print(f"        ❌ Error extracting messages: {e}")
        
        return messages
    
    def infer_sender_from_content(self, text):
        """Infer message sender from content patterns"""
        
        text_lower = text.lower()
        
        # Advisor patterns
        advisor_patterns = [
            'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',
            'let me', 'i can help', 'what i\'m getting', 'i\'m seeing',
            'the universe', 'your guides', 'i\'m picking up'
        ]
        
        # Customer patterns  
        customer_patterns = [
            'will i', 'should i', 'when will', 'what about', 'can you tell me',
            'i want to know', 'my question', 'help me understand'
        ]
        
        advisor_score = sum(1 for pattern in advisor_patterns if pattern in text_lower)
        customer_score = sum(1 for pattern in customer_patterns if pattern in text_lower)
        
        if advisor_score > customer_score:
            return 'advisor'
        elif customer_score > advisor_score:
            return 'customer'
        else:
            return 'unknown'
    
    def process_messages(self, messages):
        """Process and order messages"""
        
        # Add sequence numbers
        for i, msg in enumerate(messages):
            msg['sequence'] = i + 1
        
        # Try to sort by timestamp if available
        timestamped_messages = [msg for msg in messages if msg.get('timestamp')]
        
        if timestamped_messages:
            try:
                timestamped_messages.sort(key=lambda x: self.parse_timestamp(x['timestamp']))
                
                for i, msg in enumerate(timestamped_messages):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = True
                
                non_timestamped = [msg for msg in messages if not msg.get('timestamp')]
                for i, msg in enumerate(non_timestamped):
                    msg['sequence'] = len(timestamped_messages) + i + 1
                    msg['order_resolved'] = False
                
                return timestamped_messages + non_timestamped
                
            except:
                pass
        
        for msg in messages:
            msg['order_resolved'] = False
        
        return messages
    
    def parse_timestamp(self, timestamp_str):
        """Parse timestamp for sorting"""
        
        from datetime import datetime
        
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%m/%d/%Y %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%m/%d/%Y %H:%M',
            '%H:%M:%S',
            '%H:%M'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(str(timestamp_str), fmt)
            except:
                continue
        
        return datetime.min
    
    def load_progress(self):
        """Load extraction progress"""
        
        try:
            if self.progress_file.exists():
                with open(self.progress_file, 'r') as f:
                    return json.load(f)
        except:
            pass
        
        return {}
    
    def save_progress(self, completed_customers):
        """Save extraction progress"""
        
        try:
            progress = {
                'completed_customers': completed_customers,
                'total_completed': len(completed_customers),
                'timestamp': datetime.now().isoformat()
            }
            
            with open(self.progress_file, 'w') as f:
                json.dump(progress, f, indent=2)
        except:
            pass
    
    def save_interaction_data(self):
        """Save all extracted interaction and chat data"""
        
        print(f"\n💾 Saving interaction and chat data...")
        
        # Save customer interactions
        interactions_dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'total_customers_processed': len(self.customer_interactions),
                'total_interactions_found': sum(len(data['interactions']) for data in self.customer_interactions.values()),
                'extraction_method': 'customer_page_interaction_extraction'
            },
            'customer_interactions': self.customer_interactions,
            'errors': self.errors
        }
        
        interactions_file = Path("data/customer_interactions.json")
        with open(interactions_file, 'w') as f:
            json.dump(interactions_dataset, f, indent=2)
        
        print(f"💾 Customer interactions saved to: {interactions_file}")
        
        # Save chat transcripts
        if self.chat_transcripts:
            chat_dataset = {
                'metadata': {
                    'extraction_timestamp': datetime.now().isoformat(),
                    'customers_with_chats': len(self.chat_transcripts),
                    'total_chat_transcripts': sum(len(t['transcripts']) for t in self.chat_transcripts.values()),
                    'extraction_method': 'interaction_id_based_extraction'
                },
                'chat_transcripts': self.chat_transcripts,
                'errors': self.errors
            }
            
            chat_file = Path("data/interaction_based_chat_transcripts.json")
            with open(chat_file, 'w') as f:
                json.dump(chat_dataset, f, indent=2)
            
            print(f"💾 Chat transcripts saved to: {chat_file}")
        
        # Generate summary
        interactions_metadata = interactions_dataset['metadata']
        
        print(f"\n📊 EXTRACTION SUMMARY:")
        print(f"  • Customers processed: {interactions_metadata['total_customers_processed']}")
        print(f"  • Total interactions found: {interactions_metadata['total_interactions_found']}")
        
        if self.chat_transcripts:
            chat_metadata = chat_dataset['metadata']
            print(f"  • Customers with chat transcripts: {chat_metadata['customers_with_chats']}")
            print(f"  • Total chat transcripts: {chat_metadata['total_chat_transcripts']}")
        else:
            print(f"  • No chat transcripts extracted")
    
    def cleanup(self):
        """Clean up resources"""
        
        if self.driver:
            try:
                self.driver.quit()
                print("🔒 Browser closed")
            except:
                pass

if __name__ == "__main__":
    extractor = CustomerInteractionExtractor()
    extractor.run_interaction_extraction()
