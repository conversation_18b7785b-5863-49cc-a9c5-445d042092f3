2025-05-24 18:29:13 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:29:13 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:29:28 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:29:28 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:29:28 | INFO | keen_scraper.auth_manager:_init_encryption:85 | Generated new encryption key: config/.encryption_key
2025-05-24 18:29:28 | ERROR | keen_scraper.auth_manager:_load_cookies:108 | Failed to load cookies: 
2025-05-24 18:29:28 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 18:29:28 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 18:29:39 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:29:39 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:29:39 | ERROR | keen_scraper.auth_manager:_load_cookies:108 | Failed to load cookies: 
2025-05-24 18:29:39 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 18:29:39 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 18:29:47 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:29:47 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:29:47 | ERROR | keen_scraper.auth_manager:_load_cookies:108 | Failed to load cookies: 
2025-05-24 18:29:47 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 18:29:47 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 18:29:55 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:29:55 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:29:55 | ERROR | keen_scraper.auth_manager:_load_cookies:108 | Failed to load cookies: 
2025-05-24 18:29:55 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 18:29:55 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 18:44:30 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:44:30 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:44:30 | ERROR | keen_scraper.auth_manager:_load_cookies:108 | Failed to load cookies: 
2025-05-24 18:44:30 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 18:44:30 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 18:44:30 | INFO | __main__:setup_authentication:107 | Setting up Keen.com authentication
2025-05-24 18:44:30 | WARNING | keen_scraper.auth_manager:validate_session:181 | No cookies available for validation
2025-05-24 18:44:30 | WARNING | __main__:setup_authentication:110 | No valid session found, prompting for cookies
2025-05-24 18:50:43 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:50:43 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:50:43 | ERROR | keen_scraper.auth_manager:_load_cookies:108 | Failed to load cookies: 
2025-05-24 18:50:43 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 18:50:43 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 18:50:43 | INFO | __main__:run_full_pipeline:292 | Starting full pipeline execution
2025-05-24 18:50:43 | INFO | __main__:setup_authentication:107 | Setting up Keen.com authentication
2025-05-24 18:50:43 | WARNING | keen_scraper.auth_manager:validate_session:181 | No cookies available for validation
2025-05-24 18:50:43 | WARNING | __main__:setup_authentication:110 | No valid session found, prompting for cookies
2025-05-24 18:55:17 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:55:17 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:55:17 | ERROR | keen_scraper.auth_manager:_load_cookies:108 | Failed to load cookies: 
2025-05-24 18:55:17 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 18:55:17 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 18:55:27 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:55:27 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:55:27 | ERROR | keen_scraper.auth_manager:_load_cookies:108 | Failed to load cookies: 
2025-05-24 18:55:27 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 18:55:27 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 18:55:27 | INFO | __main__:setup_authentication:107 | Setting up Keen.com authentication
2025-05-24 18:55:27 | WARNING | keen_scraper.auth_manager:validate_session:181 | No cookies available for validation
2025-05-24 18:55:27 | WARNING | __main__:setup_authentication:110 | No valid session found, prompting for cookies
2025-05-24 18:58:04 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:58:04 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:58:04 | INFO | keen_scraper.auth_manager:_load_cookies_from_config_or_storage:94 | Loading cookies from configuration
2025-05-24 18:58:04 | INFO | keen_scraper.auth_manager:_save_cookies:145 | Saved cookies successfully
2025-05-24 18:58:04 | INFO | google_voice.voice_processor:_load_cookies_from_config:64 | Loading Google Voice cookies from configuration
2025-05-24 18:58:04 | INFO | google_voice.voice_processor:update_cookies:72 | Updated Google Voice authentication cookies
2025-05-24 18:58:04 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 18:58:04 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 18:58:14 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:58:14 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:58:34 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 18:58:34 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 18:58:35 | INFO | keen_scraper.auth_manager:_init_encryption:85 | Generated new encryption key: config/.encryption_key
2025-05-24 18:58:35 | INFO | keen_scraper.auth_manager:_load_cookies_from_config_or_storage:94 | Loading cookies from configuration
2025-05-24 18:58:35 | INFO | keen_scraper.auth_manager:_save_cookies:145 | Saved cookies successfully
2025-05-24 18:58:35 | INFO | google_voice.voice_processor:_load_cookies_from_config:64 | Loading Google Voice cookies from configuration
2025-05-24 18:58:35 | INFO | google_voice.voice_processor:update_cookies:72 | Updated Google Voice authentication cookies
2025-05-24 18:58:35 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 18:58:35 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 18:58:35 | INFO | __main__:extract_keen_data:117 | Starting Keen.com data extraction
2025-05-24 18:58:35 | INFO | __main__:extract_keen_data:121 | Running in sample mode: extracting 2 customers
2025-05-24 18:58:35 | INFO | keen_scraper.data_extractor:extract_sample_data:161 | Starting sample data extraction: 2 customers
2025-05-24 18:58:35 | INFO | keen_scraper.graphql_client:get_customer_list:204 | Fetching customer list: offset=0, page_size=100
2025-05-24 18:58:35 | WARNING | keen_scraper.auth_manager:validate_session:233 | Session validation failed with status 401
2025-05-24 18:58:39 | WARNING | keen_scraper.auth_manager:validate_session:233 | Session validation failed with status 401
2025-05-24 18:58:44 | WARNING | keen_scraper.auth_manager:validate_session:233 | Session validation failed with status 401
2025-05-24 18:58:44 | ERROR | keen_scraper.graphql_client:get_all_customers:316 | Error fetching customers at offset 0: RetryError[<Future at 0x70a24d75c5c0 state=finished raised AuthenticationError>]
2025-05-24 18:58:44 | INFO | keen_scraper.graphql_client:get_customer_list:204 | Fetching customer list: offset=100, page_size=100
2025-05-24 18:58:44 | WARNING | keen_scraper.auth_manager:validate_session:233 | Session validation failed with status 401
2025-05-24 18:58:48 | WARNING | keen_scraper.auth_manager:validate_session:233 | Session validation failed with status 401
2025-05-24 19:03:16 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 19:03:16 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 19:03:30 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 19:03:30 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
2025-05-24 19:03:30 | INFO | keen_scraper.auth_manager:_init_encryption:85 | Generated new encryption key: config/.encryption_key
2025-05-24 19:03:30 | INFO | keen_scraper.auth_manager:_load_cookies_from_config_or_storage:94 | Loading cookies from configuration
2025-05-24 19:03:30 | INFO | keen_scraper.auth_manager:_save_cookies:145 | Saved cookies successfully
2025-05-24 19:03:30 | INFO | google_voice.voice_processor:_load_cookies_from_config:64 | Loading Google Voice cookies from configuration
2025-05-24 19:03:30 | INFO | google_voice.voice_processor:update_cookies:72 | Updated Google Voice authentication cookies
2025-05-24 19:03:30 | INFO | google_voice.whisper_transcriber:__init__:67 | Initialized Whisper transcriber with model: base, device: cuda
2025-05-24 19:03:30 | INFO | __main__:_initialize_components:103 | All components initialized successfully
2025-05-24 19:03:30 | INFO | __main__:extract_keen_data:117 | Starting Keen.com data extraction
2025-05-24 19:03:30 | INFO | __main__:extract_keen_data:121 | Running in sample mode: extracting 2 customers
2025-05-24 19:03:30 | INFO | keen_scraper.data_extractor:extract_sample_data:161 | Starting sample data extraction: 2 customers
2025-05-24 19:03:30 | INFO | keen_scraper.graphql_client:get_ratings_and_reviews:460 | Fetching customer data from reviews: page=1, page_size=4
2025-05-24 19:03:31 | WARNING | keen_scraper.auth_manager:validate_session:233 | Session validation failed with status 401
2025-05-24 19:03:35 | WARNING | keen_scraper.auth_manager:validate_session:233 | Session validation failed with status 401
2025-05-24 19:03:39 | WARNING | keen_scraper.auth_manager:validate_session:233 | Session validation failed with status 401
2025-05-24 19:03:39 | ERROR | keen_scraper.data_extractor:extract_sample_data:246 | Error processing page 1: RetryError[<Future at 0x7515713b0560 state=finished raised AuthenticationError>]
2025-05-24 19:03:39 | INFO | keen_scraper.data_extractor:extract_sample_data:253 | Sample extraction completed: {'customers_processed': 0, 'customers_successful': 0, 'customers_failed': 0, 'chat_sessions_extracted': 0, 'chat_messages_extracted': 0, 'call_logs_extracted': 0, 'errors': []}
2025-05-24 19:03:39 | INFO | __main__:extract_keen_data:127 | Keen.com extraction completed: {'customers_processed': 0, 'customers_successful': 0, 'customers_failed': 0, 'chat_sessions_extracted': 0, 'chat_messages_extracted': 0, 'call_logs_extracted': 0, 'errors': []}
2025-05-24 19:04:00 | INFO | utils.logger_setup:setup_logging:53 | Logging configured successfully
2025-05-24 19:04:00 | INFO | __main__:_initialize_components:64 | Initializing AstralSubstance Clone components
