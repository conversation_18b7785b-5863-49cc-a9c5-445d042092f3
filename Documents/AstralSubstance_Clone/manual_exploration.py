#!/usr/bin/env python3
"""
Manual exploration of the customer interface to understand the structure
SAFETY: READ-ONLY exploration only
"""

import sys
from pathlib import Path
import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON>oader

def manual_exploration():
    """Manually explore the customer interface to understand its structure"""
    
    print("🔍 MANUAL EXPLORATION OF CUSTOMER INTERFACE")
    print("=" * 60)
    print("⚠️  SAFETY MODE: READ-ONLY EXPLORATION ONLY")
    print("⚠️  NO DATA WILL BE MODIFIED")
    print("=" * 60)
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup browser
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = None
    
    try:
        # Start browser
        print("\n🌐 Starting browser...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Set authentication
        print("🔐 Setting authentication...")
        setup_authentication(driver, keen_config)
        
        # Navigate to customer page
        print("🌐 Navigating to customer management page...")
        customer_url = f"{keen_config['base_url']}/app/myaccount/customers"
        driver.get(customer_url)
        
        # Wait for page load
        wait = WebDriverWait(driver, 15)
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        time.sleep(5)  # Let everything load
        
        print("✅ Customer page loaded!")
        print(f"📄 Current URL: {driver.current_url}")
        print(f"📄 Page title: {driver.title}")
        
        # Analyze the page structure
        print("\n📊 ANALYZING PAGE STRUCTURE...")
        analyze_page_structure(driver)
        
        # Look for customer elements
        print("\n👥 LOOKING FOR CUSTOMER ELEMENTS...")
        customer_elements = find_customer_elements(driver)
        
        if customer_elements:
            print(f"\n🎯 ANALYZING FIRST FEW CUSTOMERS...")
            analyze_customer_elements(driver, customer_elements[:5])  # Analyze first 5
            
            # Try clicking on first customer
            print(f"\n🖱️  TESTING CUSTOMER CLICK...")
            test_customer_click(driver, customer_elements[0])
        
        # Keep browser open for manual inspection
        print(f"\n⏸️  BROWSER READY FOR MANUAL INSPECTION")
        print(f"🔍 The browser window is now open at the customer page")
        print(f"📋 You can manually inspect the interface")
        print(f"⌨️  Press Enter when you're done exploring...")
        
        input("Press Enter to continue...")
        
    except Exception as e:
        print(f"❌ Error during exploration: {e}")
    finally:
        if driver:
            print("🔒 Closing browser...")
            driver.quit()

def setup_authentication(driver, keen_config):
    """Setup authentication cookies"""
    
    base_url = keen_config['base_url']
    
    # Navigate to main page first
    driver.get(base_url)
    time.sleep(2)
    
    # Add authentication cookies
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            try:
                driver.add_cookie({
                    'name': name,
                    'value': value,
                    'domain': '.keen.com'
                })
            except Exception as e:
                print(f"  ⚠️  Failed to add cookie {name}: {e}")

def analyze_page_structure(driver):
    """Analyze the overall page structure"""
    
    # Get page source info
    page_source = driver.page_source
    print(f"  📄 Page source length: {len(page_source)} characters")
    
    # Look for main content areas
    main_selectors = [
        'main', '.main', '#main',
        '.content', '#content', '.page-content',
        '.customer-list', '.customers', '#customers',
        'table', '.table', '.data-table'
    ]
    
    for selector in main_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"  ✅ Found {len(elements)} elements with selector: {selector}")
                
                # Get some info about the first element
                element = elements[0]
                tag_name = element.tag_name
                classes = element.get_attribute('class') or 'no-class'
                text_length = len(element.text)
                print(f"    📋 First element: <{tag_name}> class='{classes}' text_length={text_length}")
        except:
            pass
    
    # Look for pagination or load more buttons
    pagination_selectors = [
        '.pagination', '.pager', '.load-more',
        'button[aria-label*="next"]', 'button[aria-label*="page"]',
        '.next', '.previous', '.page-nav'
    ]
    
    pagination_found = False
    for selector in pagination_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"  📄 Found pagination: {selector} ({len(elements)} elements)")
                pagination_found = True
        except:
            pass
    
    if not pagination_found:
        print(f"  ℹ️  No obvious pagination found")

def find_customer_elements(driver):
    """Find customer elements on the page"""
    
    # Try various selectors that might contain customers
    customer_selectors = [
        # Table-based
        'tbody tr',
        'table tr[data-id]',
        'tr[data-customer-id]',
        'tr[onclick]',
        
        # List-based
        '.customer-item',
        '.customer-row',
        '.list-item',
        '[data-customer]',
        '[data-user]',
        
        # Generic clickable items
        '.clickable[data-id]',
        'a[href*="customer"]',
        'div[onclick]',
        
        # Card-based
        '.card[data-id]',
        '.customer-card',
        '.user-card',
    ]
    
    all_customers = []
    
    for selector in customer_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"  ✅ Found {len(elements)} potential customers with: {selector}")
                
                # Filter elements that look like customers
                customer_elements = []
                for element in elements:
                    if looks_like_customer(element):
                        customer_elements.append(element)
                
                if customer_elements:
                    print(f"    👥 {len(customer_elements)} look like actual customers")
                    all_customers.extend(customer_elements)
                    
                    # If we found a good number, this is probably the right selector
                    if len(customer_elements) > 10:
                        print(f"    🎯 Using selector: {selector}")
                        return customer_elements
        except Exception as e:
            print(f"  ❌ Error with selector {selector}: {e}")
    
    # Remove duplicates
    unique_customers = []
    seen_elements = set()
    
    for element in all_customers:
        element_id = id(element)
        if element_id not in seen_elements:
            unique_customers.append(element)
            seen_elements.add(element_id)
    
    print(f"  📊 Total unique customer elements: {len(unique_customers)}")
    return unique_customers

def looks_like_customer(element):
    """Check if an element looks like a customer entry"""
    
    try:
        # Get element text
        text = element.text.strip()
        
        # Skip if no text or very short
        if not text or len(text) < 3:
            return False
        
        # Skip if it's obviously not a customer (headers, buttons, etc.)
        skip_texts = [
            'customer', 'name', 'email', 'date', 'action',  # Headers
            'edit', 'delete', 'view', 'save', 'cancel',     # Buttons
            'loading', 'search', 'filter', 'sort'           # UI elements
        ]
        
        text_lower = text.lower()
        if any(skip_text in text_lower for skip_text in skip_texts) and len(text) < 20:
            return False
        
        # Check if it has customer-like attributes
        has_customer_attr = (
            element.get_attribute('data-customer-id') or
            element.get_attribute('data-user-id') or
            element.get_attribute('data-id') or
            'customer' in element.get_attribute('class', '').lower()
        )
        
        # Check if it's clickable
        is_clickable = (
            element.get_attribute('onclick') or
            element.get_attribute('href') or
            element.tag_name.lower() in ['a', 'button'] or
            'clickable' in element.get_attribute('class', '').lower()
        )
        
        # Must have either customer attributes or be clickable with reasonable text
        return has_customer_attr or (is_clickable and len(text) > 5)
        
    except:
        return False

def analyze_customer_elements(driver, customer_elements):
    """Analyze the structure of customer elements"""
    
    for i, element in enumerate(customer_elements):
        print(f"\n  👤 Customer {i+1}:")
        
        try:
            # Basic element info
            tag_name = element.tag_name
            classes = element.get_attribute('class') or 'no-class'
            print(f"    📋 Element: <{tag_name}> class='{classes}'")
            
            # Text content
            text = element.text.strip()
            if text:
                # Show first line or first 100 chars
                display_text = text.split('\n')[0][:100]
                print(f"    📄 Text: {display_text}")
            
            # Attributes
            interesting_attrs = ['data-customer-id', 'data-user-id', 'data-id', 'onclick', 'href']
            for attr in interesting_attrs:
                value = element.get_attribute(attr)
                if value:
                    print(f"    🔗 {attr}: {value[:100]}")
            
            # Child elements
            children = element.find_elements(By.CSS_SELECTOR, '*')
            if children:
                print(f"    👶 Children: {len(children)} elements")
                
                # Look for specific child types
                links = element.find_elements(By.CSS_SELECTOR, 'a')
                buttons = element.find_elements(By.CSS_SELECTOR, 'button')
                inputs = element.find_elements(By.CSS_SELECTOR, 'input')
                
                if links:
                    print(f"      🔗 {len(links)} links")
                if buttons:
                    print(f"      🔘 {len(buttons)} buttons")
                if inputs:
                    print(f"      📝 {len(inputs)} inputs")
            
        except Exception as e:
            print(f"    ❌ Error analyzing customer {i+1}: {e}")

def test_customer_click(driver, customer_element):
    """Test clicking on a customer element"""
    
    print(f"  🖱️  Testing click on customer element...")
    
    try:
        # Get current URL before clicking
        original_url = driver.current_url
        print(f"    📄 Original URL: {original_url}")
        
        # Scroll element into view
        driver.execute_script("arguments[0].scrollIntoView(true);", customer_element)
        time.sleep(1)
        
        # Try to click
        customer_element.click()
        time.sleep(3)
        
        # Check if URL changed
        new_url = driver.current_url
        print(f"    📄 New URL: {new_url}")
        
        if new_url != original_url:
            print(f"    ✅ Click successful! URL changed")
            
            # Analyze the new page
            print(f"    📊 Analyzing customer detail page...")
            analyze_customer_detail_page(driver)
            
            # Navigate back
            print(f"    ⬅️  Navigating back...")
            driver.back()
            time.sleep(2)
            
        else:
            print(f"    ⚠️  Click didn't change URL")
            
            # Try alternative click methods
            print(f"    🔄 Trying JavaScript click...")
            driver.execute_script("arguments[0].click();", customer_element)
            time.sleep(3)
            
            new_url2 = driver.current_url
            if new_url2 != original_url:
                print(f"    ✅ JavaScript click worked!")
                driver.back()
                time.sleep(2)
            else:
                print(f"    ❌ JavaScript click also failed")
        
    except Exception as e:
        print(f"    ❌ Error testing click: {e}")

def analyze_customer_detail_page(driver):
    """Analyze the customer detail page structure"""
    
    try:
        # Get page title
        title = driver.title
        print(f"      📄 Page title: {title}")
        
        # Look for customer information sections
        info_selectors = [
            '.customer-info', '.user-info', '.profile',
            '.customer-details', '.user-details',
            'h1', 'h2', '.name', '.email'
        ]
        
        for selector in info_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"      📋 Found {len(elements)} elements with: {selector}")
                    
                    # Show text from first element
                    if elements[0].text.strip():
                        text = elements[0].text.strip()[:100]
                        print(f"        📄 Sample text: {text}")
            except:
                pass
        
        # Look for conversation/session data
        conversation_selectors = [
            '.conversation', '.session', '.chat', '.call',
            '.message', '.conversation-item', '.session-item',
            'table', '.data-table', '.history'
        ]
        
        conversations_found = False
        for selector in conversation_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"      💬 Found {len(elements)} conversation elements with: {selector}")
                    conversations_found = True
            except:
                pass
        
        if not conversations_found:
            print(f"      ℹ️  No obvious conversation elements found")
        
    except Exception as e:
        print(f"      ❌ Error analyzing detail page: {e}")

if __name__ == "__main__":
    manual_exploration()
