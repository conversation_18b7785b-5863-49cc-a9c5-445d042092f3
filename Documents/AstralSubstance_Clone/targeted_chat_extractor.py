#!/usr/bin/env python3
"""
Targeted chat transcript extractor using the exact URL structure you identified
"""

import sys
from pathlib import Path
import json
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import re

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class TargetedChatExtractor:
    """Extract chat transcripts using the exact URL structure"""
    
    def __init__(self):
        self.driver = None
        self.customers = {}
        self.chat_transcripts = {}
        self.errors = []
        self.progress_file = Path("data/chat_extraction_progress.json")
        
    def run_chat_extraction(self):
        """Run the complete chat extraction process"""
        
        print("💬 TARGETED CHAT TRANSCRIPT EXTRACTOR")
        print("=" * 60)
        print("🎯 Using exact URL structure:")
        print("  • Customer pages: /app/#/myaccount/customers/{id}")
        print("  • Chat transcripts: /myaccount/transactions/chat-details?id={id}")
        print("=" * 60)
        
        # Setup browser
        if not self.setup_browser():
            return
        
        # Load customer data
        if not self.load_customer_data():
            return
        
        # Extract chat transcripts
        self.extract_all_chat_transcripts()
        
        # Save results
        self.save_chat_data()
        
        # Cleanup
        self.cleanup()
    
    def setup_browser(self):
        """Setup Chrome browser with authentication"""
        
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Load config and set cookies
            config = ConfigLoader.load_config("config/config.yaml")
            keen_config = config['keen']
            
            # Navigate to Keen.com first
            self.driver.get(keen_config['base_url'])
            time.sleep(3)
            
            # Set authentication cookies
            cookies = keen_config['cookies']
            for name, value in cookies.items():
                if value and not value.startswith('YOUR_'):
                    self.driver.add_cookie({
                        'name': name,
                        'value': value,
                        'domain': '.keen.com'
                    })
            
            print("✅ Browser setup complete with authentication")
            return True
            
        except Exception as e:
            print(f"❌ Error setting up browser: {e}")
            return False
    
    def load_customer_data(self):
        """Load the customer data we extracted"""
        
        customer_file = Path("data/graphql_complete_customers.json")
        
        if not customer_file.exists():
            print(f"❌ Customer data file not found: {customer_file}")
            return False
        
        try:
            with open(customer_file, 'r') as f:
                data = json.load(f)
            
            self.customers = data.get('customers', {})
            print(f"✅ Loaded {len(self.customers)} customers")
            
            # Filter customers with contact history (potential chat customers)
            customers_with_contacts = {
                cid: cdata for cid, cdata in self.customers.items()
                if cdata.get('contacts', {}).get('last')
            }
            
            print(f"📊 {len(customers_with_contacts)} customers have contact history")
            return True
            
        except Exception as e:
            print(f"❌ Error loading customer data: {e}")
            return False
    
    def extract_all_chat_transcripts(self):
        """Extract chat transcripts for all customers"""
        
        print(f"\n💬 Extracting chat transcripts...")
        
        # Load progress
        progress = self.load_progress()
        completed_customers = set(progress.get('completed_customers', []))
        
        # Filter customers with contact history
        target_customers = [
            (cid, cdata) for cid, cdata in self.customers.items()
            if cdata.get('contacts', {}).get('last') and cid not in completed_customers
        ]
        
        if completed_customers:
            print(f"🔄 Resuming extraction ({len(completed_customers)} already completed)")
        
        print(f"🎯 Processing {len(target_customers)} customers with contact history")
        
        for i, (customer_id, customer_data) in enumerate(target_customers, 1):
            username = customer_data.get('userName', 'Unknown')
            print(f"\n👤 Customer {i}/{len(target_customers)}: {username} (ID: {customer_id})")
            
            try:
                # Navigate to customer page
                customer_url = f"https://www.keen.com/app/#/myaccount/customers/{customer_id}"
                print(f"    🌐 Navigating to: {customer_url}")
                
                self.driver.get(customer_url)
                time.sleep(3)
                
                # Check for chat history
                chat_transcripts = self.extract_customer_chats(customer_id, customer_data)
                
                if chat_transcripts:
                    self.chat_transcripts[customer_id] = {
                        'customer_info': customer_data,
                        'transcripts': chat_transcripts,
                        'transcript_count': len(chat_transcripts),
                        'extraction_timestamp': datetime.now().isoformat()
                    }
                    
                    print(f"    ✅ Extracted {len(chat_transcripts)} chat transcripts")
                else:
                    print(f"    ℹ️  No chat transcripts found")
                
                # Save progress
                completed_customers.add(customer_id)
                self.save_progress(list(completed_customers))
                
            except Exception as e:
                error_msg = f"Error extracting chats for customer {customer_id}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)
            
            # Rate limiting
            time.sleep(2)
        
        print(f"\n📊 Chat extraction complete!")
        print(f"✅ Extracted transcripts for {len(self.chat_transcripts)} customers")
    
    def extract_customer_chats(self, customer_id, customer_data):
        """Extract chat transcripts for a specific customer"""
        
        transcripts = []
        
        try:
            # Look for chat history indicators on the customer page
            # This might be a table, list, or section showing chat sessions
            
            # Method 1: Look for chat/transaction links
            chat_links = self.find_chat_links()
            
            if chat_links:
                print(f"      📋 Found {len(chat_links)} chat links")
                
                for i, chat_link in enumerate(chat_links[:10], 1):  # Limit to first 10 chats
                    print(f"        💬 Extracting chat {i}/{min(len(chat_links), 10)}")
                    
                    transcript = self.extract_single_chat_transcript(chat_link, i)
                    if transcript:
                        transcripts.append(transcript)
                    
                    time.sleep(1)  # Rate limiting between chats
            
            # Method 2: Look for transaction IDs in customer data
            contacts = customer_data.get('contacts', {})
            last_contact = contacts.get('last', {})
            
            if last_contact.get('masterTransactionId'):
                transaction_id = last_contact['masterTransactionId']
                print(f"      🔍 Trying transaction ID: {transaction_id}")
                
                transcript = self.extract_chat_by_transaction_id(transaction_id)
                if transcript:
                    transcripts.append(transcript)
            
        except Exception as e:
            print(f"      ❌ Error extracting customer chats: {e}")
        
        return transcripts
    
    def find_chat_links(self):
        """Find chat/transaction links on the customer page"""
        
        chat_links = []
        
        try:
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Look for different types of chat links
            link_selectors = [
                'a[href*="chat-details"]',
                'a[href*="transaction"]',
                'a[href*="chat"]',
                '[data-transaction-id]',
                '.chat-link',
                '.transaction-link'
            ]
            
            for selector in link_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and 'chat-details' in href:
                            # Extract transaction ID from URL
                            match = re.search(r'id=(\d+)', href)
                            if match:
                                transaction_id = match.group(1)
                                chat_links.append({
                                    'url': href,
                                    'transaction_id': transaction_id,
                                    'element_text': element.text.strip()
                                })
                except:
                    continue
            
            # Also look for transaction IDs in the page text/data
            page_source = self.driver.page_source
            transaction_matches = re.findall(r'(?:transaction|chat).*?id["\s]*[:=]["\s]*(\d+)', page_source, re.IGNORECASE)
            
            for transaction_id in transaction_matches:
                if len(transaction_id) > 6:  # Reasonable transaction ID length
                    chat_url = f"https://www.keen.com/myaccount/transactions/chat-details?id={transaction_id}"
                    chat_links.append({
                        'url': chat_url,
                        'transaction_id': transaction_id,
                        'element_text': f'Transaction {transaction_id}'
                    })
            
            # Remove duplicates
            unique_links = []
            seen_ids = set()
            
            for link in chat_links:
                if link['transaction_id'] not in seen_ids:
                    seen_ids.add(link['transaction_id'])
                    unique_links.append(link)
            
            return unique_links
            
        except Exception as e:
            print(f"        ⚠️  Error finding chat links: {e}")
            return []
    
    def extract_single_chat_transcript(self, chat_link, chat_index):
        """Extract a single chat transcript"""
        
        try:
            chat_url = chat_link['url']
            transaction_id = chat_link['transaction_id']
            
            # Navigate to chat transcript page
            self.driver.get(chat_url)
            time.sleep(3)
            
            # Wait for chat content to load
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except:
                print(f"          ⚠️  Chat page did not load properly")
                return None
            
            # Extract chat messages
            messages = self.extract_chat_messages()
            
            if messages:
                transcript = {
                    'transaction_id': transaction_id,
                    'chat_url': chat_url,
                    'chat_index': chat_index,
                    'messages': messages,
                    'message_count': len(messages),
                    'extraction_timestamp': datetime.now().isoformat(),
                    'source': 'keen_chat_details_page'
                }
                
                return transcript
            else:
                print(f"          ℹ️  No messages found in chat {transaction_id}")
                return None
                
        except Exception as e:
            print(f"          ❌ Error extracting chat {chat_link.get('transaction_id', 'unknown')}: {e}")
            return None
    
    def extract_chat_by_transaction_id(self, transaction_id):
        """Extract chat using transaction ID directly"""
        
        try:
            chat_url = f"https://www.keen.com/myaccount/transactions/chat-details?id={transaction_id}"
            
            self.driver.get(chat_url)
            time.sleep(3)
            
            messages = self.extract_chat_messages()
            
            if messages:
                return {
                    'transaction_id': transaction_id,
                    'chat_url': chat_url,
                    'messages': messages,
                    'message_count': len(messages),
                    'extraction_timestamp': datetime.now().isoformat(),
                    'source': 'direct_transaction_id'
                }
            
        except Exception as e:
            print(f"        ❌ Error extracting chat by transaction ID {transaction_id}: {e}")
        
        return None
    
    def extract_chat_messages(self):
        """Extract messages from a chat transcript page"""
        
        messages = []
        
        try:
            # Look for different message container patterns
            message_selectors = [
                '.message',
                '.chat-message',
                '.conversation-message',
                '.transcript-message',
                '[class*="message"]',
                '[class*="chat"]',
                'p',
                'div[class*="text"]'
            ]
            
            for selector in message_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        message = self.extract_message_from_element(element)
                        if message:
                            messages.append(message)
                    
                    if messages:  # Found messages with this selector
                        break
                        
                except:
                    continue
            
            # If no structured messages found, try to extract from page text
            if not messages:
                messages = self.extract_messages_from_page_text()
            
            # Process and order messages
            if messages:
                messages = self.process_chat_messages(messages)
            
        except Exception as e:
            print(f"          ❌ Error extracting chat messages: {e}")
        
        return messages
    
    def extract_message_from_element(self, element):
        """Extract message data from a DOM element"""
        
        try:
            text = element.text.strip()
            if not text or len(text) < 3:
                return None
            
            message = {
                'content': text,
                'element_tag': element.tag_name,
                'element_class': element.get_attribute('class') or ''
            }
            
            # Try to determine sender
            classes = message['element_class'].lower()
            if 'advisor' in classes or 'psychic' in classes:
                message['sender_type'] = 'advisor'
            elif 'customer' in classes or 'user' in classes:
                message['sender_type'] = 'customer'
            else:
                message['sender_type'] = self.infer_sender_from_content(text)
            
            # Look for timestamp
            try:
                timestamp_elem = element.find_element(By.CSS_SELECTOR, '.timestamp, .time, [data-time]')
                if timestamp_elem:
                    message['timestamp'] = timestamp_elem.text or timestamp_elem.get_attribute('data-time')
            except:
                pass
            
            return message
            
        except:
            return None
    
    def extract_messages_from_page_text(self):
        """Extract messages from unstructured page text"""
        
        try:
            page_text = self.driver.find_element(By.TAG_NAME, 'body').text
            lines = [line.strip() for line in page_text.split('\n') if line.strip()]
            
            messages = []
            for i, line in enumerate(lines):
                if len(line) > 10:  # Skip very short lines
                    message = {
                        'content': line,
                        'line_number': i,
                        'sender_type': self.infer_sender_from_content(line),
                        'source': 'page_text_extraction'
                    }
                    messages.append(message)
            
            return messages
            
        except:
            return []
    
    def infer_sender_from_content(self, text):
        """Infer message sender from content patterns"""
        
        text_lower = text.lower()
        
        # Advisor patterns
        advisor_patterns = [
            'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',
            'let me', 'i can help', 'what i\'m getting', 'i\'m seeing',
            'the universe', 'your guides', 'i\'m picking up'
        ]
        
        # Customer patterns  
        customer_patterns = [
            'will i', 'should i', 'when will', 'what about', 'can you tell me',
            'i want to know', 'my question', 'help me understand'
        ]
        
        advisor_score = sum(1 for pattern in advisor_patterns if pattern in text_lower)
        customer_score = sum(1 for pattern in customer_patterns if pattern in text_lower)
        
        if advisor_score > customer_score:
            return 'advisor'
        elif customer_score > advisor_score:
            return 'customer'
        else:
            return 'unknown'
    
    def process_chat_messages(self, messages):
        """Process and order chat messages"""
        
        # Add sequence numbers
        for i, msg in enumerate(messages):
            msg['sequence'] = i + 1
        
        # Try to sort by timestamp if available
        timestamped_messages = [msg for msg in messages if msg.get('timestamp')]
        
        if timestamped_messages:
            try:
                timestamped_messages.sort(key=lambda x: self.parse_timestamp(x['timestamp']))
                
                # Re-sequence
                for i, msg in enumerate(timestamped_messages):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = True
                
                # Add non-timestamped messages at the end
                non_timestamped = [msg for msg in messages if not msg.get('timestamp')]
                for i, msg in enumerate(non_timestamped):
                    msg['sequence'] = len(timestamped_messages) + i + 1
                    msg['order_resolved'] = False
                
                return timestamped_messages + non_timestamped
                
            except:
                pass
        
        # If timestamp sorting fails, use original order
        for msg in messages:
            msg['order_resolved'] = False
        
        return messages
    
    def parse_timestamp(self, timestamp_str):
        """Parse timestamp for sorting"""
        
        from datetime import datetime
        
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%m/%d/%Y %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%m/%d/%Y %H:%M',
            '%H:%M:%S',
            '%H:%M'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(str(timestamp_str), fmt)
            except:
                continue
        
        return datetime.min
    
    def load_progress(self):
        """Load extraction progress"""
        
        try:
            if self.progress_file.exists():
                with open(self.progress_file, 'r') as f:
                    return json.load(f)
        except:
            pass
        
        return {}
    
    def save_progress(self, completed_customers):
        """Save extraction progress"""
        
        try:
            progress = {
                'completed_customers': completed_customers,
                'total_completed': len(completed_customers),
                'timestamp': datetime.now().isoformat()
            }
            
            with open(self.progress_file, 'w') as f:
                json.dump(progress, f, indent=2)
        except:
            pass
    
    def save_chat_data(self):
        """Save all extracted chat data"""
        
        print(f"\n💾 Saving chat transcript data...")
        
        # Create comprehensive chat dataset
        chat_dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'total_customers_processed': len([c for c in self.customers.values() if c.get('contacts', {}).get('last')]),
                'customers_with_chats': len(self.chat_transcripts),
                'total_chat_transcripts': sum(len(t['transcripts']) for t in self.chat_transcripts.values()),
                'extraction_method': 'targeted_url_structure'
            },
            'chat_transcripts': self.chat_transcripts,
            'errors': self.errors
        }
        
        # Save complete chat data
        chat_file = Path("data/targeted_chat_transcripts.json")
        with open(chat_file, 'w') as f:
            json.dump(chat_dataset, f, indent=2)
        
        print(f"💾 Chat transcript data saved to: {chat_file}")
        
        # Generate summary
        metadata = chat_dataset['metadata']
        
        print(f"\n📊 CHAT EXTRACTION SUMMARY:")
        print(f"  • Customers processed: {metadata['total_customers_processed']}")
        print(f"  • Customers with chats: {metadata['customers_with_chats']}")
        print(f"  • Total chat transcripts: {metadata['total_chat_transcripts']}")
        print(f"  • Success rate: {metadata['customers_with_chats']/max(metadata['total_customers_processed'], 1)*100:.1f}%")
    
    def cleanup(self):
        """Clean up resources"""
        
        if self.driver:
            try:
                self.driver.quit()
                print("🔒 Browser closed")
            except:
                pass

if __name__ == "__main__":
    extractor = TargetedChatExtractor()
    extractor.run_chat_extraction()
