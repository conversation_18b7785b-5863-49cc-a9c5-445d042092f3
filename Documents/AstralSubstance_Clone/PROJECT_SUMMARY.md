# AstralSubstance Clone - Project Summary

## 🎯 Project Overview

**AstralSubstance Clone** is a comprehensive, production-ready system for extracting, correlating, and formatting data from Keen.com and Google Voice into structured datasets suitable for Large Language Model (LLM) training and fine-tuning.

### Key Features ✨

- **🔐 Robust Authentication**: Cookie-based session management with encryption
- **📊 Complete Data Extraction**: Customer profiles, chat transcripts, call logs
- **🎵 Audio Processing**: Google Voice recording analysis and transcription
- **🔗 Smart Correlation**: Timestamp and duration-based matching algorithms
- **📚 Multiple Dataset Formats**: Conversational, instruction-following, prompt-completion
- **🛡️ Production Ready**: Comprehensive error handling, logging, and monitoring
- **🚫 No PII Filtering**: Preserves all conversation data exactly as extracted

## 📁 Project Structure

```
AstralSubstance_Clone/
├── src/
│   ├── keen_scraper/          # Keen.com API interaction
│   │   ├── auth_manager.py    # Session & cookie management
│   │   ├── graphql_client.py  # GraphQL API client
│   │   └── data_extractor.py  # Data extraction orchestration
│   ├── google_voice/          # Google Voice processing
│   │   └── voice_processor.py # Audio file processing & transcription
│   ├── data_correlation/      # Data matching & correlation
│   │   └── correlator.py      # Correlation algorithms
│   ├── dataset_builder/       # LLM dataset formatting
│   │   └── llm_formatter.py   # Multiple output formats
│   ├── models/                # Database models & schemas
│   │   └── database.py        # SQLAlchemy models
│   ├── utils/                 # Shared utilities
│   │   ├── logger_setup.py    # Logging configuration
│   │   └── config_loader.py   # Configuration management
│   └── main.py               # CLI application entry point
├── config/
│   └── config.example.yaml   # Configuration template
├── data/                     # Data storage (created at runtime)
│   ├── raw/                  # Raw extracted data
│   ├── processed/            # Cleaned & correlated data
│   └── datasets/             # Final LLM datasets
├── logs/                     # Application logs
├── requirements.txt          # Python dependencies
├── setup.py                 # Package installation
├── README.md                # Project documentation
├── IMPLEMENTATION_PLAN.md   # Detailed technical plan
├── QUICK_START.md           # Getting started guide
└── test_implementation.py   # Validation tests
```

## 🔧 Technical Implementation

### Authentication Strategy
- **Cookie-based Authentication**: Extracts and manages Keen.com session cookies
- **Encrypted Storage**: Secure local storage of authentication credentials
- **Auto-validation**: Automatic session validation and refresh prompting
- **Fallback Mechanisms**: Manual cookie update process with clear instructions

### Data Extraction Architecture
- **GraphQL Client**: Robust client for Keen.com's GraphQL API
- **Pagination Handling**: Efficient processing of large customer lists
- **Rate Limiting**: Respectful scraping with configurable delays
- **Error Recovery**: Comprehensive retry logic and graceful degradation

### Data Correlation Methodology
```python
# Timestamp + Duration Matching Algorithm
def correlate_voice_with_calls(call_logs, voice_recordings):
    for recording in voice_recordings:
        best_match = find_best_match(recording, call_logs)
        if best_match.confidence > threshold:
            apply_correlation(recording, best_match)
```

### LLM Dataset Formats

#### 1. Conversational Format (ChatML-style)
```json
{
  "conversation_id": "keen_chat_12345",
  "messages": [
    {"role": "user", "content": "I need relationship advice"},
    {"role": "assistant", "content": "I sense your concern..."}
  ],
  "metadata": {"duration_minutes": 45, "earnings": 67.50}
}
```

#### 2. Instruction-Following Format
```json
{
  "instruction": "You are a psychic advisor...",
  "input": "Customer concern about relationship",
  "output": "Empathetic and insightful response",
  "metadata": {"conversation_id": "keen_chat_12345"}
}
```

## 🚀 Quick Start

### 1. Installation
```bash
pip install -r requirements.txt
cp config/config.example.yaml config/config.yaml
```

### 2. Authentication Setup
```bash
# Extract cookies from browser and update config.yaml
python src/main.py setup-auth
```

### 3. Run Pipeline
```bash
# Test with sample data
python src/main.py run-pipeline --sample

# Full extraction
python src/main.py run-pipeline
```

### 4. Check Results
```bash
python src/main.py status
ls data/datasets/  # View generated datasets
```

## 📊 Expected Output

### Dataset Statistics
- **Customers**: 100+ customer profiles with interaction history
- **Conversations**: Thousands of chat sessions with complete message threads
- **Call Logs**: Detailed call metadata with duration and earnings
- **Voice Recordings**: Correlated audio files with transcriptions
- **Training Data**: Multiple format datasets ready for LLM training

### Quality Metrics
- **Correlation Rate**: 80%+ voice recordings matched to call logs
- **Data Completeness**: 95%+ of conversations with full message threads
- **Timestamp Accuracy**: ±5 minute correlation tolerance
- **Format Compliance**: 100% valid JSONL output

## 🛡️ Security & Compliance

### Data Privacy
- **Local Processing**: All data remains on your system
- **Encrypted Storage**: Sensitive credentials encrypted at rest
- **No External Transmission**: No data sent to third parties
- **PII Preservation**: Complete conversation data without filtering

### Ethical Considerations
- **Terms of Service**: Review Keen.com ToS compliance
- **Rate Limiting**: Respectful API usage patterns
- **Data Ownership**: Ensure you have rights to the data
- **Usage Guidelines**: Follow platform-specific guidelines

## 🔍 Monitoring & Quality Assurance

### Built-in Monitoring
- **Extraction Logs**: Detailed operation tracking
- **Error Reporting**: Comprehensive error capture and reporting
- **Progress Tracking**: Real-time extraction progress
- **Quality Metrics**: Data completeness and correlation statistics

### Validation Checkpoints
- **Schema Validation**: Pydantic models ensure data integrity
- **Correlation Verification**: Confidence scoring for voice matching
- **Dataset Validation**: Format compliance checking
- **Manual Spot Checks**: Random sample validation recommendations

## 🎯 Success Criteria

### Technical Success
- ✅ Complete extraction of all available Keen.com data
- ✅ Successful correlation of Google Voice recordings
- ✅ Production of clean, structured LLM datasets
- ✅ Maintainable, documented codebase

### Data Quality Success
- ✅ >95% conversation completeness
- ✅ >80% voice recording correlation rate
- ✅ Multiple output formats for different training approaches
- ✅ Comprehensive metadata preservation

## 🔮 Next Steps

### Immediate Actions
1. **Test Implementation**: Run `python test_implementation.py`
2. **Configure Authentication**: Extract and configure Keen.com cookies
3. **Sample Run**: Execute pipeline with sample data
4. **Validate Output**: Review generated datasets

### Production Deployment
1. **Full Extraction**: Run complete data extraction
2. **Quality Review**: Validate data quality and completeness
3. **Dataset Preparation**: Prepare final training datasets
4. **LLM Training**: Use datasets for model fine-tuning

### Future Enhancements
- **Real-time Monitoring**: Add monitoring dashboard
- **Advanced Correlation**: ML-based correlation algorithms
- **Additional Formats**: Support for more LLM training formats
- **Automated QA**: Enhanced data quality automation

## 📞 Support & Troubleshooting

### Common Issues
- **Authentication Failures**: Update cookies, check session validity
- **Rate Limiting**: Adjust delay settings, monitor API responses
- **Memory Issues**: Reduce batch sizes, process in chunks
- **Correlation Problems**: Adjust tolerance parameters

### Debug Resources
- **Logs**: Check `logs/astral_substance.log` for detailed information
- **Database**: Examine `data/astral_substance.db` with SQLite browser
- **Configuration**: Review `config/config.yaml` settings
- **Test Suite**: Run validation tests for component verification

---

**🎉 Congratulations!** You now have a complete, production-ready system for building LLM datasets from Keen.com and Google Voice data. The implementation preserves all conversation data exactly as extracted, providing you with high-quality training data for your LLM fine-tuning projects.
