#!/usr/bin/env python3
"""
Master script to run the complete data extraction and processing pipeline
"""

import sys
import subprocess
import time
from pathlib import Path
from datetime import datetime

def run_script(script_name, description):
    """Run a script and handle errors"""
    
    print(f"\n{'='*60}")
    print(f"🚀 RUNNING: {description}")
    print(f"📄 Script: {script_name}")
    print(f"⏰ Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    try:
        # Run the script
        result = subprocess.run([
            sys.executable, script_name
        ], capture_output=True, text=True, timeout=3600)  # 1 hour timeout
        
        if result.returncode == 0:
            print(f"✅ SUCCESS: {description}")
            print(f"📊 Output preview:")
            print(result.stdout[-500:] if len(result.stdout) > 500 else result.stdout)
            return True
        else:
            print(f"❌ FAILED: {description}")
            print(f"📄 Error output:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT: {description} took longer than 1 hour")
        return False
    except Exception as e:
        print(f"❌ ERROR: {description} - {e}")
        return False

def check_prerequisites():
    """Check if all prerequisites are met"""
    
    print("🔍 CHECKING PREREQUISITES")
    print("=" * 60)
    
    # Check if config file exists
    config_file = Path("config/config.yaml")
    if not config_file.exists():
        print(f"❌ Config file not found: {config_file}")
        print("   Please ensure authentication cookies are configured")
        return False
    else:
        print(f"✅ Config file found: {config_file}")
    
    # Check if data directory exists
    data_dir = Path("data")
    if not data_dir.exists():
        data_dir.mkdir()
        print(f"📁 Created data directory: {data_dir}")
    else:
        print(f"✅ Data directory exists: {data_dir}")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major >= 3 and python_version.minor >= 8:
        print(f"✅ Python version: {python_version.major}.{python_version.minor}")
    else:
        print(f"❌ Python version too old: {python_version.major}.{python_version.minor}")
        print("   Please use Python 3.8 or newer")
        return False
    
    return True

def main():
    """Run the complete pipeline"""
    
    print("🎯 COMPREHENSIVE KEEN.COM DATA EXTRACTION PIPELINE")
    print("=" * 60)
    print("📊 This pipeline will:")
    print("  1. Extract all 4,297 customer profiles")
    print("  2. Extract chat transcripts from Keen.com")
    print("  3. Extract Google Voice recordings")
    print("  4. Transcribe recordings with Whisper")
    print("  5. Correlate all data sources")
    print("  6. Create final LLM training dataset")
    print("  7. Resolve message ordering issues")
    print("=" * 60)
    
    start_time = datetime.now()
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix issues and try again.")
        return
    
    # Pipeline steps
    pipeline_steps = [
        {
            'script': 'graphql_customer_scraper.py',
            'description': 'Extract all 4,297 customer profiles via GraphQL',
            'required': True
        },
        {
            'script': 'extract_chat_transcripts.py',
            'description': 'Extract chat transcripts for customers',
            'required': False  # Optional - may not find transcripts
        },
        {
            'script': 'google_voice_scraper.py',
            'description': 'Extract Google Voice recordings',
            'required': False  # Optional - depends on Google Voice access
        },
        {
            'script': 'whisper_transcriber.py',
            'description': 'Transcribe recordings with Whisper',
            'required': False  # Optional - depends on previous step
        },
        {
            'script': 'comprehensive_data_correlator.py',
            'description': 'Correlate all data and create final dataset',
            'required': True
        }
    ]
    
    # Track results
    results = []
    
    # Run each step
    for i, step in enumerate(pipeline_steps, 1):
        print(f"\n📍 PIPELINE STEP {i}/{len(pipeline_steps)}")
        
        success = run_script(step['script'], step['description'])
        
        results.append({
            'step': i,
            'script': step['script'],
            'description': step['description'],
            'success': success,
            'required': step['required']
        })
        
        if not success and step['required']:
            print(f"\n❌ PIPELINE FAILED at required step {i}")
            print(f"   Step: {step['description']}")
            print(f"   Script: {step['script']}")
            break
        elif not success:
            print(f"\n⚠️  Optional step {i} failed, continuing...")
        
        # Brief pause between steps
        time.sleep(2)
    
    # Generate final report
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print(f"🏁 PIPELINE EXECUTION COMPLETE")
    print(f"{'='*60}")
    print(f"⏰ Started: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏰ Ended: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏱️  Duration: {duration}")
    print(f"\n📊 STEP RESULTS:")
    
    successful_steps = 0
    failed_required_steps = 0
    
    for result in results:
        status = "✅ SUCCESS" if result['success'] else "❌ FAILED"
        required = "(REQUIRED)" if result['required'] else "(OPTIONAL)"
        
        print(f"  {result['step']}. {status} {required}")
        print(f"     {result['description']}")
        
        if result['success']:
            successful_steps += 1
        elif result['required']:
            failed_required_steps += 1
    
    print(f"\n📈 SUMMARY:")
    print(f"  • Successful steps: {successful_steps}/{len(results)}")
    print(f"  • Failed required steps: {failed_required_steps}")
    
    if failed_required_steps == 0:
        print(f"\n🎉 PIPELINE SUCCESS!")
        print(f"✅ All required steps completed successfully")
        
        # Check for final dataset
        final_dataset = Path("data/FINAL_LLM_TRAINING_DATASET.json")
        if final_dataset.exists():
            print(f"🎯 Final LLM training dataset created: {final_dataset}")
            print(f"📊 Dataset size: {final_dataset.stat().st_size / 1024 / 1024:.1f} MB")
        
        print(f"\n🚀 READY FOR LLM TRAINING!")
        
    else:
        print(f"\n❌ PIPELINE INCOMPLETE")
        print(f"⚠️  {failed_required_steps} required steps failed")
        print(f"🔧 Please check error messages and fix issues")
    
    # List created files
    data_files = list(Path("data").glob("*.json"))
    if data_files:
        print(f"\n📁 FILES CREATED ({len(data_files)}):")
        for file in sorted(data_files):
            size_mb = file.stat().st_size / 1024 / 1024
            print(f"  • {file.name} ({size_mb:.1f} MB)")
    
    print(f"\n📋 Check individual step logs above for detailed information")

if __name__ == "__main__":
    main()
