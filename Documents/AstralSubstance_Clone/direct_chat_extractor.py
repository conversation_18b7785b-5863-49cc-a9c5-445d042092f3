#!/usr/bin/env python3
"""
Direct chat transcript extractor using your authentication cookies
"""

import sys
from pathlib import Path
import json
import requests
import time
from datetime import datetime
import re

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class DirectChatExtractor:
    """Extract chat transcripts directly using your auth cookies"""
    
    def __init__(self):
        self.session = None
        self.chat_transcripts = {}
        self.errors = []
        
    def setup_session(self):
        """Setup session with your authentication cookies"""
        
        print("🔐 Setting up session with your authentication cookies...")
        
        # Load your authentication cookies
        config = ConfigLoader.load_config("config/config.yaml")
        keen_cookies = config['keen']['cookies']
        
        # Create session
        self.session = requests.Session()
        
        # Set Keen.com authentication cookies
        for name, value in keen_cookies.items():
            if value and not value.startswith('YOUR_'):
                self.session.cookies.set(name, value, domain='.keen.com')
        
        # Set headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Priority': 'u=1'
        })
        
        # Test authentication
        try:
            response = self.session.get('https://www.keen.com/app/#/myaccount/customers', timeout=15)
            if response.status_code == 200 and 'login' not in response.url.lower():
                print("✅ Authentication successful")
                return True
            else:
                print("❌ Authentication failed")
                return False
        except Exception as e:
            print(f"❌ Error testing authentication: {e}")
            return False
    
    def load_interactions(self):
        """Load the interaction data we prepared"""
        
        interactions_file = Path("data/colab_interactions.json")
        with open(interactions_file, 'r') as f:
            data = json.load(f)
        
        interactions = data['interactions'][:5]  # Start with first 5 for testing
        print(f"📋 Loaded {len(interactions)} interactions for extraction")
        
        return interactions
    
    def extract_chat_transcript(self, interaction):
        """Extract a single chat transcript"""
        
        try:
            chat_url = interaction['chat_url']
            interaction_id = interaction['interaction_id']
            customer_username = interaction['customer_username']
            
            print(f"  💬 Extracting chat {interaction_id} for {customer_username}...")
            
            # Get the chat transcript page
            response = self.session.get(chat_url, timeout=15)
            
            if response.status_code == 200:
                # Extract messages from HTML
                messages = self.extract_messages_from_html(response.text)
                
                if messages:
                    transcript = {
                        'interaction_id': interaction_id,
                        'customer_id': interaction['customer_id'],
                        'customer_username': customer_username,
                        'chat_url': chat_url,
                        'messages': messages,
                        'message_count': len(messages),
                        'extraction_timestamp': datetime.now().isoformat(),
                        'extraction_method': 'direct_http_request'
                    }
                    
                    print(f"    ✅ Extracted {len(messages)} messages")
                    return transcript
                else:
                    print(f"    ℹ️  No messages found in HTML")
                    return None
            else:
                print(f"    ❌ HTTP error: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"    ❌ Error extracting chat {interaction.get('interaction_id', 'unknown')}: {e}")
            return None
    
    def extract_messages_from_html(self, html_content):
        """Extract messages from HTML content"""
        
        messages = []
        
        try:
            # Look for common chat message patterns in HTML
            
            # Pattern 1: Look for structured message data
            json_pattern = r'"messages":\s*\[(.*?)\]'
            json_matches = re.findall(json_pattern, html_content, re.DOTALL)
            
            for match in json_matches:
                try:
                    # Try to parse as JSON
                    messages_json = f'[{match}]'
                    parsed_messages = json.loads(messages_json)
                    
                    for i, msg in enumerate(parsed_messages):
                        if isinstance(msg, dict) and msg.get('content'):
                            message = {
                                'sequence': i + 1,
                                'content': msg.get('content', ''),
                                'sender_type': self.infer_sender_type(msg.get('content', '')),
                                'timestamp': msg.get('timestamp'),
                                'extraction_method': 'json_pattern'
                            }
                            messages.append(message)
                except:
                    continue
            
            # Pattern 2: Look for message divs/spans
            if not messages:
                message_patterns = [
                    r'<div[^>]*class="[^"]*message[^"]*"[^>]*>(.*?)</div>',
                    r'<span[^>]*class="[^"]*chat[^"]*"[^>]*>(.*?)</span>',
                    r'<p[^>]*class="[^"]*dialogue[^"]*"[^>]*>(.*?)</p>'
                ]
                
                for pattern in message_patterns:
                    matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                    
                    for i, match in enumerate(matches):
                        # Clean HTML tags
                        clean_text = re.sub(r'<[^>]+>', '', match).strip()
                        
                        if clean_text and len(clean_text) > 3:
                            message = {
                                'sequence': i + 1,
                                'content': clean_text,
                                'sender_type': self.infer_sender_type(clean_text),
                                'extraction_method': 'html_pattern'
                            }
                            messages.append(message)
                    
                    if messages:
                        break
            
            # Pattern 3: Look for transcript text blocks
            if not messages:
                # Look for large text blocks that might be conversations
                text_blocks = re.findall(r'<(?:div|p|span)[^>]*>([^<]{20,})</(?:div|p|span)>', html_content, re.DOTALL)
                
                for i, block in enumerate(text_blocks):
                    clean_text = re.sub(r'\s+', ' ', block).strip()
                    
                    if (len(clean_text) > 20 and 
                        not any(skip in clean_text.lower() for skip in ['navigation', 'menu', 'header', 'footer', 'copyright'])):
                        
                        message = {
                            'sequence': i + 1,
                            'content': clean_text,
                            'sender_type': self.infer_sender_type(clean_text),
                            'extraction_method': 'text_block'
                        }
                        messages.append(message)
            
            # Process messages for ordering
            if messages:
                messages = self.process_message_ordering(messages)
            
        except Exception as e:
            print(f"      ❌ Error extracting messages from HTML: {e}")
        
        return messages
    
    def infer_sender_type(self, text):
        """Infer if message is from advisor or customer"""
        
        text_lower = text.lower()
        
        # Advisor patterns
        advisor_patterns = [
            'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',
            'let me', 'i can help', 'what i\'m getting', 'i\'m seeing',
            'the universe', 'your guides', 'i\'m picking up', 'i\'m feeling'
        ]
        
        # Customer patterns  
        customer_patterns = [
            'will i', 'should i', 'when will', 'what about', 'can you tell me',
            'i want to know', 'my question', 'help me understand', 'what do you see'
        ]
        
        advisor_score = sum(1 for pattern in advisor_patterns if pattern in text_lower)
        customer_score = sum(1 for pattern in customer_patterns if pattern in text_lower)
        
        if advisor_score > customer_score:
            return 'advisor'
        elif customer_score > advisor_score:
            return 'customer'
        else:
            return 'unknown'
    
    def process_message_ordering(self, messages):
        """Process message ordering to resolve issues"""
        
        # Sort by timestamp if available
        timestamped = [msg for msg in messages if msg.get('timestamp')]
        non_timestamped = [msg for msg in messages if not msg.get('timestamp')]
        
        if timestamped:
            try:
                timestamped.sort(key=lambda x: x.get('timestamp', ''))
                for i, msg in enumerate(timestamped):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = True
            except:
                for i, msg in enumerate(timestamped):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = False
        
        for i, msg in enumerate(non_timestamped):
            msg['sequence'] = len(timestamped) + i + 1
            msg['order_resolved'] = False
        
        return timestamped + non_timestamped
    
    def run_extraction(self):
        """Run the complete extraction process"""
        
        print("🚀 DIRECT CHAT TRANSCRIPT EXTRACTOR")
        print("=" * 60)
        print("🔐 Using your Keen.com authentication cookies")
        print("💬 Extracting chat transcripts via direct HTTP requests")
        print("🎯 Applying message ordering resolution")
        print("=" * 60)
        
        # Setup session
        if not self.setup_session():
            print("❌ Could not setup authenticated session")
            return
        
        # Load interactions
        interactions = self.load_interactions()
        
        # Extract chat transcripts
        print(f"\n💬 Extracting {len(interactions)} chat transcripts...")
        
        for i, interaction in enumerate(interactions, 1):
            customer_username = interaction['customer_username']
            interaction_id = interaction['interaction_id']
            
            print(f"\n👤 {i}/{len(interactions)}: {customer_username} (Chat {interaction_id})")
            
            try:
                transcript = self.extract_chat_transcript(interaction)
                
                if transcript:
                    customer_id = interaction['customer_id']
                    
                    if customer_id not in self.chat_transcripts:
                        self.chat_transcripts[customer_id] = {
                            'customer_info': {
                                'customer_id': customer_id,
                                'username': customer_username
                            },
                            'transcripts': []
                        }
                    
                    self.chat_transcripts[customer_id]['transcripts'].append(transcript)
                
            except Exception as e:
                error_msg = f"Error processing {interaction_id}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)
            
            # Rate limiting
            time.sleep(2)
        
        # Save results
        self.save_results()
    
    def save_results(self):
        """Save extraction results"""
        
        print(f"\n💾 Saving extraction results...")
        
        dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'direct_http_with_auth_cookies',
                'customers_with_transcripts': len(self.chat_transcripts),
                'total_transcripts': sum(len(c['transcripts']) for c in self.chat_transcripts.values()),
                'total_messages': sum(
                    sum(len(t.get('messages', [])) for t in c['transcripts']) 
                    for c in self.chat_transcripts.values()
                ),
                'errors': len(self.errors)
            },
            'chat_transcripts': self.chat_transcripts,
            'errors': self.errors
        }
        
        # Save to file
        results_file = Path("data/direct_chat_transcripts.json")
        with open(results_file, 'w') as f:
            json.dump(dataset, f, indent=2)
        
        print(f"💾 Results saved to: {results_file}")
        print(f"📊 Customers with transcripts: {len(self.chat_transcripts)}")
        print(f"💬 Total transcripts: {dataset['metadata']['total_transcripts']}")
        print(f"📝 Total messages: {dataset['metadata']['total_messages']}")
        print(f"❌ Errors: {len(self.errors)}")
        
        # Display sample results
        if self.chat_transcripts:
            print(f"\n📋 SAMPLE EXTRACTED CONVERSATIONS:")
            for i, (customer_id, data) in enumerate(list(self.chat_transcripts.items())[:3], 1):
                transcripts = data['transcripts']
                username = data['customer_info']['username']
                print(f"  {i}. Customer: {username} ({customer_id})")
                print(f"     Transcripts: {len(transcripts)}")
                
                if transcripts and transcripts[0].get('messages'):
                    sample_messages = transcripts[0]['messages'][:3]
                    for j, msg in enumerate(sample_messages, 1):
                        sender = msg.get('sender_type', 'unknown').title()
                        content = msg.get('content', '')[:50] + "..." if len(msg.get('content', '')) > 50 else msg.get('content', '')
                        print(f"       {j}. {sender}: {content}")
        
        print(f"\n🎉 DIRECT EXTRACTION COMPLETE!")
        
        return dataset

if __name__ == "__main__":
    extractor = DirectChatExtractor()
    extractor.run_extraction()
