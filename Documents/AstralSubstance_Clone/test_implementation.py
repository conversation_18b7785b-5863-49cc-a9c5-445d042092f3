#!/usr/bin/env python3
"""
Test Implementation Script

Validates the AstralSubstance Clone implementation by testing
core components and functionality.
"""

import sys
import os
from pathlib import Path
import tempfile
import json
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        # Core modules
        from keen_scraper.auth_manager import Keen<PERSON>uthManager, AuthenticationError
        from keen_scraper.graphql_client import GraphQLClient, GraphQLError
        from keen_scraper.data_extractor import KeenDataExtractor
        
        from google_voice.voice_processor import GoogleVoiceProcessor
        from data_correlation.correlator import DataCorrelator
        from dataset_builder.llm_formatter import LLMDatasetFormatter
        
        from models.database import (
            DatabaseManager, Customer, ChatSession, ChatMessage,
            CallLog, VoiceRecording, DatasetEntry
        )
        
        from utils.logger_setup import setup_logging
        from utils.config_loader import ConfigLoader
        
        print("✅ All imports successful")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


def test_config_loading():
    """Test configuration loading"""
    print("Testing configuration loading...")
    
    try:
        from utils.config_loader import ConfigLoader
        
        # Test creating default config
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            config_path = f.name
        
        ConfigLoader.create_default_config(config_path)
        
        # Test loading config
        config = ConfigLoader.load_config(config_path)
        
        # Validate required sections
        required_sections = ['keen', 'database', 'storage', 'correlation', 'dataset']
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing required config section: {section}")
        
        # Cleanup
        os.unlink(config_path)
        
        print("✅ Configuration loading successful")
        return True
        
    except Exception as e:
        print(f"❌ Configuration loading error: {e}")
        return False


def test_database_models():
    """Test database models and operations"""
    print("Testing database models...")
    
    try:
        from models.database import DatabaseManager, Customer, ChatSession, ChatMessage
        
        # Create in-memory database
        db_manager = DatabaseManager("sqlite:///:memory:")
        db_manager.create_tables()
        
        # Test creating customer
        with db_manager.get_session() as session:
            customer = Customer(
                keen_customer_id="test_123",
                username="test_user",
                nickname="Test User",
                customer_since=datetime.now(),
                total_call_count=5,
                total_chat_count=10,
                total_earnings=150.00
            )
            session.add(customer)
            session.commit()
            
            # Test querying
            retrieved_customer = session.query(Customer).filter(
                Customer.keen_customer_id == "test_123"
            ).first()
            
            if not retrieved_customer:
                raise ValueError("Failed to retrieve customer")
            
            # Test chat session
            chat_session = ChatSession(
                customer_id=customer.id,
                session_id="session_456",
                start_time=datetime.now(),
                message_count=3,
                session_type="chat"
            )
            session.add(chat_session)
            session.commit()
        
        print("✅ Database models test successful")
        return True
        
    except Exception as e:
        print(f"❌ Database models error: {e}")
        return False


def test_authentication_manager():
    """Test authentication manager (without actual API calls)"""
    print("Testing authentication manager...")
    
    try:
        from keen_scraper.auth_manager import KeenAuthManager
        
        # Create minimal config
        config = {
            'keen': {
                'base_url': 'https://www.keen.com',
                'graphql_endpoint': '/api/graphqlv0',
                'cookies': {},
                'rate_limiting': {
                    'base_delay': 1.0,
                    'max_retries': 3,
                    'backoff_factor': 2.0
                }
            },
            'security': {
                'encrypt_cookies': True,
                'encryption_key_file': tempfile.mktemp()
            }
        }
        
        # Initialize auth manager
        auth_manager = KeenAuthManager(config)
        
        # Test cookie update
        test_cookies = {
            'session_token': 'test_token_123',
            'csrf_token': 'test_csrf_456'
        }
        auth_manager.update_cookies(test_cookies)
        
        # Test session headers
        session = auth_manager.session
        if 'User-Agent' not in session.headers:
            raise ValueError("User-Agent header not set")
        
        print("✅ Authentication manager test successful")
        return True
        
    except Exception as e:
        print(f"❌ Authentication manager error: {e}")
        return False


def test_voice_processor():
    """Test voice processor (without actual audio files)"""
    print("Testing voice processor...")
    
    try:
        from google_voice.voice_processor import GoogleVoiceProcessor
        
        config = {
            'google_voice': {
                'recordings_directory': 'test_recordings',
                'supported_formats': ['.wav', '.mp3'],
                'audio': {
                    'sample_rate': 16000,
                    'normalize_audio': True
                }
            }
        }
        
        processor = GoogleVoiceProcessor(config)
        
        # Test timestamp extraction from filename
        test_file = Path("recording_2024-01-15_14-30-45.wav")
        timestamp = processor._extract_timestamp_from_file(test_file)
        
        if not isinstance(timestamp, datetime):
            raise ValueError("Failed to extract timestamp")
        
        print("✅ Voice processor test successful")
        return True
        
    except Exception as e:
        print(f"❌ Voice processor error: {e}")
        return False


def test_data_correlator():
    """Test data correlation logic"""
    print("Testing data correlator...")
    
    try:
        from data_correlation.correlator import DataCorrelator
        from models.database import DatabaseManager, CallLog, VoiceRecording
        
        config = {
            'correlation': {
                'timestamp_tolerance_minutes': 5,
                'duration_tolerance_percent': 10,
                'confidence_threshold': 0.6,
                'weights': {
                    'timestamp': 0.7,
                    'duration': 0.3
                }
            }
        }
        
        correlator = DataCorrelator(config)
        
        # Create test data
        base_time = datetime.now()
        
        # Mock call log
        class MockCallLog:
            def __init__(self):
                self.id = 1
                self.call_timestamp = base_time
                self.duration_seconds = 300
        
        # Mock recording
        class MockRecording:
            def __init__(self, time_offset=0, duration=300):
                self.id = 1
                self.timestamp = base_time + timedelta(seconds=time_offset)
                self.duration_seconds = duration
        
        from datetime import timedelta
        
        call_log = MockCallLog()
        recording = MockRecording(time_offset=30, duration=310)  # 30 seconds later, similar duration
        
        # Test correlation scoring
        score, time_diff, duration_diff = correlator._calculate_correlation_score(recording, call_log)
        
        if score <= 0:
            raise ValueError("Correlation score should be positive for similar recordings")
        
        print("✅ Data correlator test successful")
        return True
        
    except Exception as e:
        print(f"❌ Data correlator error: {e}")
        return False


def test_dataset_formatter():
    """Test dataset formatter"""
    print("Testing dataset formatter...")
    
    try:
        from dataset_builder.llm_formatter import LLMDatasetFormatter, ConversationEntry
        from datetime import datetime
        
        config = {
            'dataset': {
                'formats': ['conversational', 'instruction_following'],
                'output': {
                    'max_conversation_length': 10000,
                    'include_metadata': True,
                    'preserve_timestamps': True
                },
                'splits': {
                    'train': 0.8,
                    'validation': 0.1,
                    'test': 0.1
                }
            },
            'storage': {
                'datasets_dir': tempfile.mkdtemp()
            }
        }
        
        formatter = LLMDatasetFormatter(config)
        
        # Test conversation entry creation
        conversation = ConversationEntry(
            conversation_id="test_conv_123",
            customer_id="customer_456",
            session_type="chat",
            timestamp=datetime.now(),
            messages=[
                {"role": "user", "content": "Hello"},
                {"role": "assistant", "content": "Hi there!"}
            ],
            metadata={"test": "value"}
        )
        
        # Test data splitting
        test_data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        train, val, test = formatter._split_dataset(test_data)
        
        if len(train) + len(val) + len(test) != len(test_data):
            raise ValueError("Data split doesn't preserve total count")
        
        print("✅ Dataset formatter test successful")
        return True
        
    except Exception as e:
        print(f"❌ Dataset formatter error: {e}")
        return False


def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("ASTRALSUBSTANCE CLONE - IMPLEMENTATION TESTS")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_config_loading,
        test_database_models,
        test_authentication_manager,
        test_voice_processor,
        test_data_correlator,
        test_dataset_formatter
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"RESULTS: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 All tests passed! Implementation is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
