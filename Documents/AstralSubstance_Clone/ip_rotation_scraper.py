#!/usr/bin/env python3
"""
IP rotation scraper with multiple safe methods to change IP addresses
"""

import sys
from pathlib import Path
import json
import time
import random
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import requests
import subprocess

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class IPRotationScraper:
    """Scraper with IP rotation capabilities"""
    
    def __init__(self):
        self.driver = None
        self.current_ip = None
        self.ip_rotation_methods = []
        self.customers = {}
        self.chat_transcripts = {}
        self.errors = []
        self.progress_file = Path("data/ip_rotation_progress.json")
        
    def setup_ip_rotation(self):
        """Setup available IP rotation methods"""
        
        print("🔄 SETTING UP IP ROTATION METHODS")
        print("=" * 60)
        
        # Method 1: Check for Tor
        if self.check_tor_availability():
            self.ip_rotation_methods.append('tor')
            print("✅ Tor available for IP rotation")
        
        # Method 2: Check for VPN
        if self.check_vpn_availability():
            self.ip_rotation_methods.append('vpn')
            print("✅ VPN available for IP rotation")
        
        # Method 3: Check for proxy services
        if self.check_proxy_availability():
            self.ip_rotation_methods.append('proxy')
            print("✅ Proxy services available")
        
        # Method 4: Mobile hotspot rotation
        if self.check_mobile_hotspot():
            self.ip_rotation_methods.append('mobile')
            print("✅ Mobile hotspot rotation available")
        
        # Method 5: Network interface rotation
        if self.check_network_interfaces():
            self.ip_rotation_methods.append('interface')
            print("✅ Multiple network interfaces available")
        
        if not self.ip_rotation_methods:
            print("⚠️  No IP rotation methods available")
            print("💡 Recommendations:")
            print("   1. Install Tor Browser: sudo apt install tor")
            print("   2. Setup VPN connection")
            print("   3. Use mobile hotspot as backup")
            print("   4. Consider proxy services")
            return False
        
        print(f"🎯 Available methods: {', '.join(self.ip_rotation_methods)}")
        return True
    
    def check_tor_availability(self):
        """Check if Tor is available"""
        try:
            result = subprocess.run(['which', 'tor'], capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def check_vpn_availability(self):
        """Check if VPN is available"""
        try:
            # Check for common VPN interfaces
            result = subprocess.run(['ip', 'addr'], capture_output=True, text=True)
            vpn_interfaces = ['tun', 'tap', 'wg', 'vpn']
            return any(interface in result.stdout for interface in vpn_interfaces)
        except:
            return False
    
    def check_proxy_availability(self):
        """Check if proxy configuration is available"""
        # Check for proxy configuration in environment or config
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        return any(var in os.environ for var in proxy_vars)
    
    def check_mobile_hotspot(self):
        """Check if mobile hotspot is available"""
        try:
            # Check for wireless interfaces that could be mobile hotspots
            result = subprocess.run(['iwconfig'], capture_output=True, text=True)
            return 'wlan' in result.stdout or 'wlp' in result.stdout
        except:
            return False
    
    def check_network_interfaces(self):
        """Check for multiple network interfaces"""
        try:
            result = subprocess.run(['ip', 'addr', 'show'], capture_output=True, text=True)
            # Count active interfaces (excluding loopback)
            interfaces = [line for line in result.stdout.split('\n') if 'inet ' in line and '127.0.0.1' not in line]
            return len(interfaces) > 1
        except:
            return False
    
    def get_current_ip(self):
        """Get current external IP address"""
        try:
            response = requests.get('https://httpbin.org/ip', timeout=10)
            if response.status_code == 200:
                return response.json().get('origin', 'Unknown')
        except:
            pass
        
        try:
            response = requests.get('https://api.ipify.org', timeout=10)
            if response.status_code == 200:
                return response.text.strip()
        except:
            pass
        
        return 'Unknown'
    
    def rotate_ip(self):
        """Rotate IP address using available methods"""
        
        old_ip = self.get_current_ip()
        print(f"🔄 Current IP: {old_ip}")
        print(f"🔄 Rotating IP address...")
        
        for method in self.ip_rotation_methods:
            try:
                if method == 'tor':
                    success = self.rotate_via_tor()
                elif method == 'vpn':
                    success = self.rotate_via_vpn()
                elif method == 'proxy':
                    success = self.rotate_via_proxy()
                elif method == 'mobile':
                    success = self.rotate_via_mobile()
                elif method == 'interface':
                    success = self.rotate_via_interface()
                else:
                    continue
                
                if success:
                    new_ip = self.get_current_ip()
                    if new_ip != old_ip and new_ip != 'Unknown':
                        print(f"✅ IP rotated successfully: {old_ip} → {new_ip}")
                        self.current_ip = new_ip
                        return True
                    else:
                        print(f"⚠️  IP rotation via {method} didn't change IP")
                
            except Exception as e:
                print(f"❌ Error rotating IP via {method}: {e}")
        
        print(f"❌ Failed to rotate IP address")
        return False
    
    def rotate_via_tor(self):
        """Rotate IP via Tor network"""
        try:
            # Restart Tor service to get new circuit
            subprocess.run(['sudo', 'systemctl', 'restart', 'tor'], check=True)
            time.sleep(10)  # Wait for Tor to establish new circuit
            
            # Send NEWNYM signal to get new identity
            subprocess.run(['sudo', '-u', 'debian-tor', 'kill', '-HUP', '$(cat /var/run/tor/tor.pid)'], shell=True)
            time.sleep(5)
            
            return True
        except:
            return False
    
    def rotate_via_vpn(self):
        """Rotate IP via VPN reconnection"""
        try:
            # Disconnect VPN
            subprocess.run(['sudo', 'killall', 'openvpn'], capture_output=True)
            time.sleep(5)
            
            # Reconnect VPN (assuming OpenVPN config exists)
            vpn_configs = list(Path('/etc/openvpn').glob('*.conf'))
            if vpn_configs:
                config = random.choice(vpn_configs)
                subprocess.Popen(['sudo', 'openvpn', str(config)])
                time.sleep(15)  # Wait for VPN to connect
                return True
        except:
            pass
        
        return False
    
    def rotate_via_proxy(self):
        """Rotate IP via proxy switching"""
        # This would require a list of proxy servers
        # For now, just return False as it needs external proxy list
        return False
    
    def rotate_via_mobile(self):
        """Rotate IP via mobile hotspot reconnection"""
        try:
            # Disconnect from current WiFi
            subprocess.run(['nmcli', 'device', 'disconnect', 'wlan0'], capture_output=True)
            time.sleep(5)
            
            # Reconnect to mobile hotspot
            # This assumes mobile hotspot is configured
            result = subprocess.run(['nmcli', 'connection', 'show'], capture_output=True, text=True)
            mobile_connections = [line.split()[0] for line in result.stdout.split('\n') 
                                if 'mobile' in line.lower() or 'hotspot' in line.lower()]
            
            if mobile_connections:
                connection = random.choice(mobile_connections)
                subprocess.run(['nmcli', 'connection', 'up', connection], capture_output=True)
                time.sleep(10)
                return True
        except:
            pass
        
        return False
    
    def rotate_via_interface(self):
        """Rotate IP via network interface switching"""
        try:
            # Get available interfaces
            result = subprocess.run(['ip', 'route', 'show', 'default'], capture_output=True, text=True)
            current_interface = result.stdout.split('dev')[1].split()[0] if 'dev' in result.stdout else None
            
            # Get all interfaces
            result = subprocess.run(['ip', 'addr', 'show'], capture_output=True, text=True)
            interfaces = []
            for line in result.stdout.split('\n'):
                if 'inet ' in line and '127.0.0.1' not in line:
                    interface = line.split()[-1] if 'scope global' in line else None
                    if interface and interface != current_interface:
                        interfaces.append(interface)
            
            if interfaces:
                new_interface = random.choice(interfaces)
                # Switch default route
                subprocess.run(['sudo', 'ip', 'route', 'del', 'default'], capture_output=True)
                subprocess.run(['sudo', 'ip', 'route', 'add', 'default', 'dev', new_interface], capture_output=True)
                time.sleep(5)
                return True
        except:
            pass
        
        return False
    
    def setup_browser_with_rotation(self):
        """Setup browser with IP rotation support"""
        
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Add proxy if Tor is available
        if 'tor' in self.ip_rotation_methods:
            chrome_options.add_argument("--proxy-server=socks5://127.0.0.1:9050")
        
        # Add random user agent
        user_agents = [
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
        ]
        chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return True
    
    def safe_scraping_with_rotation(self):
        """Perform scraping with IP rotation and safety measures"""
        
        print("🛡️  SAFE SCRAPING WITH IP ROTATION")
        print("=" * 60)
        
        # Setup IP rotation
        if not self.setup_ip_rotation():
            print("❌ Cannot proceed without IP rotation capabilities")
            return
        
        # Load customer data
        if not self.load_customer_data():
            return
        
        # Setup browser
        if not self.setup_browser_with_rotation():
            return
        
        # Load progress
        progress = self.load_progress()
        completed_customers = set(progress.get('completed_customers', []))
        
        # Filter customers with contact history
        target_customers = [
            (cid, cdata) for cid, cdata in self.customers.items()
            if cdata.get('contacts', {}).get('last') and cid not in completed_customers
        ]
        
        print(f"🎯 Processing {len(target_customers)} remaining customers")
        
        # Process customers with IP rotation
        batch_size = 10  # Rotate IP every 10 customers
        
        for i, (customer_id, customer_data) in enumerate(target_customers):
            # Rotate IP every batch_size customers
            if i % batch_size == 0 and i > 0:
                print(f"\n🔄 Rotating IP after {batch_size} customers...")
                
                # Close current browser
                if self.driver:
                    self.driver.quit()
                
                # Rotate IP
                if self.rotate_ip():
                    # Wait before continuing
                    wait_time = random.randint(30, 60)
                    print(f"⏱️  Waiting {wait_time} seconds before continuing...")
                    time.sleep(wait_time)
                    
                    # Setup new browser
                    self.setup_browser_with_rotation()
                    self.authenticate_browser()
                else:
                    print("⚠️  IP rotation failed, continuing with current IP")
            
            # Process customer
            username = customer_data.get('userName', 'Unknown')
            print(f"\n👤 Customer {i+1}/{len(target_customers)}: {username} (ID: {customer_id})")
            
            try:
                # Extract chat transcripts for this customer
                transcripts = self.extract_customer_chat_transcripts(customer_id, customer_data)
                
                if transcripts:
                    self.chat_transcripts[customer_id] = {
                        'customer_info': customer_data,
                        'transcripts': transcripts,
                        'transcript_count': len(transcripts),
                        'extraction_timestamp': datetime.now().isoformat(),
                        'ip_address': self.current_ip
                    }
                    
                    print(f"    ✅ Extracted {len(transcripts)} chat transcripts")
                
                # Save progress
                completed_customers.add(customer_id)
                self.save_progress(list(completed_customers))
                
            except Exception as e:
                error_msg = f"Error extracting chats for customer {customer_id}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)
            
            # Random delay between customers
            delay = random.randint(3, 8)
            time.sleep(delay)
        
        # Save final results
        self.save_chat_data()
        
        # Cleanup
        if self.driver:
            self.driver.quit()
    
    def authenticate_browser(self):
        """Authenticate browser with Keen.com"""
        
        try:
            config = ConfigLoader.load_config("config/config.yaml")
            keen_config = config['keen']
            
            # Navigate to Keen.com
            self.driver.get(keen_config['base_url'])
            time.sleep(3)
            
            # Set authentication cookies
            cookies = keen_config['cookies']
            for name, value in cookies.items():
                if value and not value.startswith('YOUR_'):
                    self.driver.add_cookie({
                        'name': name,
                        'value': value,
                        'domain': '.keen.com'
                    })
            
            # Refresh to apply cookies
            self.driver.refresh()
            time.sleep(2)
            
            return True
            
        except Exception as e:
            print(f"❌ Error authenticating browser: {e}")
            return False
    
    def load_customer_data(self):
        """Load customer data"""
        
        customer_file = Path("data/graphql_complete_customers.json")
        
        if not customer_file.exists():
            print(f"❌ Customer data file not found: {customer_file}")
            return False
        
        try:
            with open(customer_file, 'r') as f:
                data = json.load(f)
            
            self.customers = data.get('customers', {})
            print(f"✅ Loaded {len(self.customers)} customers")
            return True
            
        except Exception as e:
            print(f"❌ Error loading customer data: {e}")
            return False
    
    def extract_customer_chat_transcripts(self, customer_id, customer_data):
        """Extract chat transcripts for a customer (simplified version)"""
        
        transcripts = []
        
        try:
            # Navigate to customer page
            customer_url = f"https://www.keen.com/app/#/myaccount/customers/{customer_id}"
            self.driver.get(customer_url)
            time.sleep(4)
            
            # Extract interaction IDs (simplified)
            page_source = self.driver.page_source
            
            # Look for chat interaction IDs
            import re
            chat_ids = re.findall(r'\b(138\d{5})\b', page_source)  # Chat IDs typically start with 138
            
            # Limit to first 3 chats to avoid overloading
            for chat_id in chat_ids[:3]:
                transcript = self.extract_single_chat_transcript(chat_id)
                if transcript:
                    transcripts.append(transcript)
                
                time.sleep(2)  # Delay between chats
        
        except Exception as e:
            print(f"      ❌ Error extracting customer chats: {e}")
        
        return transcripts
    
    def extract_single_chat_transcript(self, chat_id):
        """Extract a single chat transcript"""
        
        try:
            chat_url = f"https://www.keen.com/myaccount/transactions/chat-details?id={chat_id}"
            self.driver.get(chat_url)
            time.sleep(3)
            
            # Simple message extraction
            messages = []
            
            # Look for message elements
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, 'p, div, .message')
                for element in elements:
                    text = element.text.strip()
                    if text and len(text) > 10:
                        messages.append({
                            'content': text,
                            'sender_type': 'unknown',  # Simplified
                            'extraction_timestamp': datetime.now().isoformat()
                        })
            except:
                pass
            
            if messages:
                return {
                    'chat_id': chat_id,
                    'chat_url': chat_url,
                    'messages': messages[:10],  # Limit messages
                    'message_count': len(messages),
                    'extraction_timestamp': datetime.now().isoformat()
                }
        
        except Exception as e:
            print(f"        ❌ Error extracting chat {chat_id}: {e}")
        
        return None
    
    def load_progress(self):
        """Load progress"""
        try:
            if self.progress_file.exists():
                with open(self.progress_file, 'r') as f:
                    return json.load(f)
        except:
            pass
        return {}
    
    def save_progress(self, completed_customers):
        """Save progress"""
        try:
            progress = {
                'completed_customers': completed_customers,
                'total_completed': len(completed_customers),
                'timestamp': datetime.now().isoformat(),
                'current_ip': self.current_ip
            }
            with open(self.progress_file, 'w') as f:
                json.dump(progress, f, indent=2)
        except:
            pass
    
    def save_chat_data(self):
        """Save extracted chat data"""
        
        print(f"\n💾 Saving chat data...")
        
        chat_dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'customers_with_chats': len(self.chat_transcripts),
                'total_chat_transcripts': sum(len(t['transcripts']) for t in self.chat_transcripts.values()),
                'extraction_method': 'ip_rotation_safe_scraping',
                'ip_rotation_methods': self.ip_rotation_methods
            },
            'chat_transcripts': self.chat_transcripts,
            'errors': self.errors
        }
        
        chat_file = Path("data/ip_rotated_chat_transcripts.json")
        with open(chat_file, 'w') as f:
            json.dump(chat_dataset, f, indent=2)
        
        print(f"💾 Chat data saved to: {chat_file}")
        
        metadata = chat_dataset['metadata']
        print(f"📊 Extracted transcripts for {metadata['customers_with_chats']} customers")
        print(f"💬 Total transcripts: {metadata['total_chat_transcripts']}")

if __name__ == "__main__":
    import os
    
    scraper = IPRotationScraper()
    scraper.safe_scraping_with_rotation()
