#!/usr/bin/env python3
"""
Extract customers using the exact headers and authentication from browser automation
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON><PERSON><PERSON>

def extract_with_browser_headers():
    """Extract customers using browser-identical headers and authentication"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Use exact headers from successful browser automation
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Uid': '',
        'X-Domain-ID': '1',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://www.keen.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Priority': 'u=0',
        'TE': 'trailers'
    }
    
    base_url = keen_config['base_url']
    
    print("🔐 EXTRACTING WITH BROWSER-IDENTICAL AUTHENTICATION")
    print("=" * 60)
    
    # First, test the working customer-lists endpoint
    print("\n📋 Step 1: Testing the working customer-lists endpoint...")
    test_working_endpoint(session, headers, base_url)
    
    # Then try to access individual customer lists
    print("\n👥 Step 2: Attempting to access individual customer lists...")
    extract_from_specific_lists(session, headers, base_url)
    
    # Try alternative approaches
    print("\n🔍 Step 3: Trying alternative customer access methods...")
    try_alternative_methods(session, headers, base_url)

def test_working_endpoint(session, headers, base_url):
    """Test the endpoint we know works"""
    
    endpoint = f"{base_url}/api/advisors/56392386/customer-lists"
    
    try:
        response = session.get(endpoint, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Customer lists endpoint working!")
            print(f"  📊 Found {len(data.get('customerLists', []))} customer lists")
            
            # Show the lists again
            for customer_list in data.get('customerLists', []):
                list_id = customer_list.get('id')
                list_name = customer_list.get('name')
                count = customer_list.get('customerCount', 0)
                list_type = customer_list.get('type')
                print(f"    📋 {list_name} (ID: {list_id}, Type: {list_type}): {count} customers")
            
            return data.get('customerLists', [])
        else:
            print(f"  ❌ Failed: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return []

def extract_from_specific_lists(session, headers, base_url):
    """Try to extract from specific customer lists using various approaches"""
    
    # Focus on the largest list first
    target_lists = [
        {'id': 5591276, 'name': 'All Customers NEW', 'count': 3861},
        {'id': 5576887, 'name': 'New Customers', 'count': 336},
    ]
    
    for customer_list in target_lists:
        list_id = customer_list['id']
        list_name = customer_list['name']
        expected_count = customer_list['count']
        
        print(f"\n  📋 Targeting: {list_name} ({expected_count} customers)")
        
        # Try different endpoint patterns with exact browser headers
        endpoint_patterns = [
            f"/api/advisors/56392386/customer-lists/{list_id}/customers",
            f"/api/customer-lists/{list_id}/customers", 
            f"/api/customer-lists/{list_id}",
            f"/api/advisors/56392386/customers",  # Try without list filter first
        ]
        
        for endpoint in endpoint_patterns:
            print(f"    🧪 Testing: {endpoint}")
            
            customers = test_endpoint_with_browser_auth(session, headers, base_url, endpoint, list_id, expected_count)
            
            if customers:
                print(f"    🎉 SUCCESS! Extracted {len(customers)} customers from {list_name}")
                
                # Save the successful extraction
                save_customer_extraction(customers, list_name, endpoint)
                
                if len(customers) >= expected_count * 0.8:  # Got most of the expected customers
                    return customers
                    
        print(f"    ❌ Could not access customers from {list_name}")
    
    return []

def test_endpoint_with_browser_auth(session, headers, base_url, endpoint, list_id=None, expected_count=0):
    """Test endpoint with browser-identical authentication"""
    
    url = f"{base_url}{endpoint}"
    
    # Try different parameter combinations
    param_combinations = [
        {},  # No parameters
        {'listId': list_id} if list_id else {},
        {'customerListId': list_id} if list_id else {},
        {'limit': expected_count} if expected_count > 0 else {'limit': 1000},
        {'pageSize': expected_count} if expected_count > 0 else {'pageSize': 1000},
        {'limit': 5000},  # Try large limit
        {'pageSize': 5000},
        {'all': True},
        {'includeAll': True},
    ]
    
    # Also try POST requests
    for params in param_combinations:
        # Try GET
        try:
            response = session.get(url, headers=headers, params=params, timeout=30)
            
            if response.status_code == 200 and response.content:
                customers = process_response(response, params, 'GET')
                if customers:
                    return customers
            elif response.status_code == 401:
                print(f"      ⚠️  GET 401 (Unauthorized) with params: {params}")
            elif response.status_code not in [404, 405]:
                print(f"      ⚠️  GET {response.status_code} with params: {params}")
                
        except Exception as e:
            pass
        
        # Try POST
        try:
            response = session.post(url, headers=headers, json=params, timeout=30)
            
            if response.status_code == 200 and response.content:
                customers = process_response(response, params, 'POST')
                if customers:
                    return customers
            elif response.status_code == 401:
                print(f"      ⚠️  POST 401 (Unauthorized) with data: {params}")
            elif response.status_code not in [404, 405]:
                print(f"      ⚠️  POST {response.status_code} with data: {params}")
                
        except Exception as e:
            pass
        
        time.sleep(0.5)
    
    return []

def process_response(response, params, method):
    """Process a successful response to extract customers"""
    
    try:
        data = response.json()
        customers = extract_customers_from_data(data)
        
        if customers:
            print(f"      ✅ {method} with {params}: Found {len(customers)} customers")
            return customers
        else:
            print(f"      ℹ️  {method} with {params}: No customers in response")
            
    except json.JSONDecodeError:
        print(f"      ⚠️  {method} with {params}: Non-JSON response")
    
    return []

def extract_customers_from_data(data):
    """Extract customer data from response"""
    
    customers = []
    
    def search_customers(obj, depth=0):
        if depth > 5:
            return
            
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'customer' in key.lower() and isinstance(value, list):
                    for customer in value:
                        if isinstance(customer, dict) and (customer.get('id') or customer.get('customerId')):
                            customers.append(customer)
                elif key.lower() in ['data', 'results', 'items', 'members', 'users'] and isinstance(value, list):
                    if value and isinstance(value[0], dict):
                        first_item = value[0]
                        customer_fields = ['id', 'customerId', 'userName', 'email', 'name', 'nickname']
                        if any(field in first_item for field in customer_fields):
                            customers.extend(value)
                        else:
                            search_customers(value, depth + 1)
                elif isinstance(value, (dict, list)):
                    search_customers(value, depth + 1)
        elif isinstance(obj, list):
            if obj and isinstance(obj[0], dict):
                first_item = obj[0]
                customer_fields = ['id', 'customerId', 'userName', 'email', 'name', 'nickname']
                if any(field in first_item for field in customer_fields):
                    customers.extend(obj)
                else:
                    for item in obj:
                        search_customers(item, depth + 1)
    
    search_customers(data)
    
    # Remove duplicates
    unique_customers = {}
    for customer in customers:
        customer_id = customer.get('id') or customer.get('customerId')
        if customer_id:
            unique_customers[customer_id] = customer
    
    return list(unique_customers.values())

def try_alternative_methods(session, headers, base_url):
    """Try alternative methods to access customer data"""
    
    print("  🔍 Trying alternative customer access methods...")
    
    # Try the GraphQL endpoint that we know works for reviews
    print("    🧪 Testing GraphQL endpoint for customer data...")
    try_graphql_customer_queries(session, headers, base_url)
    
    # Try session-based customer discovery
    print("    🧪 Testing session-based customer discovery...")
    try_session_based_discovery(session, headers, base_url)

def try_graphql_customer_queries(session, headers, base_url):
    """Try GraphQL queries for customer data"""
    
    graphql_url = f"{base_url}/api/graphql2"
    
    # Try different GraphQL queries that might return customer data
    queries = [
        {
            'name': 'advisor customers',
            'query': """query($advisorId:Int){
                advisor(id:$advisorId) {
                    customers {
                        id
                        userName
                        nickname
                        emailAddress
                    }
                }
            }""",
            'variables': {'advisorId': 56392386}
        },
        {
            'name': 'user customers',
            'query': """query {
                user {
                    customers {
                        id
                        userName
                        nickname
                        emailAddress
                    }
                }
            }""",
            'variables': {}
        }
    ]
    
    for query_info in queries:
        print(f"      🧪 Testing GraphQL: {query_info['name']}")
        
        try:
            response = session.post(graphql_url, headers=headers, json={
                'query': query_info['query'],
                'variables': query_info['variables']
            }, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' not in data and 'data' in data:
                    customers = extract_customers_from_data(data['data'])
                    if customers:
                        print(f"        ✅ Found {len(customers)} customers via GraphQL!")
                        return customers
                else:
                    print(f"        ❌ GraphQL error: {data.get('errors', 'Unknown error')}")
            else:
                print(f"        ❌ GraphQL failed: {response.status_code}")
                
        except Exception as e:
            print(f"        ❌ GraphQL error: {e}")
    
    return []

def try_session_based_discovery(session, headers, base_url):
    """Try to discover customers through session data"""
    
    # This would involve using the session IDs we already have from reviews
    # to find related customer data
    print("      ℹ️  Session-based discovery would require existing session IDs")
    print("      📋 Could use the 145 customers from reviews as starting points")
    
    return []

def save_customer_extraction(customers, list_name, endpoint):
    """Save successful customer extraction"""
    
    filename = f"data/customers_extracted_{list_name.replace(' ', '_')}.json"
    
    extraction_data = {
        'list_name': list_name,
        'endpoint': endpoint,
        'customer_count': len(customers),
        'extraction_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'customers': customers
    }
    
    with open(filename, 'w') as f:
        json.dump(extraction_data, f, indent=2)
    
    print(f"      💾 Saved to: {filename}")

if __name__ == "__main__":
    extract_with_browser_headers()
