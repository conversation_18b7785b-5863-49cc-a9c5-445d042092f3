#!/usr/bin/env python3
"""
Extract ALL customer data using the discovered GraphQL fields
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON><PERSON><PERSON>

def extract_all_customers():
    """Extract ALL customer data using the correct GraphQL fields"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
    }
    
    base_url = keen_config['base_url']
    graphql_url = f"{base_url}/api/graphql"
    
    print("🚀 EXTRACTING ALL CUSTOMER DATA")
    print("=" * 60)
    
    # Extract customers using the discovered fields
    print("\n👥 Step 1: Extracting customers via currentUser.customers...")
    customers_data = extract_customers_via_current_user(session, headers, graphql_url)
    
    print("\n📋 Step 2: Extracting customer lists via currentUser.customerLists...")
    customer_lists_data = extract_customer_lists(session, headers, graphql_url)
    
    # Combine and analyze results
    print("\n📊 Step 3: Analyzing extracted data...")
    analyze_customer_data(customers_data, customer_lists_data)

def extract_customers_via_current_user(session, headers, graphql_url):
    """Extract customers via currentUser.customers field"""
    
    # Start with a basic query to see the structure
    basic_query = {
        "query": """
        query {
            currentUser {
                customers {
                    totalCount
                    pageInfo {
                        hasNextPage
                        hasPreviousPage
                        startCursor
                        endCursor
                    }
                    edges {
                        cursor
                        node {
                            id
                            customerId
                            userName
                            nickname
                            emailAddress
                            lastContactDate
                            since
                            pastEarnings {
                                amount
                                displayAmount
                            }
                            ratingsAverage
                            comments
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=basic_query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Error: {data['errors'][0]['message']}")
                return {}
            elif 'data' in data and data['data']['currentUser']['customers']:
                customers = data['data']['currentUser']['customers']
                
                total_count = customers.get('totalCount', 0)
                edges = customers.get('edges', [])
                page_info = customers.get('pageInfo', {})
                
                print(f"  ✅ Found {total_count} total customers!")
                print(f"  📄 Current page: {len(edges)} customers")
                print(f"  🔄 Has next page: {page_info.get('hasNextPage', False)}")
                
                # Show sample customer data
                if edges:
                    sample_customer = edges[0]['node']
                    print(f"  👤 Sample customer:")
                    for key, value in sample_customer.items():
                        if value is not None:
                            print(f"    {key}: {value}")
                
                # If there are more pages, get them all
                all_customers = edges.copy()
                
                if page_info.get('hasNextPage') and total_count > len(edges):
                    print(f"\n  🔄 Fetching remaining {total_count - len(edges)} customers...")
                    remaining_customers = fetch_all_customer_pages(session, headers, graphql_url, page_info.get('endCursor'))
                    all_customers.extend(remaining_customers)
                
                print(f"  🎉 Total customers extracted: {len(all_customers)}")
                
                # Save customer data
                customer_data = {
                    'totalCount': total_count,
                    'customers': [edge['node'] for edge in all_customers]
                }
                
                with open('data/all_customers.json', 'w') as f:
                    json.dump(customer_data, f, indent=2)
                print(f"  💾 Customer data saved to: data/all_customers.json")
                
                return customer_data
            else:
                print(f"  ❌ No customer data returned")
                return {}
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {}

def fetch_all_customer_pages(session, headers, graphql_url, start_cursor):
    """Fetch all remaining customer pages"""
    
    all_customers = []
    current_cursor = start_cursor
    page_num = 2
    
    while current_cursor:
        print(f"    📄 Fetching page {page_num}...")
        
        paginated_query = {
            "query": """
            query($after: String) {
                currentUser {
                    customers(after: $after) {
                        pageInfo {
                            hasNextPage
                            endCursor
                        }
                        edges {
                            cursor
                            node {
                                id
                                customerId
                                userName
                                nickname
                                emailAddress
                                lastContactDate
                                since
                                pastEarnings {
                                    amount
                                    displayAmount
                                }
                                ratingsAverage
                                comments
                            }
                        }
                    }
                }
            }
            """,
            "variables": {"after": current_cursor}
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=paginated_query, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"      ❌ Error on page {page_num}: {data['errors'][0]['message']}")
                    break
                elif 'data' in data and data['data']['currentUser']['customers']:
                    customers = data['data']['currentUser']['customers']
                    edges = customers.get('edges', [])
                    page_info = customers.get('pageInfo', {})
                    
                    print(f"      ✅ Page {page_num}: {len(edges)} customers")
                    all_customers.extend(edges)
                    
                    # Check if there are more pages
                    if page_info.get('hasNextPage'):
                        current_cursor = page_info.get('endCursor')
                        page_num += 1
                    else:
                        break
                else:
                    print(f"      ❌ No data on page {page_num}")
                    break
            else:
                print(f"      ❌ HTTP error on page {page_num}: {response.status_code}")
                break
                
        except Exception as e:
            print(f"      ❌ Error on page {page_num}: {e}")
            break
        
        time.sleep(1)  # Rate limiting
    
    return all_customers

def extract_customer_lists(session, headers, graphql_url):
    """Extract customer lists via currentUser.customerLists field"""
    
    customer_lists_query = {
        "query": """
        query {
            currentUser {
                customerLists {
                    totalCount
                    edges {
                        node {
                            id
                            name
                            description
                            customers {
                                totalCount
                                edges {
                                    node {
                                        id
                                        customerId
                                        userName
                                        nickname
                                        emailAddress
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=customer_lists_query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Error: {data['errors'][0]['message']}")
                return {}
            elif 'data' in data and data['data']['currentUser']['customerLists']:
                customer_lists = data['data']['currentUser']['customerLists']
                
                total_lists = customer_lists.get('totalCount', 0)
                list_edges = customer_lists.get('edges', [])
                
                print(f"  ✅ Found {total_lists} customer lists!")
                
                for list_edge in list_edges:
                    list_node = list_edge['node']
                    list_name = list_node.get('name', 'Unnamed')
                    list_customers = list_node.get('customers', {})
                    customer_count = list_customers.get('totalCount', 0)
                    
                    print(f"    📋 List: {list_name} ({customer_count} customers)")
                
                # Save customer lists data
                with open('data/customer_lists.json', 'w') as f:
                    json.dump(customer_lists, f, indent=2)
                print(f"  💾 Customer lists saved to: data/customer_lists.json")
                
                return customer_lists
            else:
                print(f"  ❌ No customer lists returned")
                return {}
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {}

def analyze_customer_data(customers_data, customer_lists_data):
    """Analyze the extracted customer data"""
    
    print(f"📊 CUSTOMER DATA ANALYSIS")
    print("=" * 40)
    
    if customers_data:
        customers = customers_data.get('customers', [])
        total_count = customers_data.get('totalCount', 0)
        
        print(f"👥 Total customers: {total_count}")
        print(f"📄 Customers extracted: {len(customers)}")
        
        # Analyze customer data
        if customers:
            # Count customers with different data
            with_email = sum(1 for c in customers if c.get('emailAddress'))
            with_nickname = sum(1 for c in customers if c.get('nickname'))
            with_earnings = sum(1 for c in customers if c.get('pastEarnings', {}).get('amount'))
            with_ratings = sum(1 for c in customers if c.get('ratingsAverage'))
            with_comments = sum(1 for c in customers if c.get('comments'))
            
            print(f"\n📊 Data completeness:")
            print(f"  Email addresses: {with_email}/{len(customers)} ({with_email/len(customers)*100:.1f}%)")
            print(f"  Nicknames: {with_nickname}/{len(customers)} ({with_nickname/len(customers)*100:.1f}%)")
            print(f"  Earnings data: {with_earnings}/{len(customers)} ({with_earnings/len(customers)*100:.1f}%)")
            print(f"  Ratings: {with_ratings}/{len(customers)} ({with_ratings/len(customers)*100:.1f}%)")
            print(f"  Comments: {with_comments}/{len(customers)} ({with_comments/len(customers)*100:.1f}%)")
            
            # Show top customers by earnings
            customers_with_earnings = [c for c in customers if c.get('pastEarnings', {}).get('amount')]
            if customers_with_earnings:
                top_customers = sorted(customers_with_earnings, 
                                     key=lambda x: float(x['pastEarnings']['amount']), 
                                     reverse=True)[:5]
                
                print(f"\n💰 Top 5 customers by earnings:")
                for i, customer in enumerate(top_customers, 1):
                    name = customer.get('userName', 'Unknown')
                    earnings = customer['pastEarnings']['displayAmount']
                    print(f"  {i}. {name}: {earnings}")
    
    if customer_lists_data:
        lists = customer_lists_data.get('edges', [])
        print(f"\n📋 Customer lists: {len(lists)}")
        
        for list_edge in lists:
            list_node = list_edge['node']
            print(f"  - {list_node.get('name', 'Unnamed')}: {list_node.get('customers', {}).get('totalCount', 0)} customers")
    
    print(f"\n🎉 EXTRACTION COMPLETE!")
    print(f"All customer data has been successfully extracted and saved!")

if __name__ == "__main__":
    extract_all_customers()
