#!/usr/bin/env python3
"""
Investigate Keen.com pagination and data structure to scrape ALL records
"""

import sys
from pathlib import Path
import requests
import json
import time
from urllib.parse import urljoin

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def investigate_keen_pagination():
    """Investigate how to paginate through ALL Keen.com records"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers from working curl request
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Uid': '',
        'X-Domain-ID': '1',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://www.keen.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.keen.com/app/myaccount/feedback',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Priority': 'u=0',
        'TE': 'trailers'
    }
    
    base_url = keen_config['base_url']
    graphql_url = f"{base_url}{keen_config['graphql_endpoint']}"
    
    print("🔍 INVESTIGATING KEEN.COM PAGINATION")
    print("=" * 60)
    
    # Step 1: Check the total number of reviews available
    print("\n📊 Step 1: Checking total reviews available...")
    total_reviews = check_total_reviews(session, headers, graphql_url)
    
    # Step 2: Test different page sizes
    print(f"\n📄 Step 2: Testing pagination (Total reviews: {total_reviews})...")
    test_pagination_limits(session, headers, graphql_url, total_reviews)
    
    # Step 3: Explore other potential endpoints
    print("\n🔍 Step 3: Exploring other potential data endpoints...")
    explore_other_endpoints(session, headers, base_url)
    
    # Step 4: Check for customer-specific endpoints
    print("\n👥 Step 4: Looking for customer-specific endpoints...")
    explore_customer_endpoints(session, headers, base_url)
    
    # Step 5: Test maximum pagination
    print("\n🚀 Step 5: Testing maximum pagination...")
    test_maximum_pagination(session, headers, graphql_url, total_reviews)

def check_total_reviews(session, headers, graphql_url):
    """Check the total number of reviews available"""
    
    query_data = {
        "query": """query($advisorId:Int $listingId:Int){
            ratingsAndReviews(advisorId:$advisorId listingId:$listingId pageSize:1 pageNumber:1){
                totalEdges
                averageRating
            }
        }""",
        "variables": {
            "listingId": "12471990",
            "advisorId": 56392386
        }
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
        if response.status_code == 200:
            data = response.json()
            total_edges = data['data']['ratingsAndReviews']['totalEdges']
            avg_rating = data['data']['ratingsAndReviews']['averageRating']
            print(f"  ✅ Total reviews available: {total_edges}")
            print(f"  ⭐ Average rating: {avg_rating:.2f}")
            return total_edges
        else:
            print(f"  ❌ Failed to get total: {response.status_code}")
            return 0
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return 0

def test_pagination_limits(session, headers, graphql_url, total_reviews):
    """Test different page sizes and pagination limits"""
    
    page_sizes = [50, 100, 200, 500, 1000]
    
    for page_size in page_sizes:
        print(f"\n  Testing page size: {page_size}")
        
        query_data = {
            "query": """query($advisorId:Int $listingId:Int $pageSize:Int $pageNumber:Int){
                ratingsAndReviews(advisorId:$advisorId listingId:$listingId pageSize:$pageSize pageNumber:$pageNumber){
                    totalEdges
                    edges{
                        node{
                            id
                            date
                            target{
                                source{
                                    customer{
                                        id
                                        userName
                                    }
                                }
                            }
                        }
                    }
                }
            }""",
            "variables": {
                "listingId": "12471990",
                "advisorId": 56392386,
                "pageSize": str(page_size),
                "pageNumber": 1
            }
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
            if response.status_code == 200:
                data = response.json()
                edges = data['data']['ratingsAndReviews']['edges']
                print(f"    ✅ Page size {page_size}: Got {len(edges)} records")
                
                # Check if we can get unique customers
                customers = set()
                for edge in edges:
                    customer = edge['node']['target']['source']['customer']
                    if customer and customer.get('id'):
                        customers.add(customer['id'])
                
                print(f"    👥 Unique customers in this page: {len(customers)}")
                
                # Test if we can get more pages
                if len(edges) == page_size and page_size < total_reviews:
                    print(f"    📄 Testing page 2...")
                    query_data['variables']['pageNumber'] = 2
                    response2 = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
                    if response2.status_code == 200:
                        data2 = response2.json()
                        edges2 = data2['data']['ratingsAndReviews']['edges']
                        print(f"    ✅ Page 2: Got {len(edges2)} records")
                    else:
                        print(f"    ❌ Page 2 failed: {response2.status_code}")
                
            else:
                print(f"    ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(1)  # Rate limiting

def explore_other_endpoints(session, headers, base_url):
    """Explore other potential endpoints for more data"""
    
    # Common endpoints to try
    endpoints_to_try = [
        '/api/graphql',      # Alternative GraphQL endpoint
        '/api/graphqlv1',    # Version 1
        '/api/graphqlv3',    # Version 3
        '/api/customers',    # Direct customers endpoint
        '/api/conversations', # Conversations endpoint
        '/api/sessions',     # Sessions endpoint
        '/api/calls',        # Calls endpoint
        '/api/chats',        # Chats endpoint
    ]
    
    for endpoint in endpoints_to_try:
        url = urljoin(base_url, endpoint)
        print(f"  Testing: {url}")
        
        try:
            # Try GET first
            response = session.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                print(f"    ✅ GET {endpoint}: {response.status_code} - {len(response.content)} bytes")
                
                # Check if it's JSON
                try:
                    data = response.json()
                    print(f"    📄 JSON response with keys: {list(data.keys())[:5]}")
                except:
                    print(f"    📄 Non-JSON response")
            else:
                print(f"    ❌ GET {endpoint}: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error testing {endpoint}: {e}")
        
        time.sleep(0.5)

def explore_customer_endpoints(session, headers, base_url):
    """Look for customer-specific endpoints"""
    
    # Try to access customer management pages
    customer_urls = [
        '/app/myaccount/customers',
        '/app/myaccount/customer-list',
        '/app/myaccount/customer-history',
        '/app/myaccount/conversations',
        '/app/myaccount/calls',
        '/app/myaccount/sessions',
    ]
    
    for url_path in customer_urls:
        url = urljoin(base_url, url_path)
        print(f"  Testing: {url}")
        
        try:
            response = session.get(url, headers=headers, timeout=15)
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                print(f"    ✅ Success - Content length: {len(content)}")
                
                # Look for signs of customer data
                if 'customer' in content.lower():
                    print(f"    👥 Contains 'customer' references")
                if 'graphql' in content.lower():
                    print(f"    🔍 Contains GraphQL references")
                if 'api' in content.lower():
                    print(f"    🔗 Contains API references")
                    
                # Look for JavaScript that might reveal API endpoints
                if 'fetch(' in content or 'axios' in content or 'xhr' in content:
                    print(f"    📡 Contains AJAX/fetch calls")
                    
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(1)

def test_maximum_pagination(session, headers, graphql_url, total_reviews):
    """Test how far we can paginate to get ALL data"""
    
    print(f"  🎯 Attempting to extract ALL {total_reviews} reviews...")
    
    page_size = 100  # Use a reasonable page size
    max_pages = (total_reviews // page_size) + 1
    
    print(f"  📊 Plan: {max_pages} pages of {page_size} records each")
    
    all_customers = set()
    successful_pages = 0
    
    # Test first few pages and last few pages
    pages_to_test = [1, 2, 3, max_pages-2, max_pages-1, max_pages]
    pages_to_test = [p for p in pages_to_test if p > 0 and p <= max_pages]
    
    for page_num in pages_to_test:
        print(f"\n    Testing page {page_num}/{max_pages}...")
        
        query_data = {
            "query": """query($advisorId:Int $listingId:Int $pageSize:Int $pageNumber:Int){
                ratingsAndReviews(advisorId:$advisorId listingId:$listingId pageSize:$pageSize pageNumber:$pageNumber){
                    edges{
                        node{
                            id
                            date
                            target{
                                source{
                                    customer{
                                        id
                                        userName
                                        nickname
                                    }
                                }
                            }
                        }
                    }
                }
            }""",
            "variables": {
                "listingId": "12471990",
                "advisorId": 56392386,
                "pageSize": str(page_size),
                "pageNumber": page_num
            }
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
            if response.status_code == 200:
                data = response.json()
                edges = data['data']['ratingsAndReviews']['edges']
                
                page_customers = set()
                for edge in edges:
                    customer = edge['node']['target']['source']['customer']
                    if customer and customer.get('id'):
                        page_customers.add(customer['id'])
                        all_customers.add(customer['id'])
                
                print(f"      ✅ Page {page_num}: {len(edges)} reviews, {len(page_customers)} unique customers")
                successful_pages += 1
                
            else:
                print(f"      ❌ Page {page_num} failed: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Page {page_num} error: {e}")
        
        time.sleep(1)  # Rate limiting
    
    print(f"\n  📊 PAGINATION SUMMARY:")
    print(f"    Successful pages tested: {successful_pages}/{len(pages_to_test)}")
    print(f"    Total unique customers found: {len(all_customers)}")
    print(f"    Estimated total customers: {len(all_customers) * max_pages // len(pages_to_test)}")
    
    if successful_pages == len(pages_to_test):
        print(f"  🎉 PAGINATION WORKING! Can extract all {total_reviews} reviews")
        return True
    else:
        print(f"  ⚠️  Some pagination issues detected")
        return False

if __name__ == "__main__":
    investigate_keen_pagination()
