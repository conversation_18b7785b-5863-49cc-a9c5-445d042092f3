# Quick Start Guide

## Prerequisites

1. **Python 3.8+** installed
2. **Keen.com account** with active session
3. **Google Voice recordings** (optional)
4. **Browser** for cookie extraction

## Installation

```bash
# Clone or download the project
cd AstralSubstance_Clone

# Install dependencies
pip install -r requirements.txt

# Create configuration
cp config/config.example.yaml config/config.yaml
```

## Configuration Setup

### 1. Extract Keen.com Cookies

1. Open your browser and log into Keen.com
2. Navigate to your customer list page
3. Open Developer Tools (F12)
4. Go to Network tab and refresh the page
5. Find a request to `/api/graphqlv0`
6. Copy the entire `Cookie` header value

Example Cookie header:
```
session_token=abc123def456; csrf_token=xyz789; other_cookie=value123
```

### 2. Update Configuration

Edit `config/config.yaml`:

```yaml
keen:
  cookies:
    session_token: "your_session_token_here"
    csrf_token: "your_csrf_token_here"
    # Add other cookies as key-value pairs
```

### 3. Setup Google Voice Authentication

Extract Google Voice cookies from your browser:

1. Open voice.google.com and log in
2. Navigate to your calls page
3. Open Developer Tools (F12)
4. Go to Network tab and refresh the page
5. Find a request to voice.google.com
6. Copy the entire `Cookie` header value

Example Cookie header:
```
SID=g.a000...; HSID=AKewvW3...; SSID=ATZZbJJ...; APISID=eUyqAoSM...; SAPISID=VgMcw9RP...; ...
```

Update `config/config.yaml`:
```yaml
google_voice:
  cookies:
    SID: "your_sid_value_here"
    HSID: "your_hsid_value_here"
    SSID: "your_ssid_value_here"
    APISID: "your_apisid_value_here"
    SAPISID: "your_sapisid_value_here"
    # Add all other cookies from the header
```

## Quick Test Run

```bash
# Test Keen.com authentication
python src/main.py setup-auth

# Test Google Voice authentication
python src/main.py setup-gvoice-auth

# Extract sample data (10 customers)
python src/main.py run-pipeline --sample

# Check status
python src/main.py status
```

## Full Pipeline

```bash
# Run complete extraction and dataset building
python src/main.py run-pipeline

# Or run steps individually:
python src/main.py extract-keen
python src/main.py scrape-gvoice  # Scrape Google Voice recordings
python src/main.py correlate
python src/main.py build-datasets

# Alternative: Process local audio files
python src/main.py process-voice --recordings-dir /path/to/local/recordings
```

## Output Files

Generated datasets will be in `data/datasets/`:

- `train_conversational_dataset.jsonl` - Training conversations
- `validation_conversational_dataset.jsonl` - Validation conversations
- `test_conversational_dataset.jsonl` - Test conversations
- `train_instruction_dataset.jsonl` - Training instructions
- `validation_instruction_dataset.jsonl` - Validation instructions
- `test_instruction_dataset.jsonl` - Test instructions

## Dataset Formats

### Conversational Format
```json
{
  "conversation_id": "keen_chat_12345",
  "customer_id": "56771410",
  "session_type": "chat",
  "timestamp": "2024-01-15T14:30:00Z",
  "messages": [
    {
      "role": "user",
      "content": "I need help with my relationship",
      "timestamp": "2024-01-15T14:30:00Z"
    },
    {
      "role": "assistant",
      "content": "I understand you're going through a difficult time...",
      "timestamp": "2024-01-15T14:30:15Z"
    }
  ],
  "metadata": {
    "duration_minutes": 45,
    "customer_since": "2023-06-01",
    "advisor_earnings": 67.50,
    "call_recording_available": true
  }
}
```

### Instruction Format
```json
{
  "instruction": "You are a psychic advisor helping a customer with relationship concerns...",
  "input": "I'm worried my partner is losing interest in me.",
  "output": "I can sense the uncertainty you're feeling...",
  "metadata": {
    "conversation_id": "keen_chat_12345",
    "session_type": "chat",
    "timestamp": "2024-01-15T14:30:00Z"
  }
}
```

## Troubleshooting

### Authentication Issues
- Ensure cookies are current (they expire)
- Check that all required cookies are included
- Verify you're logged into Keen.com in the same browser

### No Data Extracted
- Check your Keen.com account has customer data
- Verify API endpoints haven't changed
- Enable debug mode in config for detailed logs

### Voice Processing Errors
- Ensure audio files are in supported formats
- Check file permissions and paths
- Install additional audio libraries if needed:
  ```bash
  pip install librosa soundfile
  ```

### Memory Issues
- Reduce batch_size in config
- Process in sample mode first
- Monitor system resources

## Advanced Usage

### Custom Queries
Modify GraphQL queries in `src/keen_scraper/graphql_client.py`

### Custom Correlation
Adjust correlation parameters in config:
```yaml
correlation:
  timestamp_tolerance_minutes: 5
  duration_tolerance_percent: 10
  confidence_threshold: 0.6
```

### Custom Dataset Formats
Extend `src/dataset_builder/llm_formatter.py` for new formats

## Support

- Check logs in `logs/astral_substance.log`
- Review configuration in `config/config.yaml`
- Examine database with SQLite browser: `data/astral_substance.db`

## Security Notes

- Cookies are encrypted and stored locally
- No data is transmitted outside your system
- Review terms of service compliance
- Consider data privacy implications
