#!/usr/bin/env python3
"""
Visual exploration of the customer interface with screenshots
SAFETY: READ-ONLY exploration with visual documentation
"""

import sys
from pathlib import Path
import json
import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

def visual_exploration():
    """Visual exploration with screenshots"""
    
    print("📸 VISUAL EXPLORATION WITH SCREENSHOTS")
    print("=" * 60)
    print("⚠️  SAFETY MODE: READ-ONLY EXPLORATION ONLY")
    print("⚠️  TAKING SCREENSHOTS FOR ANALYSIS")
    print("=" * 60)
    
    # Create screenshots directory
    screenshots_dir = Path("data/screenshots")
    screenshots_dir.mkdir(exist_ok=True)
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup browser
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--force-device-scale-factor=1')
    
    driver = None
    
    try:
        # Start browser
        print("\n🌐 Starting browser...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Set authentication
        print("🔐 Setting authentication...")
        setup_authentication(driver, keen_config)
        
        # Navigate to customer page
        print("🌐 Navigating to customer management page...")
        customer_url = f"{keen_config['base_url']}/app/myaccount/customers"
        driver.get(customer_url)
        
        # Wait for page load
        wait = WebDriverWait(driver, 15)
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        time.sleep(5)
        
        print("✅ Customer page loaded!")
        
        # Take initial screenshot
        take_screenshot(driver, screenshots_dir, "01_initial_customer_page", "Initial customer management page")
        
        # Analyze and screenshot customer elements
        print("\n👥 ANALYZING CUSTOMER ELEMENTS...")
        analyze_customer_elements_with_screenshots(driver, screenshots_dir)
        
        # Look for pagination
        print("\n📄 LOOKING FOR PAGINATION...")
        analyze_pagination_with_screenshots(driver, screenshots_dir)
        
        # Try clicking on first customer
        print("\n🖱️  TESTING CUSTOMER INTERACTION...")
        test_customer_interaction_with_screenshots(driver, screenshots_dir)
        
        # Generate summary
        print("\n📊 GENERATING VISUAL SUMMARY...")
        generate_visual_summary(screenshots_dir)
        
        print(f"\n📸 All screenshots saved to: {screenshots_dir}")
        print("🔍 You can now review the screenshots to understand the interface structure")
        
    except Exception as e:
        print(f"❌ Error during visual exploration: {e}")
        if driver:
            take_screenshot(driver, screenshots_dir, "error_screenshot", f"Error state: {e}")
    finally:
        if driver:
            print("🔒 Closing browser...")
            driver.quit()

def setup_authentication(driver, keen_config):
    """Setup authentication cookies"""
    
    base_url = keen_config['base_url']
    driver.get(base_url)
    time.sleep(2)
    
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            try:
                driver.add_cookie({
                    'name': name,
                    'value': value,
                    'domain': '.keen.com'
                })
            except Exception as e:
                print(f"  ⚠️  Failed to add cookie {name}: {e}")

def take_screenshot(driver, screenshots_dir, filename, description):
    """Take a screenshot with description"""
    
    try:
        screenshot_path = screenshots_dir / f"{filename}.png"
        driver.save_screenshot(str(screenshot_path))
        
        # Save description
        desc_path = screenshots_dir / f"{filename}.txt"
        with open(desc_path, 'w') as f:
            f.write(f"Screenshot: {filename}\n")
            f.write(f"Description: {description}\n")
            f.write(f"URL: {driver.current_url}\n")
            f.write(f"Title: {driver.title}\n")
            f.write(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"  📸 Screenshot saved: {filename}.png - {description}")
        
    except Exception as e:
        print(f"  ❌ Failed to take screenshot {filename}: {e}")

def analyze_customer_elements_with_screenshots(driver, screenshots_dir):
    """Analyze customer elements and take screenshots"""
    
    # Look for different types of customer elements
    element_types = [
        ('.list-item', 'list_items'),
        ('a[href*="customer"]', 'customer_links'),
        ('tbody tr', 'table_rows'),
        ('[data-id]', 'data_elements'),
        ('.customer', 'customer_class'),
        ('.user', 'user_class'),
    ]
    
    for selector, name in element_types:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            print(f"  📋 Found {len(elements)} elements with selector: {selector}")
            
            if elements:
                # Highlight first few elements and take screenshot
                highlight_elements_and_screenshot(driver, screenshots_dir, elements[:5], 
                                                f"02_{name}_highlighted", 
                                                f"Highlighted {name} elements ({len(elements)} total)")
                
                # Take detailed screenshot of first element
                if elements:
                    scroll_to_element_and_screenshot(driver, screenshots_dir, elements[0],
                                                   f"03_{name}_detail",
                                                   f"Detailed view of first {name} element")
        except Exception as e:
            print(f"  ❌ Error analyzing {selector}: {e}")

def highlight_elements_and_screenshot(driver, screenshots_dir, elements, filename, description):
    """Highlight elements and take screenshot"""
    
    try:
        # Add highlighting style
        highlight_script = """
        arguments[0].style.border = '3px solid red';
        arguments[0].style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
        """
        
        # Highlight elements
        for element in elements:
            try:
                driver.execute_script(highlight_script, element)
            except:
                pass
        
        time.sleep(1)
        take_screenshot(driver, screenshots_dir, filename, description)
        
        # Remove highlighting
        remove_highlight_script = """
        arguments[0].style.border = '';
        arguments[0].style.backgroundColor = '';
        """
        
        for element in elements:
            try:
                driver.execute_script(remove_highlight_script, element)
            except:
                pass
                
    except Exception as e:
        print(f"  ❌ Error highlighting elements: {e}")

def scroll_to_element_and_screenshot(driver, screenshots_dir, element, filename, description):
    """Scroll to element and take screenshot"""
    
    try:
        # Scroll element into view
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
        time.sleep(1)
        
        # Highlight the element
        driver.execute_script("""
            arguments[0].style.border = '3px solid blue';
            arguments[0].style.backgroundColor = 'rgba(0, 0, 255, 0.1)';
        """, element)
        
        time.sleep(1)
        take_screenshot(driver, screenshots_dir, filename, description)
        
        # Remove highlighting
        driver.execute_script("""
            arguments[0].style.border = '';
            arguments[0].style.backgroundColor = '';
        """, element)
        
    except Exception as e:
        print(f"  ❌ Error scrolling to element: {e}")

def analyze_pagination_with_screenshots(driver, screenshots_dir):
    """Look for pagination elements and take screenshots"""
    
    pagination_selectors = [
        '.pagination',
        '.pager',
        '.page-nav',
        'button[aria-label*="next"]',
        'button[aria-label*="page"]',
        '.next',
        '.previous',
        '.load-more',
        'button:contains("Load More")',
        'a:contains("Next")',
        '[data-page]',
    ]
    
    pagination_found = []
    
    for selector in pagination_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"  📄 Found {len(elements)} pagination elements: {selector}")
                pagination_found.extend(elements)
        except:
            pass
    
    if pagination_found:
        # Remove duplicates
        unique_pagination = []
        seen_elements = set()
        
        for elem in pagination_found:
            elem_id = id(elem)
            if elem_id not in seen_elements:
                unique_pagination.append(elem)
                seen_elements.add(elem_id)
        
        print(f"  📊 Total unique pagination elements: {len(unique_pagination)}")
        
        # Highlight pagination elements
        highlight_elements_and_screenshot(driver, screenshots_dir, unique_pagination,
                                        "04_pagination_highlighted",
                                        f"Highlighted pagination elements ({len(unique_pagination)} found)")
        
        # Take detailed screenshots of each pagination element
        for i, elem in enumerate(unique_pagination[:3]):  # First 3 only
            scroll_to_element_and_screenshot(driver, screenshots_dir, elem,
                                           f"05_pagination_detail_{i+1}",
                                           f"Pagination element {i+1} detail")
    else:
        print("  ℹ️  No pagination elements found")
        take_screenshot(driver, screenshots_dir, "04_no_pagination", "No pagination elements found")

def test_customer_interaction_with_screenshots(driver, screenshots_dir):
    """Test clicking on customer elements with screenshots"""
    
    # Find potential customer elements
    customer_selectors = [
        '.list-item',
        'a[href*="customer"]',
        'tbody tr',
        '[data-id]'
    ]
    
    clickable_customers = []
    
    for selector in customer_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            for element in elements:
                if is_likely_customer_element(element):
                    clickable_customers.append(element)
                    if len(clickable_customers) >= 3:  # Limit to first 3
                        break
            if len(clickable_customers) >= 3:
                break
        except:
            pass
    
    if clickable_customers:
        print(f"  🖱️  Found {len(clickable_customers)} clickable customer elements")
        
        # Test clicking on first customer
        test_element = clickable_customers[0]
        
        # Take screenshot before click
        scroll_to_element_and_screenshot(driver, screenshots_dir, test_element,
                                       "06_before_customer_click",
                                       "Before clicking on customer element")
        
        # Get current URL
        original_url = driver.current_url
        
        try:
            # Try clicking
            test_element.click()
            time.sleep(3)
            
            # Take screenshot after click
            take_screenshot(driver, screenshots_dir, "07_after_customer_click",
                          f"After clicking customer element (URL: {driver.current_url})")
            
            # Check if URL changed
            new_url = driver.current_url
            if new_url != original_url:
                print(f"    ✅ Click successful! URL changed from {original_url} to {new_url}")
                
                # Take screenshot of customer detail page
                time.sleep(2)
                take_screenshot(driver, screenshots_dir, "08_customer_detail_page",
                              "Customer detail page after successful click")
                
                # Look for conversation/session elements
                analyze_customer_detail_page_with_screenshots(driver, screenshots_dir)
                
                # Navigate back
                driver.back()
                time.sleep(2)
                take_screenshot(driver, screenshots_dir, "09_back_to_customer_list",
                              "Back to customer list after viewing detail")
                
            else:
                print(f"    ⚠️  Click didn't change URL")
                
        except Exception as e:
            print(f"    ❌ Error clicking customer element: {e}")
            take_screenshot(driver, screenshots_dir, "07_customer_click_error",
                          f"Error clicking customer element: {e}")
    else:
        print("  ❌ No clickable customer elements found")

def is_likely_customer_element(element):
    """Check if element is likely a customer"""
    
    try:
        # Must be visible and enabled
        if not (element.is_displayed() and element.is_enabled()):
            return False
        
        # Must have some text or be clickable
        text = element.text.strip()
        is_clickable = (
            element.get_attribute('onclick') or
            element.get_attribute('href') or
            element.tag_name.lower() in ['a', 'button']
        )
        
        if not (text or is_clickable):
            return False
        
        # Skip obvious non-customer elements
        if text:
            text_lower = text.lower()
            skip_texts = ['customer', 'name', 'email', 'date', 'action', 'edit', 'delete', 'save', 'cancel']
            if any(skip in text_lower for skip in skip_texts) and len(text) < 20:
                return False
        
        return True
        
    except:
        return False

def analyze_customer_detail_page_with_screenshots(driver, screenshots_dir):
    """Analyze customer detail page with screenshots"""
    
    print("    📋 Analyzing customer detail page...")
    
    # Look for conversation/session elements
    conversation_selectors = [
        '.conversation',
        '.session',
        '.chat',
        '.call',
        '.message',
        '.conversation-item',
        '.session-item',
        'table',
        '.data-table',
        '.history',
        '.transcript'
    ]
    
    conversation_elements = []
    
    for selector in conversation_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"      💬 Found {len(elements)} conversation elements: {selector}")
                conversation_elements.extend(elements)
        except:
            pass
    
    if conversation_elements:
        # Remove duplicates and highlight
        unique_conversations = []
        seen_elements = set()
        
        for elem in conversation_elements:
            elem_id = id(elem)
            if elem_id not in seen_elements:
                unique_conversations.append(elem)
                seen_elements.add(elem_id)
        
        highlight_elements_and_screenshot(driver, screenshots_dir, unique_conversations[:5],
                                        "10_conversations_highlighted",
                                        f"Highlighted conversation elements ({len(unique_conversations)} found)")
    else:
        print("      ℹ️  No conversation elements found")

def generate_visual_summary(screenshots_dir):
    """Generate a summary of all screenshots taken"""
    
    summary = {
        'exploration_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'screenshots': []
    }
    
    # List all screenshots
    for file_path in sorted(screenshots_dir.glob("*.png")):
        filename = file_path.stem
        
        # Try to read description
        desc_path = screenshots_dir / f"{filename}.txt"
        description = "No description"
        
        if desc_path.exists():
            try:
                with open(desc_path, 'r') as f:
                    lines = f.readlines()
                    for line in lines:
                        if line.startswith('Description:'):
                            description = line.replace('Description:', '').strip()
                            break
            except:
                pass
        
        summary['screenshots'].append({
            'filename': f"{filename}.png",
            'description': description
        })
    
    # Save summary
    summary_path = screenshots_dir / "visual_summary.json"
    with open(summary_path, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"  📊 Visual summary saved: {summary_path}")
    print(f"  📸 Total screenshots taken: {len(summary['screenshots'])}")

if __name__ == "__main__":
    visual_exploration()
