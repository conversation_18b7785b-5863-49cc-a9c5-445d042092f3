#!/usr/bin/env python3
"""
Comprehensive strategy to extract ALL 4,297 customers using multiple approaches
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON><PERSON><PERSON>

def extract_all_customers_comprehensive():
    """Use multiple strategies to extract all customers"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Uid': '',
        'X-Domain-ID': '1',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://www.keen.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.keen.com/app/myaccount/feedback',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Priority': 'u=0',
        'TE': 'trailers'
    }
    
    base_url = keen_config['base_url']
    
    print("🚀 COMPREHENSIVE CUSTOMER EXTRACTION STRATEGY")
    print("=" * 60)
    
    # Strategy 1: Extract from reviews (we know this works)
    print("\n📊 Strategy 1: Extract customers from ALL reviews...")
    review_customers = extract_from_reviews(session, headers, base_url)
    
    # Strategy 2: Try to find other GraphQL queries that might have more customers
    print("\n🔍 Strategy 2: Test alternative GraphQL queries...")
    graphql_customers = test_alternative_graphql_queries(session, headers, base_url)
    
    # Strategy 3: Try to find pagination or different endpoints
    print("\n📄 Strategy 3: Test pagination and alternative endpoints...")
    pagination_customers = test_pagination_strategies(session, headers, base_url)
    
    # Strategy 4: Analyze the web interface for hidden endpoints
    print("\n🌐 Strategy 4: Analyze web interface for hidden endpoints...")
    hidden_customers = analyze_web_interface(session, headers, base_url)
    
    # Combine all results
    print("\n📊 Combining all extraction results...")
    all_customers = combine_customer_data(review_customers, graphql_customers, pagination_customers, hidden_customers)
    
    # Final analysis
    print("\n🎯 Final analysis and recommendations...")
    final_analysis(all_customers)

def extract_from_reviews(session, headers, base_url):
    """Extract customers from all reviews (we know this works)"""
    
    graphql_url = f"{base_url}/api/graphql2"
    
    # We already know there are 361 reviews, let's get them all
    page_size = 100
    total_pages = 4  # 361 reviews / 100 per page = 4 pages
    
    all_customers = {}
    
    print(f"  📄 Extracting from {total_pages} pages of reviews...")
    
    for page_num in range(1, total_pages + 1):
        print(f"    Page {page_num}/{total_pages}...")
        
        query_data = {
            "query": """query($advisorId:Int $listingId:Int $pageSize:Int $pageNumber:Int){
                ratingsAndReviews(advisorId:$advisorId listingId:$listingId pageSize:$pageSize pageNumber:$pageNumber){
                    totalEdges
                    edges{
                        node{
                            id
                            rating
                            review
                            date
                            target{
                                type
                                source{
                                    id
                                    sessionExists
                                    masterTransactionId
                                    customer{
                                        id
                                        userName
                                        nickname
                                    }
                                }
                            }
                        }
                    }
                }
            }""",
            "variables": {
                "listingId": "12471990",
                "advisorId": 56392386,
                "pageSize": str(page_size),
                "pageNumber": page_num
            }
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and 'ratingsAndReviews' in data['data']:
                    reviews = data['data']['ratingsAndReviews']
                    edges = reviews.get('edges', [])
                    
                    print(f"      ✅ Got {len(edges)} reviews")
                    
                    # Extract customers
                    for edge in edges:
                        review_node = edge.get('node', {})
                        target = review_node.get('target', {})
                        source = target.get('source', {})
                        customer_data = source.get('customer', {})
                        
                        if customer_data and customer_data.get('id'):
                            customer_id = customer_data['id']
                            
                            if customer_id not in all_customers:
                                all_customers[customer_id] = {
                                    'id': customer_id,
                                    'userName': customer_data.get('userName'),
                                    'nickname': customer_data.get('nickname'),
                                    'session_ids': [],
                                    'transaction_ids': [],
                                    'reviews': []
                                }
                            
                            # Add session and transaction data
                            if source.get('id'):
                                all_customers[customer_id]['session_ids'].append(source['id'])
                            if source.get('masterTransactionId'):
                                all_customers[customer_id]['transaction_ids'].append(source['masterTransactionId'])
                            
                            # Add review data
                            all_customers[customer_id]['reviews'].append({
                                'review_id': review_node.get('id'),
                                'rating': review_node.get('rating'),
                                'date': review_node.get('date')
                            })
                    
                    print(f"      👥 Total unique customers so far: {len(all_customers)}")
                    
        except Exception as e:
            print(f"      ❌ Error on page {page_num}: {e}")
        
        time.sleep(1)
    
    print(f"  🎉 Extracted {len(all_customers)} customers from reviews")
    return all_customers

def test_alternative_graphql_queries(session, headers, base_url):
    """Test alternative GraphQL queries that might have more customers"""
    
    graphql_url = f"{base_url}/api/graphql2"
    
    # Try different queries that might return customer data
    alternative_queries = [
        {
            'name': 'user query',
            'query': """query { user { id userName customers { id userName } } }"""
        },
        {
            'name': 'advisor sessions',
            'query': """query($advisorId:Int){ advisor(id:$advisorId) { sessions { id customer { id userName } } } }""",
            'variables': {"advisorId": 56392386}
        },
        {
            'name': 'all sessions',
            'query': """query { sessions { id customer { id userName } advisor { id } } }"""
        },
        {
            'name': 'transactions',
            'query': """query { transactions { id customer { id userName } } }"""
        }
    ]
    
    customers_found = {}
    
    for query_info in alternative_queries:
        print(f"    Testing: {query_info['name']}")
        
        query_data = {
            "query": query_info['query'],
            "variables": query_info.get('variables', {})
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"      ❌ Error: {data['errors'][0]['message']}")
                elif 'data' in data:
                    print(f"      ✅ Success!")
                    
                    # Extract any customer data from the response
                    extracted = extract_customers_from_response(data['data'])
                    if extracted:
                        customers_found.update(extracted)
                        print(f"      👥 Found {len(extracted)} customers")
                    else:
                        print(f"      ℹ️  No customer data in response")
            else:
                print(f"      ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
        
        time.sleep(1)
    
    print(f"  📊 Alternative queries found {len(customers_found)} additional customers")
    return customers_found

def test_pagination_strategies(session, headers, base_url):
    """Test different pagination strategies"""
    
    graphql_url = f"{base_url}/api/graphql2"
    
    # Try to get more reviews by testing higher page numbers
    print(f"    Testing higher page numbers...")
    
    customers_found = {}
    
    # Test pages beyond what we know exists
    for page_num in range(5, 20):  # Test pages 5-19
        print(f"      Testing page {page_num}...")
        
        query_data = {
            "query": """query($advisorId:Int $listingId:Int $pageSize:Int $pageNumber:Int){
                ratingsAndReviews(advisorId:$advisorId listingId:$listingId pageSize:$pageSize pageNumber:$pageNumber){
                    totalEdges
                    edges{
                        node{
                            target{
                                source{
                                    customer{
                                        id
                                        userName
                                        nickname
                                    }
                                }
                            }
                        }
                    }
                }
            }""",
            "variables": {
                "listingId": "12471990",
                "advisorId": 56392386,
                "pageSize": "100",
                "pageNumber": page_num
            }
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and 'ratingsAndReviews' in data['data']:
                    reviews = data['data']['ratingsAndReviews']
                    edges = reviews.get('edges', [])
                    
                    if edges:
                        print(f"        ✅ Found {len(edges)} more reviews!")
                        
                        # Extract customers from these additional reviews
                        for edge in edges:
                            customer_data = edge.get('node', {}).get('target', {}).get('source', {}).get('customer', {})
                            if customer_data and customer_data.get('id'):
                                customer_id = customer_data['id']
                                customers_found[customer_id] = {
                                    'id': customer_id,
                                    'userName': customer_data.get('userName'),
                                    'nickname': customer_data.get('nickname'),
                                    'source': f'pagination_page_{page_num}'
                                }
                    else:
                        print(f"        ℹ️  No more reviews on page {page_num}")
                        break  # No more pages
                        
        except Exception as e:
            print(f"        ❌ Error on page {page_num}: {e}")
            break
        
        time.sleep(1)
    
    print(f"  📄 Pagination found {len(customers_found)} additional customers")
    return customers_found

def analyze_web_interface(session, headers, base_url):
    """Analyze the web interface for hidden endpoints"""
    
    # This is a placeholder - in a real scenario, we would:
    # 1. Monitor network traffic while using the web interface
    # 2. Look for AJAX calls that load customer data
    # 3. Reverse engineer the web app's API calls
    
    print(f"    📋 Web interface analysis would require:")
    print(f"      - Browser automation to trigger customer loading")
    print(f"      - Network monitoring to capture API calls")
    print(f"      - JavaScript analysis to find hidden endpoints")
    print(f"    ℹ️  This is beyond the scope of this script")
    
    return {}

def extract_customers_from_response(data):
    """Extract customer data from any GraphQL response"""
    
    customers = {}
    
    def search_for_customers(obj, path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                
                if key == 'customer' and isinstance(value, dict) and value.get('id'):
                    customer_id = value['id']
                    customers[customer_id] = {
                        'id': customer_id,
                        'userName': value.get('userName'),
                        'nickname': value.get('nickname'),
                        'source': f'graphql_{path}'
                    }
                elif key == 'customers' and isinstance(value, list):
                    for customer in value:
                        if isinstance(customer, dict) and customer.get('id'):
                            customer_id = customer['id']
                            customers[customer_id] = {
                                'id': customer_id,
                                'userName': customer.get('userName'),
                                'nickname': customer.get('nickname'),
                                'source': f'graphql_{path}'
                            }
                elif isinstance(value, (dict, list)):
                    search_for_customers(value, current_path)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                search_for_customers(item, f"{path}[{i}]")
    
    search_for_customers(data)
    return customers

def combine_customer_data(*customer_sources):
    """Combine customer data from multiple sources"""
    
    combined = {}
    
    for source in customer_sources:
        if source:
            for customer_id, customer_data in source.items():
                if customer_id not in combined:
                    combined[customer_id] = customer_data
                else:
                    # Merge data from multiple sources
                    existing = combined[customer_id]
                    
                    # Update fields if they're missing
                    for key, value in customer_data.items():
                        if key not in existing or not existing[key]:
                            existing[key] = value
                        elif key in ['session_ids', 'transaction_ids', 'reviews'] and isinstance(value, list):
                            # Merge lists
                            if key not in existing:
                                existing[key] = []
                            existing[key].extend(value)
    
    return combined

def final_analysis(all_customers):
    """Final analysis and recommendations"""
    
    print(f"📊 FINAL EXTRACTION RESULTS")
    print("=" * 50)
    
    print(f"👥 Total unique customers extracted: {len(all_customers)}")
    
    if all_customers:
        # Analyze data quality
        with_username = sum(1 for c in all_customers.values() if c.get('userName'))
        with_nickname = sum(1 for c in all_customers.values() if c.get('nickname'))
        with_sessions = sum(1 for c in all_customers.values() if c.get('session_ids'))
        
        print(f"📊 Data quality:")
        print(f"  Customers with username: {with_username}/{len(all_customers)} ({with_username/len(all_customers)*100:.1f}%)")
        print(f"  Customers with nickname: {with_nickname}/{len(all_customers)} ({with_nickname/len(all_customers)*100:.1f}%)")
        print(f"  Customers with session data: {with_sessions}/{len(all_customers)} ({with_sessions/len(all_customers)*100:.1f}%)")
        
        # Save the data
        output_data = {
            'total_customers': len(all_customers),
            'extraction_methods': ['reviews', 'graphql_alternatives', 'pagination', 'web_interface'],
            'customers': list(all_customers.values())
        }
        
        with open('data/all_customers_comprehensive.json', 'w') as f:
            json.dump(output_data, f, indent=2)
        print(f"💾 All customer data saved to: data/all_customers_comprehensive.json")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    
    if len(all_customers) < 1000:
        print(f"⚠️  Only extracted {len(all_customers)} customers out of expected 4,297")
        print(f"📋 To get ALL customers, you may need to:")
        print(f"  1. Use browser automation to monitor the actual web interface")
        print(f"  2. Find additional API endpoints through network monitoring")
        print(f"  3. Contact Keen.com support for API documentation")
        print(f"  4. Use different authentication or access levels")
    else:
        print(f"✅ Successfully extracted a significant number of customers!")
        print(f"🚀 Ready for Google Voice correlation and transcription!")

if __name__ == "__main__":
    extract_all_customers_comprehensive()
