#!/usr/bin/env python3
"""
Extract ALL customer data using the correct approach - currentUser is an Advisor
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON>oa<PERSON>

def extract_customers_final():
    """Extract ALL customer data using the correct GraphQL approach"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
    }
    
    base_url = keen_config['base_url']
    graphql_url = f"{base_url}/api/graphql"
    
    print("🚀 EXTRACTING ALL CUSTOMER DATA (FINAL)")
    print("=" * 60)
    
    # First, confirm currentUser is an Advisor
    print("\n🔍 Step 1: Confirming user type...")
    user_info = check_user_type(session, headers, graphql_url)
    
    if user_info and user_info.get('__typename') == 'Advisor':
        print("\n👥 Step 2: Extracting customers from Advisor...")
        customers_data = extract_advisor_customers(session, headers, graphql_url)
        
        print("\n📋 Step 3: Extracting customer lists from Advisor...")
        customer_lists_data = extract_advisor_customer_lists(session, headers, graphql_url)
        
        print("\n📊 Step 4: Final analysis...")
        analyze_final_data(customers_data, customer_lists_data)
    else:
        print("❌ User is not an Advisor type, cannot access customer data")

def check_user_type(session, headers, graphql_url):
    """Check what type currentUser returns"""
    
    query = {
        "query": """
        query {
            currentUser {
                __typename
                id
                userName
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'data' in data and data['data']['currentUser']:
                user_info = data['data']['currentUser']
                print(f"  ✅ User type: {user_info.get('__typename', 'Unknown')}")
                print(f"  🆔 User ID: {user_info.get('id', 'Unknown')}")
                print(f"  👤 Username: {user_info.get('userName', 'Unknown')}")
                return user_info
            else:
                print(f"  ❌ No user data returned")
                return None
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return None

def extract_advisor_customers(session, headers, graphql_url):
    """Extract customers from Advisor type (cast currentUser as Advisor)"""
    
    # Since currentUser returns Advisor type, we can access advisor fields directly
    query = {
        "query": """
        query {
            currentUser {
                ... on Advisor {
                    customers {
                        totalCount
                        pageInfo {
                            hasNextPage
                            hasPreviousPage
                            startCursor
                            endCursor
                        }
                        edges {
                            cursor
                            node {
                                id
                                customerId
                                userName
                                nickname
                                emailAddress
                                lastContactDate
                                since
                                pastEarnings {
                                    amount
                                    displayAmount
                                }
                                ratingsAverage
                                comments
                                blockedAt
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Error: {data['errors'][0]['message']}")
                return {}
            elif 'data' in data and data['data']['currentUser'] and 'customers' in data['data']['currentUser']:
                customers = data['data']['currentUser']['customers']
                
                total_count = customers.get('totalCount', 0)
                edges = customers.get('edges', [])
                page_info = customers.get('pageInfo', {})
                
                print(f"  🎉 SUCCESS! Found {total_count} total customers!")
                print(f"  📄 Current page: {len(edges)} customers")
                print(f"  🔄 Has next page: {page_info.get('hasNextPage', False)}")
                
                # Show sample customer data
                if edges:
                    sample_customer = edges[0]['node']
                    print(f"  👤 Sample customer:")
                    for key, value in sample_customer.items():
                        if value is not None:
                            if key == 'pastEarnings' and isinstance(value, dict):
                                print(f"    {key}: {value.get('displayAmount', 'N/A')}")
                            else:
                                print(f"    {key}: {value}")
                
                # Get all pages if there are more
                all_customers = edges.copy()
                
                if page_info.get('hasNextPage') and total_count > len(edges):
                    print(f"\n  🔄 Fetching remaining {total_count - len(edges)} customers...")
                    remaining_customers = fetch_all_advisor_customer_pages(session, headers, graphql_url, page_info.get('endCursor'))
                    all_customers.extend(remaining_customers)
                
                print(f"  🎉 Total customers extracted: {len(all_customers)}")
                
                # Save customer data
                customer_data = {
                    'totalCount': total_count,
                    'extractedCount': len(all_customers),
                    'customers': [edge['node'] for edge in all_customers]
                }
                
                with open('data/all_customers_final.json', 'w') as f:
                    json.dump(customer_data, f, indent=2)
                print(f"  💾 Customer data saved to: data/all_customers_final.json")
                
                return customer_data
            else:
                print(f"  ❌ No customer data in response")
                return {}
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {}

def fetch_all_advisor_customer_pages(session, headers, graphql_url, start_cursor):
    """Fetch all remaining customer pages for advisor"""
    
    all_customers = []
    current_cursor = start_cursor
    page_num = 2
    
    while current_cursor:
        print(f"    📄 Fetching page {page_num}...")
        
        query = {
            "query": """
            query($after: String) {
                currentUser {
                    ... on Advisor {
                        customers(after: $after) {
                            pageInfo {
                                hasNextPage
                                endCursor
                            }
                            edges {
                                cursor
                                node {
                                    id
                                    customerId
                                    userName
                                    nickname
                                    emailAddress
                                    lastContactDate
                                    since
                                    pastEarnings {
                                        amount
                                        displayAmount
                                    }
                                    ratingsAverage
                                    comments
                                    blockedAt
                                }
                            }
                        }
                    }
                }
            }
            """,
            "variables": {"after": current_cursor}
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"      ❌ Error on page {page_num}: {data['errors'][0]['message']}")
                    break
                elif 'data' in data and data['data']['currentUser'] and 'customers' in data['data']['currentUser']:
                    customers = data['data']['currentUser']['customers']
                    edges = customers.get('edges', [])
                    page_info = customers.get('pageInfo', {})
                    
                    print(f"      ✅ Page {page_num}: {len(edges)} customers")
                    all_customers.extend(edges)
                    
                    # Check if there are more pages
                    if page_info.get('hasNextPage'):
                        current_cursor = page_info.get('endCursor')
                        page_num += 1
                    else:
                        break
                else:
                    print(f"      ❌ No data on page {page_num}")
                    break
            else:
                print(f"      ❌ HTTP error on page {page_num}: {response.status_code}")
                break
                
        except Exception as e:
            print(f"      ❌ Error on page {page_num}: {e}")
            break
        
        time.sleep(1)  # Rate limiting
    
    return all_customers

def extract_advisor_customer_lists(session, headers, graphql_url):
    """Extract customer lists from Advisor"""
    
    query = {
        "query": """
        query {
            currentUser {
                ... on Advisor {
                    customerLists {
                        totalCount
                        edges {
                            node {
                                id
                                name
                                description
                                customers {
                                    totalCount
                                }
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Error: {data['errors'][0]['message']}")
                return {}
            elif 'data' in data and data['data']['currentUser'] and 'customerLists' in data['data']['currentUser']:
                customer_lists = data['data']['currentUser']['customerLists']
                
                total_lists = customer_lists.get('totalCount', 0)
                list_edges = customer_lists.get('edges', [])
                
                print(f"  ✅ Found {total_lists} customer lists!")
                
                for list_edge in list_edges:
                    list_node = list_edge['node']
                    list_name = list_node.get('name', 'Unnamed')
                    customer_count = list_node.get('customers', {}).get('totalCount', 0)
                    
                    print(f"    📋 List: {list_name} ({customer_count} customers)")
                
                # Save customer lists data
                with open('data/customer_lists_final.json', 'w') as f:
                    json.dump(customer_lists, f, indent=2)
                print(f"  💾 Customer lists saved to: data/customer_lists_final.json")
                
                return customer_lists
            else:
                print(f"  ❌ No customer lists in response")
                return {}
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {}

def analyze_final_data(customers_data, customer_lists_data):
    """Final analysis of extracted customer data"""
    
    print(f"📊 FINAL CUSTOMER DATA ANALYSIS")
    print("=" * 50)
    
    if customers_data:
        customers = customers_data.get('customers', [])
        total_count = customers_data.get('totalCount', 0)
        extracted_count = customers_data.get('extractedCount', 0)
        
        print(f"🎯 EXTRACTION RESULTS:")
        print(f"  Total customers available: {total_count}")
        print(f"  Customers successfully extracted: {extracted_count}")
        print(f"  Extraction rate: {extracted_count/total_count*100:.1f}%" if total_count > 0 else "  Extraction rate: N/A")
        
        if customers:
            # Analyze data quality
            with_email = sum(1 for c in customers if c.get('emailAddress'))
            with_nickname = sum(1 for c in customers if c.get('nickname'))
            with_earnings = sum(1 for c in customers if c.get('pastEarnings', {}).get('amount'))
            with_ratings = sum(1 for c in customers if c.get('ratingsAverage'))
            blocked_customers = sum(1 for c in customers if c.get('blockedAt'))
            
            print(f"\n📊 DATA QUALITY:")
            print(f"  Customers with email: {with_email}/{len(customers)} ({with_email/len(customers)*100:.1f}%)")
            print(f"  Customers with nickname: {with_nickname}/{len(customers)} ({with_nickname/len(customers)*100:.1f}%)")
            print(f"  Customers with earnings: {with_earnings}/{len(customers)} ({with_earnings/len(customers)*100:.1f}%)")
            print(f"  Customers with ratings: {with_ratings}/{len(customers)} ({with_ratings/len(customers)*100:.1f}%)")
            print(f"  Blocked customers: {blocked_customers}/{len(customers)} ({blocked_customers/len(customers)*100:.1f}%)")
            
            # Calculate total earnings
            total_earnings = 0
            for customer in customers:
                earnings = customer.get('pastEarnings', {})
                if earnings and earnings.get('amount'):
                    total_earnings += float(earnings['amount'])
            
            print(f"\n💰 EARNINGS SUMMARY:")
            print(f"  Total earnings from all customers: ${total_earnings:.2f}")
            print(f"  Average earnings per customer: ${total_earnings/len(customers):.2f}")
    
    if customer_lists_data:
        lists = customer_lists_data.get('edges', [])
        total_lists = customer_lists_data.get('totalCount', 0)
        
        print(f"\n📋 CUSTOMER LISTS:")
        print(f"  Total lists: {total_lists}")
        print(f"  Lists extracted: {len(lists)}")
    
    print(f"\n🎉 COMPLETE SUCCESS!")
    print(f"✅ All available customer data has been extracted!")
    print(f"✅ Data saved to JSON files for further processing!")
    print(f"✅ Ready for Google Voice correlation and transcription!")

if __name__ == "__main__":
    extract_customers_final()
