#!/usr/bin/env python3
"""
Chat extractor using proxy/different IP approach with your valid cookies
"""

import sys
from pathlib import Path
import json
import requests
import time
from datetime import datetime
import subprocess
import random

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class ProxyChatExtractor:
    """Extract chat transcripts using proxy/IP rotation with your valid cookies"""
    
    def __init__(self):
        self.session = None
        self.current_ip = None
        self.chat_transcripts = {}
        self.errors = []
        
    def get_current_ip(self):
        """Get current external IP"""
        try:
            response = requests.get('https://httpbin.org/ip', timeout=10)
            if response.status_code == 200:
                return response.json().get('origin', 'Unknown')
        except:
            pass
        return 'Unknown'
    
    def try_ip_change_methods(self):
        """Try various methods to change IP address"""
        
        print("🔄 Attempting to change IP address...")
        
        old_ip = self.get_current_ip()
        print(f"🌐 Current IP: {old_ip}")
        
        # Method 1: Try to restart network interface
        try:
            print("  🔄 Attempting network interface restart...")
            subprocess.run(['sudo', 'systemctl', 'restart', 'NetworkManager'], 
                         capture_output=True, timeout=30)
            time.sleep(10)
            
            new_ip = self.get_current_ip()
            if new_ip != old_ip and new_ip != 'Unknown':
                print(f"  ✅ IP changed: {old_ip} → {new_ip}")
                return True
        except:
            print("  ❌ Network restart failed")
        
        # Method 2: Try to use different DNS
        try:
            print("  🔄 Attempting DNS change...")
            subprocess.run(['sudo', 'systemctl', 'flush-dns'], 
                         capture_output=True, timeout=10)
            time.sleep(5)
        except:
            pass
        
        # Method 3: Check for VPN
        try:
            print("  🔄 Checking for VPN connections...")
            result = subprocess.run(['nmcli', 'connection', 'show'], 
                                  capture_output=True, text=True, timeout=10)
            
            if 'vpn' in result.stdout.lower():
                print("  ✅ VPN connection found")
                # Try to cycle VPN
                vpn_lines = [line for line in result.stdout.split('\n') if 'vpn' in line.lower()]
                if vpn_lines:
                    vpn_name = vpn_lines[0].split()[0]
                    subprocess.run(['nmcli', 'connection', 'down', vpn_name], 
                                 capture_output=True, timeout=10)
                    time.sleep(5)
                    subprocess.run(['nmcli', 'connection', 'up', vpn_name], 
                                 capture_output=True, timeout=15)
                    time.sleep(10)
                    
                    new_ip = self.get_current_ip()
                    if new_ip != old_ip:
                        print(f"  ✅ VPN IP changed: {old_ip} → {new_ip}")
                        return True
        except:
            print("  ❌ VPN cycling failed")
        
        print(f"  ⚠️  IP unchanged: {old_ip}")
        return False
    
    def setup_session_with_proxies(self):
        """Setup session with potential proxy options"""
        
        print("🔐 Setting up session with your cookies...")
        
        # Load your cookies
        config = ConfigLoader.load_config("config/config.yaml")
        keen_cookies = config['keen']['cookies']
        
        # Create session
        self.session = requests.Session()
        
        # Try different proxy configurations
        proxy_configs = [
            None,  # No proxy
            {'http': 'socks5://127.0.0.1:9050', 'https': 'socks5://127.0.0.1:9050'},  # Tor
            {'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'},  # Local proxy
        ]
        
        for i, proxy_config in enumerate(proxy_configs):
            try:
                if proxy_config:
                    print(f"  🔄 Trying proxy configuration {i+1}...")
                    self.session.proxies.update(proxy_config)
                else:
                    print(f"  🔄 Trying direct connection...")
                
                # Set cookies
                for name, value in keen_cookies.items():
                    if value and not value.startswith('YOUR_'):
                        self.session.cookies.set(name, value, domain='.keen.com')
                
                # Set headers with rotation
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                ]
                
                self.session.headers.update({
                    'User-Agent': random.choice(user_agents),
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                })
                
                # Test the configuration
                test_response = self.session.get('https://httpbin.org/ip', timeout=10)
                if test_response.status_code == 200:
                    ip_info = test_response.json()
                    print(f"  ✅ Connection working, IP: {ip_info.get('origin', 'Unknown')}")
                    
                    # Test Keen.com access
                    keen_response = self.session.get('https://www.keen.com', timeout=10)
                    if keen_response.status_code == 200:
                        print(f"  ✅ Keen.com accessible")
                        return True
                    else:
                        print(f"  ❌ Keen.com returned {keen_response.status_code}")
                
            except Exception as e:
                print(f"  ❌ Proxy config {i+1} failed: {e}")
                continue
        
        print("❌ All proxy configurations failed")
        return False
    
    def extract_with_retry(self, chat_url, max_retries=3):
        """Extract chat with retry logic"""
        
        for attempt in range(max_retries):
            try:
                # Add random delay
                time.sleep(random.uniform(2, 5))
                
                # Try the request
                response = self.session.get(chat_url, timeout=15)
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 403:
                    print(f"    ⚠️  403 Forbidden (attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        print(f"    🔄 Waiting before retry...")
                        time.sleep(random.uniform(10, 20))
                else:
                    print(f"    ❌ HTTP {response.status_code} (attempt {attempt + 1})")
                    
            except Exception as e:
                print(f"    ❌ Request failed (attempt {attempt + 1}): {e}")
        
        return None
    
    def run_extraction(self):
        """Run the extraction with IP/proxy handling"""
        
        print("🚀 PROXY CHAT TRANSCRIPT EXTRACTOR")
        print("=" * 60)
        print("🔐 Using your valid Keen.com cookies")
        print("🌐 Attempting IP rotation and proxy methods")
        print("💬 Extracting chat transcripts with retry logic")
        print("=" * 60)
        
        # Check current IP
        current_ip = self.get_current_ip()
        print(f"🌐 Starting IP: {current_ip}")
        
        # Try to change IP first
        ip_changed = self.try_ip_change_methods()
        
        # Setup session
        if not self.setup_session_with_proxies():
            print("❌ Could not setup working session")
            return
        
        # Load interactions to test
        test_interactions = [
            {
                "customer_id": "********",
                "customer_username": "User97925248",
                "interaction_id": "********",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********"
            },
            {
                "customer_id": "********",
                "customer_username": "User50690268", 
                "interaction_id": "********",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********"
            }
        ]
        
        print(f"\n💬 Testing extraction on {len(test_interactions)} chat transcripts...")
        
        for i, interaction in enumerate(test_interactions, 1):
            customer_username = interaction['customer_username']
            interaction_id = interaction['interaction_id']
            
            print(f"\n👤 {i}/{len(test_interactions)}: {customer_username} (Chat {interaction_id})")
            
            # Try extraction with retry
            response = self.extract_with_retry(interaction['chat_url'])
            
            if response:
                print(f"    ✅ Successfully accessed chat page")
                print(f"    📏 Response length: {len(response.text)} characters")
                
                # Try to extract messages
                messages = self.extract_messages_from_html(response.text)
                
                if messages:
                    customer_id = interaction['customer_id']
                    
                    if customer_id not in self.chat_transcripts:
                        self.chat_transcripts[customer_id] = {
                            'customer_info': {
                                'customer_id': customer_id,
                                'username': customer_username
                            },
                            'transcripts': []
                        }
                    
                    transcript = {
                        'interaction_id': interaction_id,
                        'customer_id': customer_id,
                        'customer_username': customer_username,
                        'chat_url': interaction['chat_url'],
                        'messages': messages,
                        'message_count': len(messages),
                        'extraction_timestamp': datetime.now().isoformat(),
                        'extraction_method': 'proxy_with_retry'
                    }
                    
                    self.chat_transcripts[customer_id]['transcripts'].append(transcript)
                    print(f"    ✅ Extracted {len(messages)} messages")
                else:
                    print(f"    ℹ️  No messages found in response")
            else:
                print(f"    ❌ Failed to access chat page after retries")
        
        # Save results
        self.save_results()
    
    def extract_messages_from_html(self, html_content):
        """Extract messages from HTML content"""
        
        import re
        messages = []
        
        try:
            # Look for various message patterns
            patterns = [
                r'"content":\s*"([^"]+)"',
                r'"message":\s*"([^"]+)"',
                r'"text":\s*"([^"]+)"',
                r'<div[^>]*class="[^"]*message[^"]*"[^>]*>(.*?)</div>',
                r'<p[^>]*>(.*?)</p>'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                for i, match in enumerate(matches):
                    clean_text = re.sub(r'<[^>]+>', '', match).strip()
                    if len(clean_text) > 10:
                        message = {
                            'sequence': i + 1,
                            'content': clean_text,
                            'sender_type': self.infer_sender_type(clean_text),
                            'extraction_method': 'html_pattern'
                        }
                        messages.append(message)
                
                if messages:
                    break
        
        except Exception as e:
            print(f"      ❌ Error extracting messages: {e}")
        
        return messages[:10]  # Limit to first 10
    
    def infer_sender_type(self, text):
        """Infer sender type"""
        text_lower = text.lower()
        
        if any(pattern in text_lower for pattern in ['i see', 'i sense', 'the cards', 'spirit']):
            return 'advisor'
        elif any(pattern in text_lower for pattern in ['will i', 'should i', 'when will']):
            return 'customer'
        else:
            return 'unknown'
    
    def save_results(self):
        """Save extraction results"""
        
        if self.chat_transcripts:
            dataset = {
                'metadata': {
                    'extraction_timestamp': datetime.now().isoformat(),
                    'extraction_method': 'proxy_with_retry_and_ip_rotation',
                    'customers_with_transcripts': len(self.chat_transcripts),
                    'total_transcripts': sum(len(c['transcripts']) for c in self.chat_transcripts.values()),
                    'total_messages': sum(
                        sum(len(t.get('messages', [])) for t in c['transcripts']) 
                        for c in self.chat_transcripts.values()
                    )
                },
                'chat_transcripts': self.chat_transcripts,
                'errors': self.errors
            }
            
            results_file = Path("data/proxy_chat_transcripts.json")
            with open(results_file, 'w') as f:
                json.dump(dataset, f, indent=2)
            
            print(f"\n💾 Results saved to: {results_file}")
            print(f"📊 Customers with transcripts: {len(self.chat_transcripts)}")
            print(f"💬 Total transcripts: {dataset['metadata']['total_transcripts']}")
            print(f"📝 Total messages: {dataset['metadata']['total_messages']}")
            
            print(f"\n🎉 PROXY EXTRACTION COMPLETE!")
        else:
            print(f"\n⚠️  No transcripts extracted")

if __name__ == "__main__":
    extractor = ProxyChatExtractor()
    extractor.run_extraction()
