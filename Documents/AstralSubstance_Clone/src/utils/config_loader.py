"""
Configuration Loader Utility

Handles loading and validation of configuration files.
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, validator


class DatabaseConfig(BaseModel):
    """Database configuration model"""
    type: str = Field(..., description="Database type")
    sqlite: Optional[Dict[str, str]] = Field(None, description="SQLite configuration")
    postgresql: Optional[Dict[str, Any]] = Field(None, description="PostgreSQL configuration")


class KeenConfig(BaseModel):
    """Keen.com API configuration model"""
    base_url: str = Field(..., description="Base URL for Keen.com")
    graphql_endpoint: str = Field(..., description="GraphQL endpoint path")
    cookies: Dict[str, str] = Field(default_factory=dict, description="Authentication cookies")
    rate_limiting: Dict[str, Any] = Field(default_factory=dict, description="Rate limiting settings")
    pagination: Dict[str, Any] = Field(default_factory=dict, description="Pagination settings")


class StorageConfig(BaseModel):
    """Storage configuration model"""
    raw_data_dir: str = Field(..., description="Raw data directory")
    processed_data_dir: str = Field(..., description="Processed data directory")
    datasets_dir: str = Field(..., description="Datasets directory")
    logs_dir: str = Field(..., description="Logs directory")


class AppConfig(BaseModel):
    """Main application configuration model"""
    keen: KeenConfig = Field(..., description="Keen.com configuration")
    database: DatabaseConfig = Field(..., description="Database configuration")
    storage: StorageConfig = Field(..., description="Storage configuration")
    google_voice: Dict[str, Any] = Field(default_factory=dict, description="Google Voice configuration")
    correlation: Dict[str, Any] = Field(default_factory=dict, description="Correlation settings")
    dataset: Dict[str, Any] = Field(default_factory=dict, description="Dataset settings")
    logging: Dict[str, Any] = Field(default_factory=dict, description="Logging configuration")
    security: Dict[str, Any] = Field(default_factory=dict, description="Security settings")
    performance: Dict[str, Any] = Field(default_factory=dict, description="Performance settings")
    monitoring: Dict[str, Any] = Field(default_factory=dict, description="Monitoring settings")
    development: Dict[str, Any] = Field(default_factory=dict, description="Development settings")


class ConfigLoader:
    """Configuration loader and validator"""
    
    @staticmethod
    def load_config(config_path: str) -> Dict[str, Any]:
        """
        Load and validate configuration from YAML file
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Validated configuration dictionary
            
        Raises:
            FileNotFoundError: If config file doesn't exist
            ValueError: If config is invalid
        """
        config_file = Path(config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_file, 'r') as f:
            raw_config = yaml.safe_load(f)
        
        # Validate configuration
        try:
            validated_config = AppConfig(**raw_config)
            return validated_config.dict()
        except Exception as e:
            raise ValueError(f"Invalid configuration: {e}")
    
    @staticmethod
    def create_default_config(output_path: str):
        """
        Create a default configuration file
        
        Args:
            output_path: Path where to save the default config
        """
        default_config = {
            'keen': {
                'base_url': 'https://www.keen.com',
                'graphql_endpoint': '/api/graphqlv0',
                'cookies': {
                    'session_token': 'YOUR_SESSION_TOKEN_HERE',
                    'csrf_token': 'YOUR_CSRF_TOKEN_HERE'
                },
                'rate_limiting': {
                    'base_delay': 2.5,
                    'max_retries': 3,
                    'backoff_factor': 2.0,
                    'max_concurrent': 2
                },
                'pagination': {
                    'default_page_size': 100,
                    'max_page_size': 500
                }
            },
            'database': {
                'type': 'sqlite',
                'sqlite': {
                    'path': 'data/astral_substance.db'
                }
            },
            'storage': {
                'raw_data_dir': 'data/raw',
                'processed_data_dir': 'data/processed',
                'datasets_dir': 'data/datasets',
                'logs_dir': 'logs'
            },
            'google_voice': {
                'recordings_directory': 'data/raw/voice_recordings',
                'supported_formats': ['.wav', '.mp3', '.m4a', '.ogg']
            },
            'correlation': {
                'timestamp_tolerance_minutes': 5,
                'duration_tolerance_percent': 10,
                'confidence_threshold': 0.6,
                'weights': {
                    'timestamp': 0.7,
                    'duration': 0.3
                }
            },
            'dataset': {
                'formats': ['conversational', 'instruction_following'],
                'output': {
                    'max_conversation_length': 10000,
                    'include_metadata': True,
                    'preserve_timestamps': True
                },
                'splits': {
                    'train': 0.8,
                    'validation': 0.1,
                    'test': 0.1
                }
            },
            'logging': {
                'level': 'INFO',
                'format': '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}',
                'file_logging': {
                    'enabled': True,
                    'rotation': '100 MB',
                    'retention': '30 days'
                },
                'console_logging': {
                    'enabled': True,
                    'colorize': True
                }
            },
            'security': {
                'encrypt_cookies': True,
                'encryption_key_file': 'config/.encryption_key'
            },
            'performance': {
                'batch_size': 50,
                'memory_limit_mb': 2048,
                'parallel_processing': True,
                'max_workers': 4
            },
            'development': {
                'debug_mode': False,
                'sample_mode': False,
                'sample_size': 10
            }
        }
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)
        
        print(f"Default configuration created at: {output_path}")
        print("Please edit the configuration file with your actual settings before running the application.")
