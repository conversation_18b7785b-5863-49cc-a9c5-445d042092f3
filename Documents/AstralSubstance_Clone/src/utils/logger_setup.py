"""
Logger Setup Utility

Configures loguru logger based on configuration settings.
"""

import sys
from pathlib import Path
from typing import Dict, Any

from loguru import logger


def setup_logging(config: Dict[str, Any]):
    """
    Setup logging configuration
    
    Args:
        config: Application configuration dictionary
    """
    logging_config = config.get('logging', {})
    
    # Remove default logger
    logger.remove()
    
    # Console logging
    console_config = logging_config.get('console_logging', {})
    if console_config.get('enabled', True):
        logger.add(
            sys.stderr,
            level=logging_config.get('level', 'INFO'),
            format=logging_config.get('format', "{time} | {level} | {name}:{function}:{line} | {message}"),
            colorize=console_config.get('colorize', True)
        )
    
    # File logging
    file_config = logging_config.get('file_logging', {})
    if file_config.get('enabled', True):
        logs_dir = Path(config['storage']['logs_dir'])
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = logs_dir / "astral_substance.log"
        
        logger.add(
            str(log_file),
            level=logging_config.get('level', 'INFO'),
            format=logging_config.get('format', "{time} | {level} | {name}:{function}:{line} | {message}"),
            rotation=file_config.get('rotation', '100 MB'),
            retention=file_config.get('retention', '30 days'),
            compression="gz"
        )
    
    logger.info("Logging configured successfully")


def get_logger(name: str):
    """
    Get a logger instance with the given name
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logger.bind(name=name)
