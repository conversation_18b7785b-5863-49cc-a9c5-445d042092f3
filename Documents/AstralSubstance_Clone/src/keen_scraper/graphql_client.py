"""
Keen.com GraphQL API Client

Handles GraphQL queries, pagination, and data extraction from Keen.com API.
"""

import time
from typing import Dict, List, Any, Optional, Generator
from datetime import datetime

from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import requests

from .auth_manager import <PERSON><PERSON><PERSON>uthMana<PERSON>, AuthenticationError


class GraphQLClient:
    """
    GraphQL client for Keen.com API with built-in pagination and error handling
    """
    
    def __init__(self, auth_manager: KeenAuthManager, config: Dict[str, Any]):
        self.auth_manager = auth_manager
        self.config = config
        self.base_url = config['keen']['base_url']
        self.endpoint = config['keen']['graphql_endpoint']
        
        # Rate limiting settings
        self.base_delay = config['keen']['rate_limiting']['base_delay']
        self.max_retries = config['keen']['rate_limiting']['max_retries']
        self.backoff_factor = config['keen']['rate_limiting']['backoff_factor']
        
        # Pagination settings
        self.default_page_size = config['keen']['pagination']['default_page_size']
        self.max_page_size = config['keen']['pagination']['max_page_size']
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((requests.RequestException, AuthenticationError))
    )
    def _execute_query(self, query: str, variables: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Execute a GraphQL query with retry logic
        
        Args:
            query: GraphQL query string
            variables: Query variables
            
        Returns:
            Dict containing the response data
            
        Raises:
            GraphQLError: If the query fails
            AuthenticationError: If authentication is invalid
        """
        if variables is None:
            variables = {}
        
        # Get authenticated session
        session = self.auth_manager.get_authenticated_session()
        
        # Prepare request
        payload = {
            "query": query,
            "variables": variables
        }
        
        # Add delay for rate limiting
        time.sleep(self.base_delay)
        
        try:
            response = session.post(
                f"{self.base_url}{self.endpoint}",
                json=payload,
                timeout=30
            )
            
            # Check for HTTP errors
            if response.status_code == 401 or response.status_code == 403:
                raise AuthenticationError("Authentication failed - session may have expired")
            
            response.raise_for_status()
            
            # Parse JSON response
            data = response.json()
            
            # Check for GraphQL errors
            if 'errors' in data:
                error_messages = [error.get('message', 'Unknown error') for error in data['errors']]
                raise GraphQLError(f"GraphQL errors: {'; '.join(error_messages)}")
            
            if 'data' not in data:
                raise GraphQLError("No data in response")
            
            logger.debug(f"Query executed successfully: {len(str(data))} bytes received")
            return data['data']
            
        except requests.RequestException as e:
            logger.error(f"Request failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise GraphQLError(f"Query execution failed: {e}")
    
    def get_customer_list(self, page_size: int = None, offset: int = 0) -> Dict[str, Any]:
        """
        Get paginated list of customers
        
        Args:
            page_size: Number of customers per page
            offset: Starting offset for pagination
            
        Returns:
            Dict containing customer list and pagination info
        """
        if page_size is None:
            page_size = self.default_page_size
        
        query = """
        query ($listId: Int, $first: Int, $after: String, $last: Int, $before: String, 
               $filter: String, $sortBy: [String], $sortDescending: [Boolean], 
               $fetch: Int, $offset: Int) {
            user {
                id
                customers(
                    first: $first, 
                    after: $after, 
                    last: $last, 
                    before: $before, 
                    filter: $filter, 
                    sortBy: $sortBy, 
                    sortDescending: $sortDescending, 
                    listId: $listId, 
                    fetch: $fetch, 
                    offset: $offset
                ) {
                    totalCount
                    pageInfo {
                        hasNextPage
                        hasPreviousPage
                    }
                    edges {
                        cursor
                        node {
                            id
                            userName
                            nickname
                            customerSince
                            alerts {
                                name
                            }
                            contacts {
                                last {
                                    id
                                    activityId
                                    mailId
                                    masterTransactionId
                                    date
                                    amount {
                                        amount
                                        displayAmount(format: "c2")
                                    }
                                    type
                                }
                            }
                            cumulativeSummary {
                                totalCallCount
                                totalChatCount
                                totalPaidMails
                                totalEarnings {
                                    amount
                                    displayAmount(format: "c2")
                                }
                            }
                            list {
                                id
                                name
                            }
                        }
                    }
                }
            }
        }
        """
        
        variables = {
            "offset": offset,
            "fetch": page_size,
            "filter": "",
            "sortBy": ["contacts.last.date"],
            "sortDescending": [True],
            "listId": 0
        }
        
        logger.info(f"Fetching customer list: offset={offset}, page_size={page_size}")
        return self._execute_query(query, variables)
    
    def get_customer_details(self, customer_id: str) -> Dict[str, Any]:
        """
        Get detailed information for a specific customer
        
        Args:
            customer_id: Keen customer ID
            
        Returns:
            Dict containing detailed customer information
        """
        # This query structure may need to be discovered through browser inspection
        # as individual customer pages might use different GraphQL queries
        query = """
        query ($customerId: String!) {
            customer(id: $customerId) {
                id
                userName
                nickname
                customerSince
                profile {
                    bio
                    location
                    preferences
                }
                contactHistory {
                    totalCount
                    edges {
                        node {
                            id
                            type
                            date
                            duration
                            amount {
                                amount
                                displayAmount
                            }
                            transcript
                            recording {
                                available
                                url
                            }
                        }
                    }
                }
                chatSessions {
                    totalCount
                    edges {
                        node {
                            id
                            startTime
                            endTime
                            messageCount
                            messages {
                                id
                                timestamp
                                sender
                                content
                                type
                            }
                        }
                    }
                }
            }
        }
        """
        
        variables = {"customerId": customer_id}
        
        logger.info(f"Fetching customer details: {customer_id}")
        return self._execute_query(query, variables)
    
    def get_all_customers(self) -> Generator[Dict[str, Any], None, None]:
        """
        Generator that yields all customers with pagination
        
        Yields:
            Dict: Individual customer data
        """
        offset = 0
        page_size = self.default_page_size
        
        while True:
            try:
                result = self.get_customer_list(page_size=page_size, offset=offset)
                
                customers_data = result.get('user', {}).get('customers', {})
                edges = customers_data.get('edges', [])
                
                if not edges:
                    logger.info("No more customers to fetch")
                    break
                
                # Yield each customer
                for edge in edges:
                    customer = edge.get('node', {})
                    if customer:
                        yield customer
                
                # Check if there are more pages
                page_info = customers_data.get('pageInfo', {})
                if not page_info.get('hasNextPage', False):
                    logger.info("Reached last page of customers")
                    break
                
                # Move to next page
                offset += page_size
                logger.info(f"Moving to next page: offset={offset}")
                
            except Exception as e:
                logger.error(f"Error fetching customers at offset {offset}: {e}")
                # Decide whether to continue or stop based on error type
                if isinstance(e, AuthenticationError):
                    raise  # Re-raise auth errors
                else:
                    # For other errors, log and continue with next page
                    offset += page_size
                    continue
    
    def discover_queries(self) -> Dict[str, Any]:
        """
        Attempt to discover available GraphQL queries through introspection
        
        Returns:
            Dict containing schema information if introspection is enabled
        """
        introspection_query = """
        query IntrospectionQuery {
            __schema {
                queryType { name }
                mutationType { name }
                subscriptionType { name }
                types {
                    ...FullType
                }
            }
        }
        
        fragment FullType on __Type {
            kind
            name
            description
            fields(includeDeprecated: true) {
                name
                description
                args {
                    ...InputValue
                }
                type {
                    ...TypeRef
                }
                isDeprecated
                deprecationReason
            }
        }
        
        fragment InputValue on __InputValue {
            name
            description
            type { ...TypeRef }
            defaultValue
        }
        
        fragment TypeRef on __Type {
            kind
            name
            ofType {
                kind
                name
                ofType {
                    kind
                    name
                    ofType {
                        kind
                        name
                    }
                }
            }
        }
        """
        
        try:
            logger.info("Attempting GraphQL introspection")
            result = self._execute_query(introspection_query)
            logger.info("Introspection successful - schema discovered")
            return result
        except Exception as e:
            logger.warning(f"Introspection failed (likely disabled): {e}")
            return {}


class GraphQLError(Exception):
    """Raised when GraphQL query fails"""
    pass
