"""
Keen.com Authentication Manager

Handles session management, cookie storage, and authentication validation
for the Keen.com GraphQL API.
"""

import json
import os
from pathlib import Path
from typing import Dict, Optional, Any
from datetime import datetime, timedelta

import requests
from cryptography.fernet import Fernet
from loguru import logger
from pydantic import BaseModel, Field


class SessionCookies(BaseModel):
    """Model for session cookies"""
    session_token: str = Field(..., description="Main session token")
    csrf_token: Optional[str] = Field(None, description="CSRF protection token")
    additional_cookies: Dict[str, str] = Field(default_factory=dict, description="Other required cookies")
    expires_at: Optional[datetime] = Field(None, description="Session expiration time")


class KeenAuthManager:
    """
    Manages authentication and session handling for Keen.com API
    
    Features:
    - Encrypted cookie storage
    - Session validation
    - Automatic retry on auth failures
    - Cookie refresh prompting
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_url = config['keen']['base_url']
        self.graphql_endpoint = config['keen']['graphql_endpoint']
        
        # Initialize session
        self.session = requests.Session()
        self._setup_session_headers()
        
        # Cookie storage
        self.cookie_store_path = Path(config.get('security', {}).get('encryption_key_file', 'config/.cookies.enc'))
        self.encryption_key_path = Path(config.get('security', {}).get('encryption_key_file', 'config/.encryption_key'))
        
        # Initialize encryption
        self._init_encryption()
        
        # Load existing cookies if available
        self.cookies: Optional[SessionCookies] = None
        self._load_cookies()
    
    def _setup_session_headers(self):
        """Setup default headers for the session"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/json',
            'Origin': self.base_url,
            'Referer': f"{self.base_url}/app/myaccount/customers",
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        })
    
    def _init_encryption(self):
        """Initialize encryption for cookie storage"""
        if self.encryption_key_path.exists():
            with open(self.encryption_key_path, 'rb') as f:
                key = f.read()
        else:
            # Generate new encryption key
            key = Fernet.generate_key()
            self.encryption_key_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.encryption_key_path, 'wb') as f:
                f.write(key)
            logger.info(f"Generated new encryption key: {self.encryption_key_path}")
        
        self.cipher = Fernet(key)
    
    def _load_cookies(self):
        """Load encrypted cookies from storage"""
        if not self.cookie_store_path.exists():
            logger.warning("No stored cookies found")
            return
        
        try:
            with open(self.cookie_store_path, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.cipher.decrypt(encrypted_data)
            cookie_data = json.loads(decrypted_data.decode())
            
            self.cookies = SessionCookies(**cookie_data)
            self._apply_cookies_to_session()
            
            logger.info("Loaded stored cookies successfully")
            
        except Exception as e:
            logger.error(f"Failed to load cookies: {e}")
            self.cookies = None
    
    def _save_cookies(self):
        """Save encrypted cookies to storage"""
        if not self.cookies:
            return
        
        try:
            cookie_data = self.cookies.model_dump()
            json_data = json.dumps(cookie_data, default=str)
            encrypted_data = self.cipher.encrypt(json_data.encode())
            
            self.cookie_store_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.cookie_store_path, 'wb') as f:
                f.write(encrypted_data)
            
            logger.info("Saved cookies successfully")
            
        except Exception as e:
            logger.error(f"Failed to save cookies: {e}")
    
    def _apply_cookies_to_session(self):
        """Apply stored cookies to the requests session"""
        if not self.cookies:
            return
        
        # Apply main session token
        self.session.cookies.set('session_token', self.cookies.session_token)
        
        # Apply CSRF token if available
        if self.cookies.csrf_token:
            self.session.cookies.set('csrf_token', self.cookies.csrf_token)
            self.session.headers['X-CSRF-Token'] = self.cookies.csrf_token
        
        # Apply additional cookies
        for name, value in self.cookies.additional_cookies.items():
            self.session.cookies.set(name, value)
    
    def update_cookies(self, cookies_dict: Dict[str, str]):
        """
        Update stored cookies with new values
        
        Args:
            cookies_dict: Dictionary of cookie name-value pairs
        """
        session_token = cookies_dict.get('session_token', '')
        csrf_token = cookies_dict.get('csrf_token')
        
        # Remove known cookies from additional cookies
        additional = {k: v for k, v in cookies_dict.items() 
                     if k not in ['session_token', 'csrf_token']}
        
        self.cookies = SessionCookies(
            session_token=session_token,
            csrf_token=csrf_token,
            additional_cookies=additional,
            expires_at=datetime.now() + timedelta(hours=24)  # Assume 24h expiry
        )
        
        self._apply_cookies_to_session()
        self._save_cookies()
        
        logger.info("Updated authentication cookies")
    
    def validate_session(self) -> bool:
        """
        Validate current session by making a test API call
        
        Returns:
            bool: True if session is valid, False otherwise
        """
        if not self.cookies:
            logger.warning("No cookies available for validation")
            return False
        
        # Check if cookies are expired
        if self.cookies.expires_at and datetime.now() > self.cookies.expires_at:
            logger.warning("Stored cookies have expired")
            return False
        
        # Test with a simple GraphQL query
        test_query = {
            "query": "query { user { id } }",
            "variables": {}
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}{self.graphql_endpoint}",
                json=test_query,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'errors' not in data and 'data' in data:
                    logger.info("Session validation successful")
                    return True
                else:
                    logger.warning(f"GraphQL errors in validation: {data.get('errors', [])}")
                    return False
            else:
                logger.warning(f"Session validation failed with status {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return False
    
    def get_authenticated_session(self) -> requests.Session:
        """
        Get authenticated requests session
        
        Returns:
            requests.Session: Configured session with authentication
            
        Raises:
            AuthenticationError: If session is not valid and cannot be refreshed
        """
        if not self.validate_session():
            raise AuthenticationError(
                "Session is not valid. Please update cookies using update_cookies() method."
            )
        
        return self.session
    
    def prompt_for_cookies(self):
        """
        Interactive prompt for updating cookies
        Useful for initial setup or when session expires
        """
        print("\n" + "="*60)
        print("KEEN.COM AUTHENTICATION SETUP")
        print("="*60)
        print("\nTo extract cookies from your browser:")
        print("1. Open Keen.com and log in")
        print("2. Navigate to your customer list page")
        print("3. Open Developer Tools (F12)")
        print("4. Go to Network tab")
        print("5. Refresh the page")
        print("6. Find a request to /api/graphqlv0")
        print("7. Copy the Cookie header value")
        print("\nExample Cookie header:")
        print("session_token=abc123; csrf_token=def456; other_cookie=xyz789")
        print("\n" + "-"*60)
        
        cookie_header = input("Paste the Cookie header value here: ").strip()
        
        if not cookie_header:
            print("No cookies provided. Exiting...")
            return
        
        # Parse cookie header
        cookies_dict = {}
        for cookie in cookie_header.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookies_dict[name] = value
        
        if not cookies_dict:
            print("Failed to parse cookies. Please check the format.")
            return
        
        # Update cookies
        self.update_cookies(cookies_dict)
        
        # Validate
        if self.validate_session():
            print("✅ Authentication successful!")
        else:
            print("❌ Authentication failed. Please check your cookies.")


class AuthenticationError(Exception):
    """Raised when authentication fails"""
    pass
