"""
Keen.com Data Extractor

Orchestrates the extraction of customer data, chat sessions, and call logs
from Keen.com using the GraphQL client.
"""

import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from contextlib import contextmanager

from loguru import logger
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from tenacity import retry, stop_after_attempt, wait_exponential

from .graphql_client import GraphQLClient, GraphQLError
from ..models.database import (
    DatabaseManager, Customer, ChatSession, ChatMessage, 
    CallLog, ExtractionLog, CustomerData, ChatMessageData
)


class KeenDataExtractor:
    """
    Extracts and stores data from Keen.com API
    
    Handles customer lists, individual customer details, chat sessions,
    and call logs with proper error handling and progress tracking.
    """
    
    def __init__(self, graphql_client: GraphQLClient, db_manager: DatabaseManager, config: Dict[str, Any]):
        self.graphql_client = graphql_client
        self.db_manager = db_manager
        self.config = config
        
        # Performance settings
        self.batch_size = config.get('performance', {}).get('batch_size', 50)
        self.max_workers = config.get('performance', {}).get('max_workers', 4)
        
        # Development settings
        self.debug_mode = config.get('development', {}).get('debug_mode', False)
    
    @contextmanager
    def _get_db_session(self):
        """Context manager for database sessions"""
        session = self.db_manager.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def extract_all_data(self) -> Dict[str, Any]:
        """
        Extract all available data from Keen.com
        
        Returns:
            Dictionary with extraction statistics
        """
        logger.info("Starting full data extraction from Keen.com")
        
        extraction_start = datetime.now()
        stats = {
            'customers_processed': 0,
            'customers_successful': 0,
            'customers_failed': 0,
            'chat_sessions_extracted': 0,
            'chat_messages_extracted': 0,
            'call_logs_extracted': 0,
            'errors': []
        }
        
        try:
            with self._get_db_session() as db_session:
                # Log extraction start
                extraction_log = ExtractionLog(
                    operation_type='full_extraction',
                    status='running',
                    started_at=extraction_start
                )
                db_session.add(extraction_log)
                db_session.commit()
                
                # Extract customer data
                for customer_data in self.graphql_client.get_all_customers():
                    try:
                        stats['customers_processed'] += 1
                        
                        # Extract and store customer
                        customer = self._extract_customer(db_session, customer_data)
                        if customer:
                            stats['customers_successful'] += 1
                            
                            # Extract detailed customer data
                            detail_stats = self._extract_customer_details(db_session, customer)
                            stats['chat_sessions_extracted'] += detail_stats.get('chat_sessions', 0)
                            stats['chat_messages_extracted'] += detail_stats.get('chat_messages', 0)
                            stats['call_logs_extracted'] += detail_stats.get('call_logs', 0)
                        
                        # Progress logging
                        if stats['customers_processed'] % 10 == 0:
                            logger.info(f"Processed {stats['customers_processed']} customers")
                        
                    except Exception as e:
                        stats['customers_failed'] += 1
                        stats['errors'].append(f"Customer {customer_data.get('id', 'unknown')}: {str(e)}")
                        logger.error(f"Failed to extract customer {customer_data.get('id', 'unknown')}: {e}")
                        continue
                
                # Update extraction log
                extraction_end = datetime.now()
                extraction_log.status = 'success'
                extraction_log.completed_at = extraction_end
                extraction_log.duration_seconds = (extraction_end - extraction_start).total_seconds()
                extraction_log.records_processed = stats['customers_processed']
                extraction_log.records_successful = stats['customers_successful']
                extraction_log.records_failed = stats['customers_failed']
                
                db_session.add(extraction_log)
                
        except Exception as e:
            logger.error(f"Full extraction failed: {e}")
            stats['errors'].append(f"Full extraction error: {str(e)}")
            
            # Update extraction log with error
            with self._get_db_session() as db_session:
                extraction_log.status = 'error'
                extraction_log.completed_at = datetime.now()
                extraction_log.error_message = str(e)
                db_session.add(extraction_log)
        
        logger.info(f"Full extraction completed: {stats}")
        return stats
    
    def extract_sample_data(self, sample_size: int = 10) -> Dict[str, Any]:
        """
        Extract a sample of data for testing
        
        Args:
            sample_size: Number of customers to extract
            
        Returns:
            Dictionary with extraction statistics
        """
        logger.info(f"Starting sample data extraction: {sample_size} customers")
        
        stats = {
            'customers_processed': 0,
            'customers_successful': 0,
            'customers_failed': 0,
            'chat_sessions_extracted': 0,
            'chat_messages_extracted': 0,
            'call_logs_extracted': 0,
            'errors': []
        }
        
        try:
            with self._get_db_session() as db_session:
                customer_count = 0
                
                for customer_data in self.graphql_client.get_all_customers():
                    if customer_count >= sample_size:
                        break
                    
                    try:
                        stats['customers_processed'] += 1
                        customer_count += 1
                        
                        # Extract and store customer
                        customer = self._extract_customer(db_session, customer_data)
                        if customer:
                            stats['customers_successful'] += 1
                            
                            # Extract detailed customer data
                            detail_stats = self._extract_customer_details(db_session, customer)
                            stats['chat_sessions_extracted'] += detail_stats.get('chat_sessions', 0)
                            stats['chat_messages_extracted'] += detail_stats.get('chat_messages', 0)
                            stats['call_logs_extracted'] += detail_stats.get('call_logs', 0)
                        
                        logger.info(f"Extracted sample customer {customer_count}/{sample_size}")
                        
                    except Exception as e:
                        stats['customers_failed'] += 1
                        stats['errors'].append(f"Customer {customer_data.get('id', 'unknown')}: {str(e)}")
                        logger.error(f"Failed to extract customer {customer_data.get('id', 'unknown')}: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"Sample extraction failed: {e}")
            stats['errors'].append(f"Sample extraction error: {str(e)}")
        
        logger.info(f"Sample extraction completed: {stats}")
        return stats
    
    def _extract_customer(self, db_session: Session, customer_data: Dict[str, Any]) -> Optional[Customer]:
        """
        Extract and store customer data
        
        Args:
            db_session: Database session
            customer_data: Raw customer data from API
            
        Returns:
            Customer object or None if failed
        """
        try:
            # Validate customer data
            validated_data = CustomerData(
                keen_customer_id=customer_data['id'],
                username=customer_data.get('userName'),
                nickname=customer_data.get('nickname'),
                customer_since=self._parse_datetime(customer_data.get('customerSince')),
                total_call_count=customer_data.get('cumulativeSummary', {}).get('totalCallCount', 0),
                total_chat_count=customer_data.get('cumulativeSummary', {}).get('totalChatCount', 0),
                total_earnings=self._parse_amount(customer_data.get('cumulativeSummary', {}).get('totalEarnings')),
                raw_data=customer_data
            )
            
            # Check if customer already exists
            existing_customer = db_session.query(Customer).filter(
                Customer.keen_customer_id == validated_data.keen_customer_id
            ).first()
            
            if existing_customer:
                # Update existing customer
                for field, value in validated_data.dict(exclude={'raw_data'}).items():
                    if value is not None:
                        setattr(existing_customer, field, value)
                existing_customer.raw_data = validated_data.raw_data
                existing_customer.last_updated = datetime.now()
                customer = existing_customer
            else:
                # Create new customer
                customer = Customer(**validated_data.dict())
                db_session.add(customer)
            
            db_session.flush()  # Get the customer ID
            
            logger.debug(f"Extracted customer: {customer.keen_customer_id}")
            return customer
            
        except Exception as e:
            logger.error(f"Failed to extract customer {customer_data.get('id', 'unknown')}: {e}")
            return None
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _extract_customer_details(self, db_session: Session, customer: Customer) -> Dict[str, int]:
        """
        Extract detailed customer information including chats and calls
        
        Args:
            db_session: Database session
            customer: Customer object
            
        Returns:
            Dictionary with extraction statistics
        """
        stats = {
            'chat_sessions': 0,
            'chat_messages': 0,
            'call_logs': 0
        }
        
        try:
            # Get detailed customer data from API
            customer_details = self.graphql_client.get_customer_details(customer.keen_customer_id)
            
            if not customer_details:
                logger.warning(f"No detailed data found for customer {customer.keen_customer_id}")
                return stats
            
            # Extract chat sessions
            chat_stats = self._extract_chat_sessions(db_session, customer, customer_details)
            stats['chat_sessions'] = chat_stats.get('sessions', 0)
            stats['chat_messages'] = chat_stats.get('messages', 0)
            
            # Extract call logs
            call_stats = self._extract_call_logs(db_session, customer, customer_details)
            stats['call_logs'] = call_stats.get('calls', 0)
            
        except GraphQLError as e:
            logger.error(f"GraphQL error extracting details for customer {customer.keen_customer_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to extract details for customer {customer.keen_customer_id}: {e}")
            raise
        
        return stats
    
    def _extract_chat_sessions(self, db_session: Session, customer: Customer, customer_details: Dict[str, Any]) -> Dict[str, int]:
        """Extract chat sessions and messages"""
        stats = {'sessions': 0, 'messages': 0}
        
        chat_sessions_data = customer_details.get('customer', {}).get('chatSessions', {}).get('edges', [])
        
        for session_edge in chat_sessions_data:
            session_data = session_edge.get('node', {})
            
            try:
                # Create or update chat session
                session = ChatSession(
                    customer_id=customer.id,
                    session_id=session_data.get('id'),
                    start_time=self._parse_datetime(session_data.get('startTime')),
                    end_time=self._parse_datetime(session_data.get('endTime')),
                    message_count=session_data.get('messageCount', 0),
                    session_type='chat',
                    raw_data=session_data
                )
                
                # Calculate duration
                if session.start_time and session.end_time:
                    duration = session.end_time - session.start_time
                    session.duration_minutes = int(duration.total_seconds() / 60)
                
                db_session.add(session)
                db_session.flush()
                
                stats['sessions'] += 1
                
                # Extract messages
                messages_data = session_data.get('messages', [])
                message_stats = self._extract_chat_messages(db_session, session, messages_data)
                stats['messages'] += message_stats
                
            except Exception as e:
                logger.error(f"Failed to extract chat session {session_data.get('id', 'unknown')}: {e}")
                continue
        
        return stats
    
    def _extract_chat_messages(self, db_session: Session, session: ChatSession, messages_data: List[Dict[str, Any]]) -> int:
        """Extract chat messages for a session"""
        message_count = 0
        
        for i, message_data in enumerate(messages_data):
            try:
                # Validate message data
                validated_message = ChatMessageData(
                    timestamp=self._parse_datetime(message_data.get('timestamp')),
                    speaker=self._map_speaker(message_data.get('sender')),
                    message_text=message_data.get('content', ''),
                    message_order=i + 1,
                    message_type=message_data.get('type', 'text')
                )
                
                message = ChatMessage(
                    session_id=session.id,
                    **validated_message.dict()
                )
                
                db_session.add(message)
                message_count += 1
                
            except Exception as e:
                logger.error(f"Failed to extract message {i} for session {session.id}: {e}")
                continue
        
        return message_count
    
    def _extract_call_logs(self, db_session: Session, customer: Customer, customer_details: Dict[str, Any]) -> Dict[str, int]:
        """Extract call logs"""
        stats = {'calls': 0}
        
        contact_history = customer_details.get('customer', {}).get('contactHistory', {}).get('edges', [])
        
        for contact_edge in contact_history:
            contact_data = contact_edge.get('node', {})
            
            if contact_data.get('type') == 'call':
                try:
                    call_log = CallLog(
                        customer_id=customer.id,
                        call_timestamp=self._parse_datetime(contact_data.get('date')),
                        duration_seconds=contact_data.get('duration'),
                        call_type='incoming',  # Default, may need to be determined from data
                        amount=self._parse_amount(contact_data.get('amount')),
                        transaction_id=contact_data.get('id'),
                        raw_data=contact_data
                    )
                    
                    db_session.add(call_log)
                    stats['calls'] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to extract call log {contact_data.get('id', 'unknown')}: {e}")
                    continue
        
        return stats
    
    def _parse_datetime(self, date_string: Optional[str]) -> Optional[datetime]:
        """Parse datetime string from API"""
        if not date_string:
            return None
        
        try:
            # Handle various datetime formats
            if 'T' in date_string:
                return datetime.fromisoformat(date_string.replace('Z', '+00:00'))
            else:
                return datetime.strptime(date_string, '%Y-%m-%d')
        except Exception:
            logger.warning(f"Failed to parse datetime: {date_string}")
            return None
    
    def _parse_amount(self, amount_data: Optional[Dict[str, Any]]) -> float:
        """Parse amount from API response"""
        if not amount_data:
            return 0.0
        
        try:
            return float(amount_data.get('amount', 0.0))
        except (ValueError, TypeError):
            return 0.0
    
    def _map_speaker(self, sender: Optional[str]) -> str:
        """Map API sender to standardized speaker"""
        if not sender:
            return 'unknown'
        
        sender_lower = sender.lower()
        if 'customer' in sender_lower or 'user' in sender_lower:
            return 'customer'
        elif 'advisor' in sender_lower or 'psychic' in sender_lower:
            return 'advisor'
        else:
            return 'unknown'
