"""
AstralSubstance Clone - Main Application

Entry point for the LLM dataset builder that extracts data from Keen.com
and Google Voice, correlates them, and builds training datasets.
"""

import sys
import yaml
from pathlib import Path
from typing import Dict, Any
import click
from loguru import logger

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))

from keen_scraper.auth_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AuthenticationError
from keen_scraper.graphql_client import GraphQLClient
from keen_scraper.data_extractor import KeenDataExtractor
from google_voice.voice_processor import GoogleVoiceProcessor
from data_correlation.correlator import DataCorrelator
from dataset_builder.llm_formatter import LLMDatasetFormatter
from models.database import DatabaseManager
from utils.config_loader import ConfigLoader
from utils.logger_setup import setup_logging


class AstralSubstanceApp:
    """Main application class for AstralSubstance Clone"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        
        # Setup logging
        setup_logging(self.config)
        
        # Initialize components
        self.db_manager = None
        self.auth_manager = None
        self.graphql_client = None
        self.data_extractor = None
        self.voice_processor = None
        self.correlator = None
        self.dataset_formatter = None
        
        self._initialize_components()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        with open(self.config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        return config
    
    def _initialize_components(self):
        """Initialize all application components"""
        logger.info("Initializing AstralSubstance Clone components")
        
        # Database
        db_config = self.config['database']
        if db_config['type'] == 'sqlite':
            db_url = f"sqlite:///{db_config['sqlite']['path']}"
        else:
            # Add support for other databases as needed
            raise ValueError(f"Unsupported database type: {db_config['type']}")
        
        self.db_manager = DatabaseManager(db_url)
        self.db_manager.create_tables()
        
        # Authentication
        self.auth_manager = KeenAuthManager(self.config)
        
        # GraphQL client
        self.graphql_client = GraphQLClient(self.auth_manager, self.config)
        
        # Data extractor
        self.data_extractor = KeenDataExtractor(
            self.graphql_client, 
            self.db_manager, 
            self.config
        )
        
        # Voice processor
        self.voice_processor = GoogleVoiceProcessor(self.config)
        
        # Data correlator
        self.correlator = DataCorrelator(self.config)
        
        # Dataset formatter
        self.dataset_formatter = LLMDatasetFormatter(self.config)
        
        logger.info("All components initialized successfully")
    
    def setup_authentication(self):
        """Setup Keen.com authentication"""
        logger.info("Setting up Keen.com authentication")
        
        if not self.auth_manager.validate_session():
            logger.warning("No valid session found, prompting for cookies")
            self.auth_manager.prompt_for_cookies()
        else:
            logger.info("Valid session found")
    
    def extract_keen_data(self, sample_mode: bool = False, sample_size: int = 10):
        """Extract data from Keen.com"""
        logger.info("Starting Keen.com data extraction")
        
        try:
            if sample_mode:
                logger.info(f"Running in sample mode: extracting {sample_size} customers")
                stats = self.data_extractor.extract_sample_data(sample_size)
            else:
                logger.info("Running full extraction")
                stats = self.data_extractor.extract_all_data()
            
            logger.info(f"Keen.com extraction completed: {stats}")
            return stats
            
        except AuthenticationError:
            logger.error("Authentication failed. Please update your cookies.")
            raise
        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise
    
    def process_voice_recordings(self, recordings_dir: str = None):
        """Process Google Voice recordings"""
        if recordings_dir is None:
            recordings_dir = self.config['google_voice']['recordings_directory']
        
        logger.info(f"Processing Google Voice recordings from: {recordings_dir}")
        
        try:
            stats = self.voice_processor.process_recordings_directory(recordings_dir)
            logger.info(f"Voice processing completed: {stats}")
            return stats
        except Exception as e:
            logger.error(f"Voice processing failed: {e}")
            raise
    
    def correlate_data(self):
        """Correlate voice recordings with call logs"""
        logger.info("Starting data correlation")
        
        try:
            with self.db_manager.get_session() as db_session:
                matches = self.correlator.correlate_recordings_with_calls(db_session)
                applied_count = self.correlator.apply_correlations(db_session, matches)
                
                stats = self.correlator.get_correlation_statistics(db_session)
                logger.info(f"Correlation completed: {applied_count} new correlations, stats: {stats}")
                
                return stats
        except Exception as e:
            logger.error(f"Data correlation failed: {e}")
            raise
    
    def build_datasets(self, formats: list = None):
        """Build LLM training datasets"""
        if formats is None:
            formats = self.config['dataset']['formats']
        
        logger.info(f"Building datasets in formats: {formats}")
        
        try:
            with self.db_manager.get_session() as db_session:
                results = {}
                
                if 'conversational' in formats:
                    stats = self.dataset_formatter.build_conversational_dataset(db_session)
                    results['conversational'] = stats
                
                if 'instruction_following' in formats:
                    stats = self.dataset_formatter.build_instruction_dataset(db_session)
                    results['instruction_following'] = stats
                
                # Generate summary
                summary = self.dataset_formatter.generate_dataset_summary(db_session)
                results['summary'] = summary
                
                logger.info(f"Dataset building completed: {results}")
                return results
                
        except Exception as e:
            logger.error(f"Dataset building failed: {e}")
            raise
    
    def run_full_pipeline(self, sample_mode: bool = False, sample_size: int = 10):
        """Run the complete data extraction and dataset building pipeline"""
        logger.info("Starting full pipeline execution")
        
        try:
            # Step 1: Setup authentication
            self.setup_authentication()
            
            # Step 2: Extract Keen.com data
            keen_stats = self.extract_keen_data(sample_mode, sample_size)
            
            # Step 3: Process voice recordings
            voice_stats = self.process_voice_recordings()
            
            # Step 4: Correlate data
            correlation_stats = self.correlate_data()
            
            # Step 5: Build datasets
            dataset_stats = self.build_datasets()
            
            # Summary
            pipeline_results = {
                'keen_extraction': keen_stats,
                'voice_processing': voice_stats,
                'data_correlation': correlation_stats,
                'dataset_building': dataset_stats
            }
            
            logger.info("Full pipeline completed successfully")
            logger.info(f"Pipeline results: {pipeline_results}")
            
            return pipeline_results
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            raise


# CLI Interface
@click.group()
@click.option('--config', default='config/config.yaml', help='Configuration file path')
@click.pass_context
def cli(ctx, config):
    """AstralSubstance Clone - LLM Dataset Builder"""
    ctx.ensure_object(dict)
    ctx.obj['app'] = AstralSubstanceApp(config)


@cli.command()
@click.pass_context
def setup_auth(ctx):
    """Setup Keen.com authentication"""
    app = ctx.obj['app']
    app.setup_authentication()


@cli.command()
@click.option('--sample', is_flag=True, help='Extract only a sample of data')
@click.option('--sample-size', default=10, help='Number of customers to extract in sample mode')
@click.pass_context
def extract_keen(ctx, sample, sample_size):
    """Extract data from Keen.com"""
    app = ctx.obj['app']
    app.extract_keen_data(sample_mode=sample, sample_size=sample_size)


@cli.command()
@click.option('--recordings-dir', help='Directory containing voice recordings')
@click.pass_context
def process_voice(ctx, recordings_dir):
    """Process Google Voice recordings"""
    app = ctx.obj['app']
    app.process_voice_recordings(recordings_dir)


@cli.command()
@click.pass_context
def correlate(ctx):
    """Correlate voice recordings with call logs"""
    app = ctx.obj['app']
    app.correlate_data()


@cli.command()
@click.option('--formats', multiple=True, help='Dataset formats to build')
@click.pass_context
def build_datasets(ctx, formats):
    """Build LLM training datasets"""
    app = ctx.obj['app']
    formats_list = list(formats) if formats else None
    app.build_datasets(formats_list)


@cli.command()
@click.option('--sample', is_flag=True, help='Run in sample mode')
@click.option('--sample-size', default=10, help='Sample size for testing')
@click.pass_context
def run_pipeline(ctx, sample, sample_size):
    """Run the complete pipeline"""
    app = ctx.obj['app']
    app.run_full_pipeline(sample_mode=sample, sample_size=sample_size)


@cli.command()
@click.pass_context
def status(ctx):
    """Show current status and statistics"""
    app = ctx.obj['app']
    
    with app.db_manager.get_session() as db_session:
        stats = app.dataset_formatter.generate_dataset_summary(db_session)
        correlation_stats = app.correlator.get_correlation_statistics(db_session)
    
    click.echo("=== AstralSubstance Clone Status ===")
    click.echo(f"Customers: {stats['customers']}")
    click.echo(f"Chats: {stats['chats']}")
    click.echo(f"Calls: {stats['calls']}")
    click.echo(f"Recordings: {stats['recordings']}")
    click.echo(f"Correlation: {correlation_stats}")


if __name__ == '__main__':
    cli()
