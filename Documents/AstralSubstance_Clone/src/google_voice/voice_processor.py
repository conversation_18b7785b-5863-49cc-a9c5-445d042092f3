"""
Google Voice Recording Processor

Scrapes Google Voice recordings via web API, extracts metadata, downloads audio files,
and optionally transcribes them for correlation with Keen.com call logs.
"""

import os
import re
import json
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Generator, Tuple
import mimetypes
from urllib.parse import urlparse, parse_qs

import requests
from loguru import logger
from sqlalchemy.orm import Session
from bs4 import BeautifulSoup
import librosa
import soundfile as sf

try:
    from ..models.database import VoiceRecording, DatabaseManager, VoiceRecordingData
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from models.database import VoiceRecording, DatabaseManager, VoiceRecordingData


class GoogleVoiceAuthManager:
    """Manages Google Voice authentication using session cookies"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.session = requests.Session()
        self.cookies = {}
        self._setup_session_headers()
        self._load_cookies_from_config()

    def _setup_session_headers(self):
        """Setup default headers for Google Voice requests"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'cross-site',
            'Priority': 'u=0, i'
        })

    def _load_cookies_from_config(self):
        """Load cookies from configuration"""
        config_cookies = self.config.get('google_voice', {}).get('cookies', {})
        if config_cookies and any(v for v in config_cookies.values() if v and not v.startswith('YOUR_')):
            logger.info("Loading Google Voice cookies from configuration")
            self.update_cookies(config_cookies)

    def update_cookies(self, cookies_dict: Dict[str, str]):
        """Update session cookies"""
        self.cookies = cookies_dict
        for name, value in cookies_dict.items():
            self.session.cookies.set(name, value)
        logger.info("Updated Google Voice authentication cookies")

    def validate_session(self) -> bool:
        """Validate current session by accessing Google Voice"""
        try:
            response = self.session.get('https://voice.google.com/u/0/calls', timeout=30)
            return response.status_code == 200 and 'voice.google.com' in response.url
        except Exception as e:
            logger.error(f"Google Voice session validation failed: {e}")
            return False


class GoogleVoiceProcessor:
    """
    Scrapes Google Voice recordings via web API and processes them

    Features:
    - Google Voice calls page scraping
    - Recording metadata extraction
    - Audio file downloading
    - Optional audio transcription
    - Database storage
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.voice_config = config.get('google_voice', {})

        # Audio settings
        self.audio_config = self.voice_config.get('audio', {})
        self.target_sample_rate = self.audio_config.get('sample_rate', 16000)
        self.normalize_audio = self.audio_config.get('normalize_audio', True)

        # Storage settings
        self.recordings_dir = Path(self.voice_config.get('recordings_directory', 'data/raw/voice_recordings'))
        self.recordings_dir.mkdir(parents=True, exist_ok=True)

        # Supported formats (including Google Voice formats)
        self.supported_formats = set(self.voice_config.get('supported_formats', ['.wav', '.mp3', '.m4a', '.ogg', '.webm']))

        # Keen.com phone number for filtering
        self.keen_phone_number = self.voice_config.get('keen_phone_number', '(*************')
        self.keen_phone_variants = self._generate_phone_variants(self.keen_phone_number)

        # Authentication
        self.auth_manager = GoogleVoiceAuthManager(config)

        # Database manager
        self.db_manager = None

    def set_database_manager(self, db_manager: DatabaseManager):
        """Set database manager for storing recordings"""
        self.db_manager = db_manager

    def update_google_voice_cookies(self, cookies_dict: Dict[str, str]):
        """Update Google Voice authentication cookies"""
        self.auth_manager.update_cookies(cookies_dict)

    def _generate_phone_variants(self, phone_number: str) -> List[str]:
        """
        Generate different variants of the phone number for matching

        Args:
            phone_number: Base phone number (e.g., "(*************")

        Returns:
            List of phone number variants
        """
        # Extract digits only
        digits_only = ''.join(filter(str.isdigit, phone_number))

        variants = [
            phone_number,                           # (*************
            digits_only,                           # 8002755336
            f"{digits_only[:3]}-{digits_only[3:6]}-{digits_only[6:]}",  # ************
            f"({digits_only[:3]}) {digits_only[3:6]}-{digits_only[6:]}", # (*************
            f"+1{digits_only}",                    # +18002755336
            f"1{digits_only}",                     # 18002755336
            f"{digits_only[:3]}.{digits_only[3:6]}.{digits_only[6:]}", # ************
            f"{digits_only[:3]} {digits_only[3:6]} {digits_only[6:]}", # ************
        ]

        return list(set(variants))  # Remove duplicates

    def _is_keen_related_call(self, call_data: Dict[str, Any]) -> bool:
        """
        Check if a call is related to Keen.com based on phone number

        Args:
            call_data: Call data dictionary

        Returns:
            True if call is from/to Keen.com number
        """
        # Check various fields that might contain the phone number
        phone_fields = [
            'phone_number', 'number', 'caller_id', 'from_number', 'to_number',
            'contact_number', 'display_number', 'formatted_number'
        ]

        for field in phone_fields:
            if field in call_data:
                phone_value = str(call_data[field])
                if any(variant in phone_value for variant in self.keen_phone_variants):
                    return True

        # Also check in raw text content if available
        raw_content = str(call_data.get('raw_content', ''))
        if any(variant in raw_content for variant in self.keen_phone_variants):
            return True

        return False

    def scrape_google_voice_recordings(self) -> Dict[str, Any]:
        """
        Scrape Google Voice recordings from the web interface

        Returns:
            Dictionary with scraping statistics
        """
        logger.info("Starting Google Voice recordings scrape")

        if not self.auth_manager.validate_session():
            raise ValueError("Invalid Google Voice session. Please update cookies.")

        stats = {
            'calls_found': 0,
            'keen_calls_found': 0,
            'recordings_found': 0,
            'recordings_downloaded': 0,
            'recordings_failed': 0,
            'errors': []
        }

        try:
            # Get the calls page
            calls_data = self._scrape_calls_page()
            stats['calls_found'] = len(calls_data)

            # Filter for Keen.com related calls
            keen_calls = [call for call in calls_data if self._is_keen_related_call(call)]
            stats['keen_calls_found'] = len(keen_calls)

            logger.info(f"Found {len(keen_calls)} Keen.com related calls out of {len(calls_data)} total calls")

            # Process each Keen.com call to find recordings
            for call_data in keen_calls:
                try:
                    if call_data.get('has_recording', False):
                        stats['recordings_found'] += 1

                        # Download the recording
                        recording_data = self._download_recording(call_data)

                        if recording_data:
                            stats['recordings_downloaded'] += 1

                            # Store in database if available
                            if self.db_manager:
                                self._store_recording(recording_data)

                            logger.debug(f"Downloaded recording: {call_data['call_id']}")
                        else:
                            stats['recordings_failed'] += 1

                except Exception as e:
                    stats['recordings_failed'] += 1
                    error_msg = f"Failed to process call {call_data.get('call_id', 'unknown')}: {str(e)}"
                    stats['errors'].append(error_msg)
                    logger.error(error_msg)
                    continue

        except Exception as e:
            logger.error(f"Google Voice scraping failed: {e}")
            stats['errors'].append(f"Scraping error: {str(e)}")

        logger.info(f"Google Voice scraping completed: {stats}")
        return stats

    def _scrape_calls_page(self) -> List[Dict[str, Any]]:
        """
        Scrape the Google Voice calls page to extract call metadata

        Returns:
            List of call data dictionaries
        """
        logger.info("Scraping Google Voice calls page")

        try:
            response = self.auth_manager.session.get('https://voice.google.com/u/0/calls', timeout=30)
            response.raise_for_status()

            # Parse the HTML to extract call data
            soup = BeautifulSoup(response.content, 'html.parser')

            # Look for JavaScript data containing call information
            # Google Voice typically embeds data in script tags
            calls_data = []

            # Find script tags that might contain call data
            script_tags = soup.find_all('script')

            for script in script_tags:
                if script.string and 'calls' in script.string.lower():
                    # Try to extract JSON data from the script
                    calls_data.extend(self._extract_calls_from_script(script.string))

            # If no data found in scripts, try to parse the DOM structure
            if not calls_data:
                calls_data = self._extract_calls_from_dom(soup)

            logger.info(f"Found {len(calls_data)} calls on the page")
            return calls_data

        except Exception as e:
            logger.error(f"Failed to scrape calls page: {e}")
            return []

    def _extract_calls_from_script(self, script_content: str) -> List[Dict[str, Any]]:
        """
        Extract call data from JavaScript content

        Args:
            script_content: JavaScript content from script tag

        Returns:
            List of call data dictionaries
        """
        calls_data = []

        try:
            # Look for patterns that might contain call data
            # Google Voice often uses patterns like: ["call_id", "timestamp", "duration", ...]

            # Pattern for call IDs (like the one in your example)
            call_id_pattern = r'["\']([A-Z0-9]{30,50})["\']'
            call_ids = re.findall(call_id_pattern, script_content)

            # Pattern for timestamps (Unix timestamps or ISO dates)
            timestamp_pattern = r'["\'](\d{10,13})["\']|["\'](\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})["\']'
            timestamps = re.findall(timestamp_pattern, script_content)

            # Try to find JSON objects that might contain call data
            json_pattern = r'\{[^{}]*"[^"]*call[^"]*"[^{}]*\}'
            json_matches = re.findall(json_pattern, script_content, re.IGNORECASE)

            for json_str in json_matches:
                try:
                    call_obj = json.loads(json_str)
                    if self._is_valid_call_object(call_obj):
                        calls_data.append(self._normalize_call_data(call_obj))
                except json.JSONDecodeError:
                    continue

            # If we found call IDs but no complete objects, create basic call data
            if call_ids and not calls_data:
                for call_id in call_ids:
                    if len(call_id) > 20:  # Filter out short strings that aren't call IDs
                        # Try to find phone number context around this call ID
                        phone_context = self._extract_phone_context(script_content, call_id)

                        calls_data.append({
                            'call_id': call_id,
                            'has_recording': True,  # Assume recordings exist
                            'timestamp': None,
                            'duration': None,
                            'phone_number': phone_context.get('phone_number'),
                            'contact_name': phone_context.get('contact_name'),
                            'raw_content': phone_context.get('raw_content', ''),
                            'source': 'script_extraction'
                        })

        except Exception as e:
            logger.error(f"Error extracting calls from script: {e}")

        return calls_data

    def _extract_calls_from_dom(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        Extract call data from DOM structure

        Args:
            soup: BeautifulSoup object of the page

        Returns:
            List of call data dictionaries
        """
        calls_data = []

        try:
            # Look for common patterns in Google Voice DOM
            # This might need adjustment based on the actual DOM structure

            # Look for elements that might contain call information
            call_elements = soup.find_all(['div', 'li', 'tr'], class_=re.compile(r'call|item|row', re.I))

            for element in call_elements:
                # Look for call ID patterns in data attributes or text
                call_id = None

                # Check data attributes
                for attr in element.attrs:
                    if 'id' in attr.lower() or 'call' in attr.lower():
                        value = element.attrs[attr]
                        if isinstance(value, str) and len(value) > 20:
                            call_id = value
                            break

                # Check text content for call ID patterns
                if not call_id:
                    text = element.get_text()
                    call_id_match = re.search(r'[A-Z0-9]{30,50}', text)
                    if call_id_match:
                        call_id = call_id_match.group()

                if call_id:
                    # Look for phone number in the same element
                    element_text = element.get_text()
                    phone_number = self._extract_phone_from_text(element_text)

                    calls_data.append({
                        'call_id': call_id,
                        'has_recording': True,  # Assume recordings exist
                        'timestamp': None,
                        'duration': None,
                        'phone_number': phone_number,
                        'raw_content': element_text,
                        'source': 'dom_extraction'
                    })

        except Exception as e:
            logger.error(f"Error extracting calls from DOM: {e}")

        return calls_data

    def _extract_phone_context(self, script_content: str, call_id: str) -> Dict[str, Any]:
        """
        Extract phone number and context around a call ID

        Args:
            script_content: JavaScript content
            call_id: Call ID to find context for

        Returns:
            Dictionary with phone context information
        """
        context = {
            'phone_number': None,
            'contact_name': None,
            'raw_content': ''
        }

        try:
            # Find the position of the call ID in the script
            call_id_pos = script_content.find(call_id)
            if call_id_pos == -1:
                return context

            # Extract a window of text around the call ID (500 chars before and after)
            start_pos = max(0, call_id_pos - 500)
            end_pos = min(len(script_content), call_id_pos + len(call_id) + 500)
            context_window = script_content[start_pos:end_pos]
            context['raw_content'] = context_window

            # Look for phone number patterns in the context
            phone_patterns = [
                r'\(\d{3}\)\s*\d{3}-\d{4}',  # (*************
                r'\d{3}-\d{3}-\d{4}',        # ************
                r'\d{3}\.\d{3}\.\d{4}',      # ************
                r'\d{10}',                   # 8002755336
                r'\+1\d{10}',                # +18002755336
            ]

            for pattern in phone_patterns:
                matches = re.findall(pattern, context_window)
                for match in matches:
                    # Check if this matches our Keen.com number
                    if any(variant in match for variant in self.keen_phone_variants):
                        context['phone_number'] = match
                        break
                if context['phone_number']:
                    break

            # Look for contact names (common patterns in Google Voice)
            name_patterns = [
                r'"name":\s*"([^"]+)"',
                r'"displayName":\s*"([^"]+)"',
                r'"contactName":\s*"([^"]+)"',
            ]

            for pattern in name_patterns:
                matches = re.findall(pattern, context_window)
                if matches:
                    context['contact_name'] = matches[0]
                    break

        except Exception as e:
            logger.debug(f"Error extracting phone context for {call_id}: {e}")

        return context

    def _extract_phone_from_text(self, text: str) -> Optional[str]:
        """
        Extract phone number from text, prioritizing Keen.com number

        Args:
            text: Text to search for phone numbers

        Returns:
            Phone number if found, None otherwise
        """
        # Phone number patterns
        phone_patterns = [
            r'\(\d{3}\)\s*\d{3}-\d{4}',  # (*************
            r'\d{3}-\d{3}-\d{4}',        # ************
            r'\d{3}\.\d{3}\.\d{4}',      # ************
            r'\+1\d{10}',                # +18002755336
            r'\d{10}',                   # 8002755336 (last to avoid partial matches)
        ]

        for pattern in phone_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                # Check if this matches our Keen.com number variants
                if any(variant in match for variant in self.keen_phone_variants):
                    return match

        return None

    def _is_valid_call_object(self, call_obj: Dict[str, Any]) -> bool:
        """Check if a parsed object contains valid call data"""
        required_fields = ['id', 'call_id', 'callId']
        return any(field in call_obj for field in required_fields)

    def _normalize_call_data(self, call_obj: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize call object to standard format"""
        call_id = call_obj.get('id') or call_obj.get('call_id') or call_obj.get('callId')

        return {
            'call_id': call_id,
            'has_recording': call_obj.get('has_recording', True),
            'timestamp': call_obj.get('timestamp') or call_obj.get('date'),
            'duration': call_obj.get('duration'),
            'source': 'json_extraction'
        }

    def _download_recording(self, call_data: Dict[str, Any]) -> Optional[VoiceRecordingData]:
        """
        Download a recording file from Google Voice

        Args:
            call_data: Call data dictionary containing call_id

        Returns:
            VoiceRecordingData object or None if download failed
        """
        call_id = call_data['call_id']

        try:
            # Construct the recording URL based on the pattern from your example
            recording_url = f"https://voice.google.com/u/0/a/cr/cra:{call_id}"

            # Setup headers for audio download
            headers = {
                'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
                'Accept': 'audio/webm,audio/ogg,audio/wav,audio/*;q=0.9,application/ogg;q=0.7,video/*;q=0.6,*/*;q=0.5',
                'Accept-Language': 'en-US,en;q=0.5',
                'Range': 'bytes=0-',
                'Connection': 'keep-alive',
                'Referer': f'https://voice.google.com/u/0/calls?itemId=c.{call_id}',
                'Sec-Fetch-Dest': 'audio',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'same-origin'
            }

            # Download the recording
            response = self.auth_manager.session.get(recording_url, headers=headers, timeout=60)
            response.raise_for_status()

            # Determine file format from content type
            content_type = response.headers.get('content-type', '')
            if 'webm' in content_type:
                file_extension = '.webm'
            elif 'ogg' in content_type:
                file_extension = '.ogg'
            elif 'wav' in content_type:
                file_extension = '.wav'
            else:
                file_extension = '.webm'  # Default

            # Create filename
            timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"gvoice_{call_id}_{timestamp_str}{file_extension}"
            file_path = self.recordings_dir / filename

            # Save the file
            with open(file_path, 'wb') as f:
                f.write(response.content)

            # Extract timestamp from call data or use current time
            timestamp = self._parse_call_timestamp(call_data)

            # Analyze the audio file
            audio_metadata = self._analyze_audio_file(file_path)

            # Create recording data with phone number information
            recording_data = VoiceRecordingData(
                file_path=str(file_path),
                timestamp=timestamp,
                duration_seconds=audio_metadata.get('duration_seconds') if audio_metadata else None,
                file_size_bytes=len(response.content)
            )

            # Add phone number information if available
            if hasattr(recording_data, 'phone_number'):
                recording_data.phone_number = call_data.get('phone_number')
            if hasattr(recording_data, 'contact_name'):
                recording_data.contact_name = call_data.get('contact_name')

            logger.info(f"Downloaded recording: {filename} ({len(response.content)} bytes)")
            return recording_data

        except Exception as e:
            logger.error(f"Failed to download recording for call {call_id}: {e}")
            return None

    def _parse_call_timestamp(self, call_data: Dict[str, Any]) -> datetime:
        """
        Parse timestamp from call data

        Args:
            call_data: Call data dictionary

        Returns:
            Datetime object
        """
        timestamp_value = call_data.get('timestamp')

        if timestamp_value:
            try:
                # Try parsing as Unix timestamp
                if isinstance(timestamp_value, (int, float)):
                    return datetime.fromtimestamp(timestamp_value)
                elif isinstance(timestamp_value, str):
                    # Try parsing as Unix timestamp string
                    if timestamp_value.isdigit():
                        timestamp_int = int(timestamp_value)
                        # Handle both seconds and milliseconds
                        if timestamp_int > 1e10:  # Milliseconds
                            timestamp_int = timestamp_int / 1000
                        return datetime.fromtimestamp(timestamp_int)
                    else:
                        # Try parsing as ISO format
                        return datetime.fromisoformat(timestamp_value.replace('Z', '+00:00'))
            except Exception as e:
                logger.warning(f"Failed to parse timestamp {timestamp_value}: {e}")

        # Fallback to current time
        return datetime.now()

    def process_recordings_directory(self, recordings_dir: str) -> Dict[str, Any]:
        """
        Process all recordings in a directory

        Args:
            recordings_dir: Path to directory containing recordings

        Returns:
            Dictionary with processing statistics
        """
        logger.info(f"Processing recordings directory: {recordings_dir}")

        recordings_path = Path(recordings_dir)
        if not recordings_path.exists():
            raise FileNotFoundError(f"Recordings directory not found: {recordings_dir}")

        stats = {
            'files_found': 0,
            'files_processed': 0,
            'files_failed': 0,
            'files_skipped': 0,
            'total_duration_seconds': 0,
            'errors': []
        }

        # Find all audio files
        audio_files = list(self._find_audio_files(recordings_path))
        stats['files_found'] = len(audio_files)

        logger.info(f"Found {len(audio_files)} audio files")

        if not self.db_manager:
            logger.warning("No database manager set, processing without storage")

        # Process each file
        for file_path in audio_files:
            try:
                # Check if already processed
                if self.db_manager and self._is_file_already_processed(file_path):
                    stats['files_skipped'] += 1
                    logger.debug(f"Skipping already processed file: {file_path.name}")
                    continue

                # Process the recording
                recording_data = self._process_recording_file(file_path)

                if recording_data:
                    stats['files_processed'] += 1
                    if recording_data.duration_seconds:
                        stats['total_duration_seconds'] += recording_data.duration_seconds

                    # Store in database if available
                    if self.db_manager:
                        self._store_recording(recording_data)

                    logger.debug(f"Processed recording: {file_path.name}")
                else:
                    stats['files_failed'] += 1

            except Exception as e:
                stats['files_failed'] += 1
                error_msg = f"Failed to process {file_path.name}: {str(e)}"
                stats['errors'].append(error_msg)
                logger.error(error_msg)
                continue

        logger.info(f"Recording processing completed: {stats}")
        return stats

    def _find_audio_files(self, directory: Path) -> Generator[Path, None, None]:
        """
        Find all audio files in directory and subdirectories

        Args:
            directory: Directory to search

        Yields:
            Path objects for audio files
        """
        for file_path in directory.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                yield file_path

    def _is_file_already_processed(self, file_path: Path) -> bool:
        """
        Check if file has already been processed

        Args:
            file_path: Path to audio file

        Returns:
            True if file is already in database
        """
        if not self.db_manager:
            return False

        try:
            with self.db_manager.get_session() as db_session:
                existing = db_session.query(VoiceRecording).filter(
                    VoiceRecording.file_path == str(file_path)
                ).first()
                return existing is not None
        except Exception as e:
            logger.error(f"Error checking if file is processed: {e}")
            return False

    def _process_recording_file(self, file_path: Path) -> Optional[VoiceRecordingData]:
        """
        Process a single recording file

        Args:
            file_path: Path to audio file

        Returns:
            VoiceRecordingData object or None if processing failed
        """
        try:
            # Extract basic file metadata
            file_stats = file_path.stat()
            file_size = file_stats.st_size

            # Extract timestamp from filename or file modification time
            timestamp = self._extract_timestamp_from_file(file_path)

            # Load and analyze audio
            audio_metadata = self._analyze_audio_file(file_path)

            if not audio_metadata:
                logger.warning(f"Failed to analyze audio file: {file_path}")
                return None

            # Create recording data
            recording_data = VoiceRecordingData(
                file_path=str(file_path),
                timestamp=timestamp,
                duration_seconds=audio_metadata.get('duration_seconds'),
                file_size_bytes=file_size
            )

            return recording_data

        except Exception as e:
            logger.error(f"Error processing recording file {file_path}: {e}")
            return None

    def _extract_timestamp_from_file(self, file_path: Path) -> datetime:
        """
        Extract timestamp from filename or file metadata

        Args:
            file_path: Path to audio file

        Returns:
            Datetime object representing the recording time
        """
        # Try to extract from filename first
        filename = file_path.stem

        # Common Google Voice filename patterns
        timestamp_patterns = [
            '%Y-%m-%d_%H-%M-%S',  # 2024-01-15_14-30-45
            '%Y%m%d_%H%M%S',      # 20240115_143045
            '%Y-%m-%d %H-%M-%S',  # 2024-01-15 14-30-45
        ]

        for pattern in timestamp_patterns:
            try:
                # Extract potential timestamp from filename
                for part in filename.split('_'):
                    try:
                        timestamp = datetime.strptime(part, pattern)
                        logger.debug(f"Extracted timestamp from filename: {timestamp}")
                        return timestamp
                    except ValueError:
                        continue
            except Exception:
                continue

        # Fallback to file modification time
        file_stats = file_path.stat()
        timestamp = datetime.fromtimestamp(file_stats.st_mtime)
        logger.debug(f"Using file modification time as timestamp: {timestamp}")
        return timestamp

    def _analyze_audio_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        Analyze audio file and extract metadata

        Args:
            file_path: Path to audio file

        Returns:
            Dictionary with audio metadata
        """
        try:
            # Load audio file
            audio_data, sample_rate = librosa.load(str(file_path), sr=None)

            # Calculate duration
            duration_seconds = len(audio_data) / sample_rate

            # Get audio format info
            audio_format = file_path.suffix.lower().lstrip('.')

            # Determine number of channels
            if audio_data.ndim == 1:
                channels = 1
            else:
                channels = audio_data.shape[0]

            metadata = {
                'duration_seconds': int(duration_seconds),
                'sample_rate': sample_rate,
                'audio_format': audio_format,
                'channels': channels,
                'total_samples': len(audio_data)
            }

            logger.debug(f"Audio analysis complete: {metadata}")
            return metadata

        except Exception as e:
            logger.error(f"Failed to analyze audio file {file_path}: {e}")
            return None

    def _store_recording(self, recording_data: VoiceRecordingData):
        """
        Store recording data in database

        Args:
            recording_data: Recording data to store
        """
        try:
            with self.db_manager.get_session() as db_session:
                # Create VoiceRecording object
                recording = VoiceRecording(
                    file_path=recording_data.file_path,
                    original_filename=Path(recording_data.file_path).name,
                    timestamp=recording_data.timestamp,
                    duration_seconds=recording_data.duration_seconds,
                    file_size_bytes=recording_data.file_size_bytes,
                    phone_number=getattr(recording_data, 'phone_number', None),
                    contact_name=getattr(recording_data, 'contact_name', None),
                    processed=True,
                    processed_at=datetime.now()
                )

                db_session.add(recording)
                db_session.commit()

                logger.debug(f"Stored recording in database: {recording.original_filename}")

        except Exception as e:
            logger.error(f"Failed to store recording in database: {e}")
            raise

    def transcribe_recording(self, recording_id: int, transcription_method: str = 'whisper') -> bool:
        """
        Transcribe a recording using specified method

        Args:
            recording_id: Database ID of the recording
            transcription_method: Method to use for transcription

        Returns:
            True if transcription successful, False otherwise
        """
        if not self.db_manager:
            logger.error("No database manager available for transcription")
            return False

        try:
            with self.db_manager.get_session() as db_session:
                recording = db_session.query(VoiceRecording).get(recording_id)

                if not recording:
                    logger.error(f"Recording {recording_id} not found")
                    return False

                if not Path(recording.file_path).exists():
                    logger.error(f"Recording file not found: {recording.file_path}")
                    return False

                # Perform transcription based on method
                transcription = None
                confidence = None

                if transcription_method == 'whisper':
                    transcription, confidence = self._transcribe_with_whisper(recording.file_path)
                elif transcription_method == 'google':
                    transcription, confidence = self._transcribe_with_google(recording.file_path)
                else:
                    logger.error(f"Unsupported transcription method: {transcription_method}")
                    return False

                if transcription:
                    recording.transcription = transcription
                    recording.transcription_confidence = confidence
                    recording.transcription_method = transcription_method

                    db_session.add(recording)
                    db_session.commit()

                    logger.info(f"Transcribed recording {recording_id} using {transcription_method}")
                    return True
                else:
                    logger.warning(f"Transcription failed for recording {recording_id}")
                    return False

        except Exception as e:
            logger.error(f"Error transcribing recording {recording_id}: {e}")
            return False

    def _transcribe_with_whisper(self, file_path: str) -> tuple:
        """
        Transcribe audio using Whisper

        Args:
            file_path: Path to audio file

        Returns:
            Tuple of (transcription, confidence)
        """
        try:
            import whisper

            # Load Whisper model
            model = whisper.load_model("base")

            # Transcribe
            result = model.transcribe(file_path)

            transcription = result.get('text', '').strip()

            # Calculate average confidence from segments
            segments = result.get('segments', [])
            if segments:
                confidences = [seg.get('avg_logprob', 0) for seg in segments]
                avg_confidence = sum(confidences) / len(confidences)
                # Convert log probability to confidence score (0-1)
                confidence = max(0, min(1, (avg_confidence + 1) / 2))
            else:
                confidence = 0.5

            return transcription, confidence

        except ImportError:
            logger.error("Whisper not installed. Install with: pip install openai-whisper")
            return None, None
        except Exception as e:
            logger.error(f"Whisper transcription failed: {e}")
            return None, None

    def _transcribe_with_google(self, file_path: str) -> tuple:
        """
        Transcribe audio using Google Speech-to-Text

        Args:
            file_path: Path to audio file

        Returns:
            Tuple of (transcription, confidence)
        """
        try:
            import speech_recognition as sr

            recognizer = sr.Recognizer()

            # Load audio file
            with sr.AudioFile(file_path) as source:
                audio = recognizer.record(source)

            # Transcribe using Google
            transcription = recognizer.recognize_google(audio)
            confidence = 0.8  # Google doesn't provide confidence scores via this API

            return transcription, confidence

        except ImportError:
            logger.error("SpeechRecognition not installed. Install with: pip install SpeechRecognition")
            return None, None
        except Exception as e:
            logger.error(f"Google transcription failed: {e}")
            return None, None

    def get_processing_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about processed recordings

        Returns:
            Dictionary with processing statistics
        """
        if not self.db_manager:
            return {"error": "No database manager available"}

        try:
            with self.db_manager.get_session() as db_session:
                total_recordings = db_session.query(VoiceRecording).count()
                processed_recordings = db_session.query(VoiceRecording).filter(
                    VoiceRecording.processed == True
                ).count()
                transcribed_recordings = db_session.query(VoiceRecording).filter(
                    VoiceRecording.transcription.isnot(None)
                ).count()

                # Calculate total duration
                duration_result = db_session.query(
                    db_session.query(VoiceRecording.duration_seconds).filter(
                        VoiceRecording.duration_seconds.isnot(None)
                    ).subquery().c.duration_seconds
                ).all()

                total_duration = sum(d[0] for d in duration_result if d[0])

                return {
                    'total_recordings': total_recordings,
                    'processed_recordings': processed_recordings,
                    'transcribed_recordings': transcribed_recordings,
                    'total_duration_seconds': total_duration,
                    'total_duration_hours': round(total_duration / 3600, 2),
                    'processing_rate': round((processed_recordings / total_recordings * 100), 2) if total_recordings > 0 else 0,
                    'transcription_rate': round((transcribed_recordings / total_recordings * 100), 2) if total_recordings > 0 else 0
                }

        except Exception as e:
            logger.error(f"Error getting processing statistics: {e}")
            return {"error": str(e)}
