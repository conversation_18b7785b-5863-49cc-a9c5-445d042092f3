"""
Google Voice Recording Processor

Processes Google Voice recordings, extracts metadata, and optionally
transcribes audio files for correlation with Keen.com call logs.
"""

import os
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Generator
import mimetypes

from loguru import logger
from sqlalchemy.orm import Session
import librosa
import soundfile as sf

from ..models.database import VoiceRecording, DatabaseManager, VoiceRecordingData


class GoogleVoiceProcessor:
    """
    Processes Google Voice recordings and extracts metadata
    
    Features:
    - Audio file discovery and validation
    - Metadata extraction (duration, format, etc.)
    - Optional audio transcription
    - Database storage
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.voice_config = config.get('google_voice', {})
        
        # Audio settings
        self.audio_config = self.voice_config.get('audio', {})
        self.target_sample_rate = self.audio_config.get('sample_rate', 16000)
        self.normalize_audio = self.audio_config.get('normalize_audio', True)
        self.remove_silence = self.audio_config.get('remove_silence', True)
        
        # Supported formats
        self.supported_formats = set(self.voice_config.get('supported_formats', ['.wav', '.mp3', '.m4a', '.ogg']))
        
        # Database manager
        self.db_manager = None
    
    def set_database_manager(self, db_manager: DatabaseManager):
        """Set database manager for storing recordings"""
        self.db_manager = db_manager
    
    def process_recordings_directory(self, recordings_dir: str) -> Dict[str, Any]:
        """
        Process all recordings in a directory
        
        Args:
            recordings_dir: Path to directory containing recordings
            
        Returns:
            Dictionary with processing statistics
        """
        logger.info(f"Processing recordings directory: {recordings_dir}")
        
        recordings_path = Path(recordings_dir)
        if not recordings_path.exists():
            raise FileNotFoundError(f"Recordings directory not found: {recordings_dir}")
        
        stats = {
            'files_found': 0,
            'files_processed': 0,
            'files_failed': 0,
            'files_skipped': 0,
            'total_duration_seconds': 0,
            'errors': []
        }
        
        # Find all audio files
        audio_files = list(self._find_audio_files(recordings_path))
        stats['files_found'] = len(audio_files)
        
        logger.info(f"Found {len(audio_files)} audio files")
        
        if not self.db_manager:
            logger.warning("No database manager set, processing without storage")
        
        # Process each file
        for file_path in audio_files:
            try:
                # Check if already processed
                if self.db_manager and self._is_file_already_processed(file_path):
                    stats['files_skipped'] += 1
                    logger.debug(f"Skipping already processed file: {file_path.name}")
                    continue
                
                # Process the recording
                recording_data = self._process_recording_file(file_path)
                
                if recording_data:
                    stats['files_processed'] += 1
                    if recording_data.duration_seconds:
                        stats['total_duration_seconds'] += recording_data.duration_seconds
                    
                    # Store in database if available
                    if self.db_manager:
                        self._store_recording(recording_data)
                    
                    logger.debug(f"Processed recording: {file_path.name}")
                else:
                    stats['files_failed'] += 1
                
            except Exception as e:
                stats['files_failed'] += 1
                error_msg = f"Failed to process {file_path.name}: {str(e)}"
                stats['errors'].append(error_msg)
                logger.error(error_msg)
                continue
        
        logger.info(f"Recording processing completed: {stats}")
        return stats
    
    def _find_audio_files(self, directory: Path) -> Generator[Path, None, None]:
        """
        Find all audio files in directory and subdirectories
        
        Args:
            directory: Directory to search
            
        Yields:
            Path objects for audio files
        """
        for file_path in directory.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                yield file_path
    
    def _is_file_already_processed(self, file_path: Path) -> bool:
        """
        Check if file has already been processed
        
        Args:
            file_path: Path to audio file
            
        Returns:
            True if file is already in database
        """
        if not self.db_manager:
            return False
        
        try:
            with self.db_manager.get_session() as db_session:
                existing = db_session.query(VoiceRecording).filter(
                    VoiceRecording.file_path == str(file_path)
                ).first()
                return existing is not None
        except Exception as e:
            logger.error(f"Error checking if file is processed: {e}")
            return False
    
    def _process_recording_file(self, file_path: Path) -> Optional[VoiceRecordingData]:
        """
        Process a single recording file
        
        Args:
            file_path: Path to audio file
            
        Returns:
            VoiceRecordingData object or None if processing failed
        """
        try:
            # Extract basic file metadata
            file_stats = file_path.stat()
            file_size = file_stats.st_size
            
            # Extract timestamp from filename or file modification time
            timestamp = self._extract_timestamp_from_file(file_path)
            
            # Load and analyze audio
            audio_metadata = self._analyze_audio_file(file_path)
            
            if not audio_metadata:
                logger.warning(f"Failed to analyze audio file: {file_path}")
                return None
            
            # Create recording data
            recording_data = VoiceRecordingData(
                file_path=str(file_path),
                timestamp=timestamp,
                duration_seconds=audio_metadata.get('duration_seconds'),
                file_size_bytes=file_size
            )
            
            return recording_data
            
        except Exception as e:
            logger.error(f"Error processing recording file {file_path}: {e}")
            return None
    
    def _extract_timestamp_from_file(self, file_path: Path) -> datetime:
        """
        Extract timestamp from filename or file metadata
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Datetime object representing the recording time
        """
        # Try to extract from filename first
        filename = file_path.stem
        
        # Common Google Voice filename patterns
        timestamp_patterns = [
            '%Y-%m-%d_%H-%M-%S',  # 2024-01-15_14-30-45
            '%Y%m%d_%H%M%S',      # 20240115_143045
            '%Y-%m-%d %H-%M-%S',  # 2024-01-15 14-30-45
        ]
        
        for pattern in timestamp_patterns:
            try:
                # Extract potential timestamp from filename
                for part in filename.split('_'):
                    try:
                        timestamp = datetime.strptime(part, pattern)
                        logger.debug(f"Extracted timestamp from filename: {timestamp}")
                        return timestamp
                    except ValueError:
                        continue
            except Exception:
                continue
        
        # Fallback to file modification time
        file_stats = file_path.stat()
        timestamp = datetime.fromtimestamp(file_stats.st_mtime)
        logger.debug(f"Using file modification time as timestamp: {timestamp}")
        return timestamp
    
    def _analyze_audio_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        Analyze audio file and extract metadata
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Dictionary with audio metadata
        """
        try:
            # Load audio file
            audio_data, sample_rate = librosa.load(str(file_path), sr=None)
            
            # Calculate duration
            duration_seconds = len(audio_data) / sample_rate
            
            # Get audio format info
            audio_format = file_path.suffix.lower().lstrip('.')
            
            # Determine number of channels
            if audio_data.ndim == 1:
                channels = 1
            else:
                channels = audio_data.shape[0]
            
            metadata = {
                'duration_seconds': int(duration_seconds),
                'sample_rate': sample_rate,
                'audio_format': audio_format,
                'channels': channels,
                'total_samples': len(audio_data)
            }
            
            logger.debug(f"Audio analysis complete: {metadata}")
            return metadata
            
        except Exception as e:
            logger.error(f"Failed to analyze audio file {file_path}: {e}")
            return None
    
    def _store_recording(self, recording_data: VoiceRecordingData):
        """
        Store recording data in database
        
        Args:
            recording_data: Recording data to store
        """
        try:
            with self.db_manager.get_session() as db_session:
                # Create VoiceRecording object
                recording = VoiceRecording(
                    file_path=recording_data.file_path,
                    original_filename=Path(recording_data.file_path).name,
                    timestamp=recording_data.timestamp,
                    duration_seconds=recording_data.duration_seconds,
                    file_size_bytes=recording_data.file_size_bytes,
                    processed=True,
                    processed_at=datetime.now()
                )
                
                db_session.add(recording)
                db_session.commit()
                
                logger.debug(f"Stored recording in database: {recording.original_filename}")
                
        except Exception as e:
            logger.error(f"Failed to store recording in database: {e}")
            raise
    
    def transcribe_recording(self, recording_id: int, transcription_method: str = 'whisper') -> bool:
        """
        Transcribe a recording using specified method
        
        Args:
            recording_id: Database ID of the recording
            transcription_method: Method to use for transcription
            
        Returns:
            True if transcription successful, False otherwise
        """
        if not self.db_manager:
            logger.error("No database manager available for transcription")
            return False
        
        try:
            with self.db_manager.get_session() as db_session:
                recording = db_session.query(VoiceRecording).get(recording_id)
                
                if not recording:
                    logger.error(f"Recording {recording_id} not found")
                    return False
                
                if not Path(recording.file_path).exists():
                    logger.error(f"Recording file not found: {recording.file_path}")
                    return False
                
                # Perform transcription based on method
                transcription = None
                confidence = None
                
                if transcription_method == 'whisper':
                    transcription, confidence = self._transcribe_with_whisper(recording.file_path)
                elif transcription_method == 'google':
                    transcription, confidence = self._transcribe_with_google(recording.file_path)
                else:
                    logger.error(f"Unsupported transcription method: {transcription_method}")
                    return False
                
                if transcription:
                    recording.transcription = transcription
                    recording.transcription_confidence = confidence
                    recording.transcription_method = transcription_method
                    
                    db_session.add(recording)
                    db_session.commit()
                    
                    logger.info(f"Transcribed recording {recording_id} using {transcription_method}")
                    return True
                else:
                    logger.warning(f"Transcription failed for recording {recording_id}")
                    return False
                
        except Exception as e:
            logger.error(f"Error transcribing recording {recording_id}: {e}")
            return False
    
    def _transcribe_with_whisper(self, file_path: str) -> tuple:
        """
        Transcribe audio using Whisper
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Tuple of (transcription, confidence)
        """
        try:
            import whisper
            
            # Load Whisper model
            model = whisper.load_model("base")
            
            # Transcribe
            result = model.transcribe(file_path)
            
            transcription = result.get('text', '').strip()
            
            # Calculate average confidence from segments
            segments = result.get('segments', [])
            if segments:
                confidences = [seg.get('avg_logprob', 0) for seg in segments]
                avg_confidence = sum(confidences) / len(confidences)
                # Convert log probability to confidence score (0-1)
                confidence = max(0, min(1, (avg_confidence + 1) / 2))
            else:
                confidence = 0.5
            
            return transcription, confidence
            
        except ImportError:
            logger.error("Whisper not installed. Install with: pip install openai-whisper")
            return None, None
        except Exception as e:
            logger.error(f"Whisper transcription failed: {e}")
            return None, None
    
    def _transcribe_with_google(self, file_path: str) -> tuple:
        """
        Transcribe audio using Google Speech-to-Text
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Tuple of (transcription, confidence)
        """
        try:
            import speech_recognition as sr
            
            recognizer = sr.Recognizer()
            
            # Load audio file
            with sr.AudioFile(file_path) as source:
                audio = recognizer.record(source)
            
            # Transcribe using Google
            transcription = recognizer.recognize_google(audio)
            confidence = 0.8  # Google doesn't provide confidence scores via this API
            
            return transcription, confidence
            
        except ImportError:
            logger.error("SpeechRecognition not installed. Install with: pip install SpeechRecognition")
            return None, None
        except Exception as e:
            logger.error(f"Google transcription failed: {e}")
            return None, None
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about processed recordings
        
        Returns:
            Dictionary with processing statistics
        """
        if not self.db_manager:
            return {"error": "No database manager available"}
        
        try:
            with self.db_manager.get_session() as db_session:
                total_recordings = db_session.query(VoiceRecording).count()
                processed_recordings = db_session.query(VoiceRecording).filter(
                    VoiceRecording.processed == True
                ).count()
                transcribed_recordings = db_session.query(VoiceRecording).filter(
                    VoiceRecording.transcription.isnot(None)
                ).count()
                
                # Calculate total duration
                duration_result = db_session.query(
                    db_session.query(VoiceRecording.duration_seconds).filter(
                        VoiceRecording.duration_seconds.isnot(None)
                    ).subquery().c.duration_seconds
                ).all()
                
                total_duration = sum(d[0] for d in duration_result if d[0])
                
                return {
                    'total_recordings': total_recordings,
                    'processed_recordings': processed_recordings,
                    'transcribed_recordings': transcribed_recordings,
                    'total_duration_seconds': total_duration,
                    'total_duration_hours': round(total_duration / 3600, 2),
                    'processing_rate': round((processed_recordings / total_recordings * 100), 2) if total_recordings > 0 else 0,
                    'transcription_rate': round((transcribed_recordings / total_recordings * 100), 2) if total_recordings > 0 else 0
                }
                
        except Exception as e:
            logger.error(f"Error getting processing statistics: {e}")
            return {"error": str(e)}
