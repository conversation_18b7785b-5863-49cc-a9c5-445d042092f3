"""
Whisper Audio Transcription Module

Handles audio transcription using OpenAI's Whisper model with speaker diarization,
timestamp alignment, and batch processing capabilities.
"""

import os
import time
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import tempfile
import subprocess

import torch
import whisper
import librosa
import numpy as np
from loguru import logger
from sqlalchemy.orm import Session

try:
    from ..models.database import VoiceRecording, DatabaseManager
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from models.database import VoiceRecording, DatabaseManager


class WhisperTranscriber:
    """
    Advanced Whisper transcription with speaker diarization and batch processing

    Features:
    - Multiple Whisper model sizes
    - Speaker diarization for customer/advisor separation
    - Batch processing for efficiency
    - Timestamp-accurate transcription
    - Quality scoring and validation
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.transcription_config = config.get('transcription', {})

        # Whisper model settings
        self.model_size = self.transcription_config.get('whisper_model', 'base')
        self.device = self._get_optimal_device()
        self.model = None  # Lazy loading

        # Processing settings
        self.batch_size = self.transcription_config.get('batch_size', 5)
        self.enable_diarization = self.transcription_config.get('enable_diarization', True)
        self.min_confidence = self.transcription_config.get('min_confidence', 0.6)

        # Audio preprocessing
        self.target_sample_rate = 16000
        self.normalize_audio = True

        # Database manager
        self.db_manager = None

        logger.info(f"Initialized Whisper transcriber with model: {self.model_size}, device: {self.device}")

    def _get_optimal_device(self) -> str:
        """Determine the best device for Whisper processing"""
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"  # Apple Silicon
        else:
            return "cpu"

    def _load_model(self):
        """Lazy load Whisper model"""
        if self.model is None:
            logger.info(f"Loading Whisper model: {self.model_size}")
            self.model = whisper.load_model(self.model_size, device=self.device)
            logger.info("Whisper model loaded successfully")

    def set_database_manager(self, db_manager: DatabaseManager):
        """Set database manager for storing transcriptions"""
        self.db_manager = db_manager

    def transcribe_recording(self, recording_id: int) -> Dict[str, Any]:
        """
        Transcribe a single recording

        Args:
            recording_id: Database ID of the recording

        Returns:
            Dictionary with transcription results
        """
        if not self.db_manager:
            raise ValueError("Database manager not set")

        try:
            with self.db_manager.get_session() as db_session:
                recording = db_session.query(VoiceRecording).get(recording_id)

                if not recording:
                    raise ValueError(f"Recording {recording_id} not found")

                if not Path(recording.file_path).exists():
                    raise ValueError(f"Audio file not found: {recording.file_path}")

                # Perform transcription
                result = self._transcribe_audio_file(recording.file_path)

                # Update database
                if result['success']:
                    recording.transcription = result['transcription']
                    recording.transcription_confidence = result['confidence']
                    recording.transcription_method = 'whisper'

                    # Store detailed results as JSON
                    if hasattr(recording, 'transcription_details'):
                        recording.transcription_details = json.dumps(result['details'])

                    db_session.add(recording)
                    db_session.commit()

                    logger.info(f"Transcribed recording {recording_id}: {len(result['transcription'])} characters")
                else:
                    logger.error(f"Transcription failed for recording {recording_id}: {result['error']}")

                return result

        except Exception as e:
            logger.error(f"Error transcribing recording {recording_id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'transcription': '',
                'confidence': 0.0
            }

    def batch_transcribe_recordings(self, recording_ids: List[int] = None) -> Dict[str, Any]:
        """
        Batch transcribe multiple recordings

        Args:
            recording_ids: List of recording IDs to transcribe (None for all untranscribed)

        Returns:
            Dictionary with batch transcription statistics
        """
        if not self.db_manager:
            raise ValueError("Database manager not set")

        stats = {
            'total_recordings': 0,
            'successful_transcriptions': 0,
            'failed_transcriptions': 0,
            'total_duration_seconds': 0,
            'processing_time_seconds': 0,
            'errors': []
        }

        start_time = time.time()

        try:
            with self.db_manager.get_session() as db_session:
                # Get recordings to transcribe
                if recording_ids:
                    recordings = db_session.query(VoiceRecording).filter(
                        VoiceRecording.id.in_(recording_ids)
                    ).all()
                else:
                    # Get all untranscribed recordings
                    recordings = db_session.query(VoiceRecording).filter(
                        VoiceRecording.transcription.is_(None)
                    ).all()

                stats['total_recordings'] = len(recordings)
                logger.info(f"Starting batch transcription of {len(recordings)} recordings")

                # Process in batches
                for i in range(0, len(recordings), self.batch_size):
                    batch = recordings[i:i + self.batch_size]

                    for recording in batch:
                        try:
                            result = self.transcribe_recording(recording.id)

                            if result['success']:
                                stats['successful_transcriptions'] += 1
                                if recording.duration_seconds:
                                    stats['total_duration_seconds'] += recording.duration_seconds
                            else:
                                stats['failed_transcriptions'] += 1
                                stats['errors'].append(f"Recording {recording.id}: {result['error']}")

                        except Exception as e:
                            stats['failed_transcriptions'] += 1
                            error_msg = f"Recording {recording.id}: {str(e)}"
                            stats['errors'].append(error_msg)
                            logger.error(error_msg)

                    # Progress update
                    processed = min(i + self.batch_size, len(recordings))
                    logger.info(f"Processed {processed}/{len(recordings)} recordings")

        except Exception as e:
            logger.error(f"Batch transcription failed: {e}")
            stats['errors'].append(f"Batch processing error: {str(e)}")

        stats['processing_time_seconds'] = time.time() - start_time

        logger.info(f"Batch transcription completed: {stats}")
        return stats

    def _transcribe_audio_file(self, file_path: str) -> Dict[str, Any]:
        """
        Transcribe a single audio file using Whisper

        Args:
            file_path: Path to audio file

        Returns:
            Dictionary with transcription results
        """
        try:
            # Load Whisper model
            self._load_model()

            # Preprocess audio
            audio_path = self._preprocess_audio(file_path)

            # Transcribe with Whisper
            logger.debug(f"Transcribing audio file: {file_path}")
            result = self.model.transcribe(
                audio_path,
                language='en',  # Assume English for Keen.com calls
                task='transcribe',
                verbose=False,
                word_timestamps=True,
                initial_prompt="This is a phone conversation between a customer and a psychic advisor."
            )

            # Process results
            transcription_result = self._process_whisper_result(result)

            # Clean up temporary files
            if audio_path != file_path:
                os.unlink(audio_path)

            return transcription_result

        except Exception as e:
            logger.error(f"Whisper transcription failed for {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'transcription': '',
                'confidence': 0.0,
                'details': {}
            }

    def _preprocess_audio(self, file_path: str) -> str:
        """
        Preprocess audio file for optimal Whisper performance

        Args:
            file_path: Path to original audio file

        Returns:
            Path to preprocessed audio file
        """
        try:
            # Load audio
            audio, sr = librosa.load(file_path, sr=self.target_sample_rate)

            # Normalize audio
            if self.normalize_audio:
                audio = librosa.util.normalize(audio)

            # Remove silence (optional)
            if self.transcription_config.get('remove_silence', True):
                audio, _ = librosa.effects.trim(audio, top_db=20)

            # Save preprocessed audio to temporary file
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            import soundfile as sf
            sf.write(temp_file.name, audio, self.target_sample_rate)

            return temp_file.name

        except Exception as e:
            logger.warning(f"Audio preprocessing failed for {file_path}: {e}")
            return file_path  # Return original file if preprocessing fails

    def _process_whisper_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process Whisper transcription result

        Args:
            result: Raw Whisper result

        Returns:
            Processed transcription data
        """
        try:
            # Extract basic transcription
            transcription = result.get('text', '').strip()

            # Calculate confidence score
            segments = result.get('segments', [])
            if segments:
                # Average confidence from segments
                confidences = []
                for segment in segments:
                    if 'avg_logprob' in segment:
                        # Convert log probability to confidence (0-1)
                        conf = max(0, min(1, np.exp(segment['avg_logprob'])))
                        confidences.append(conf)

                avg_confidence = np.mean(confidences) if confidences else 0.5
            else:
                avg_confidence = 0.5

            # Speaker diarization (if enabled)
            speaker_segments = []
            if self.enable_diarization and segments:
                speaker_segments = self._perform_speaker_diarization(segments)

            # Build detailed result
            details = {
                'segments': segments,
                'speaker_segments': speaker_segments,
                'language': result.get('language', 'en'),
                'duration': sum(seg.get('end', 0) - seg.get('start', 0) for seg in segments),
                'word_count': len(transcription.split()),
                'processing_time': result.get('processing_time', 0)
            }

            return {
                'success': True,
                'transcription': transcription,
                'confidence': float(avg_confidence),
                'details': details,
                'error': None
            }

        except Exception as e:
            logger.error(f"Error processing Whisper result: {e}")
            return {
                'success': False,
                'error': str(e),
                'transcription': '',
                'confidence': 0.0,
                'details': {}
            }

    def _perform_speaker_diarization(self, segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Perform simple speaker diarization based on audio characteristics

        Args:
            segments: Whisper segments with timestamps

        Returns:
            List of speaker-labeled segments
        """
        speaker_segments = []
        current_speaker = "speaker_1"

        try:
            for i, segment in enumerate(segments):
                # Simple heuristic: change speaker on long pauses or audio characteristics
                if i > 0:
                    prev_end = segments[i-1].get('end', 0)
                    current_start = segment.get('start', 0)
                    pause_duration = current_start - prev_end

                    # Switch speaker on pauses longer than 2 seconds
                    if pause_duration > 2.0:
                        current_speaker = "speaker_2" if current_speaker == "speaker_1" else "speaker_1"

                # Map speakers to roles (heuristic)
                if current_speaker == "speaker_1":
                    role = "customer"  # Assume first speaker is customer
                else:
                    role = "advisor"

                speaker_segments.append({
                    'start': segment.get('start', 0),
                    'end': segment.get('end', 0),
                    'text': segment.get('text', ''),
                    'speaker': current_speaker,
                    'role': role,
                    'confidence': segment.get('avg_logprob', 0)
                })

        except Exception as e:
            logger.warning(f"Speaker diarization failed: {e}")
            # Return segments without speaker labels
            return [{'text': seg.get('text', ''), 'role': 'unknown'} for seg in segments]

        return speaker_segments

    def get_transcription_statistics(self) -> Dict[str, Any]:
        """
        Get transcription statistics from database

        Returns:
            Dictionary with transcription statistics
        """
        if not self.db_manager:
            return {"error": "No database manager available"}

        try:
            with self.db_manager.get_session() as db_session:
                total_recordings = db_session.query(VoiceRecording).count()
                transcribed_recordings = db_session.query(VoiceRecording).filter(
                    VoiceRecording.transcription.isnot(None)
                ).count()

                # Calculate average confidence
                confidence_results = db_session.query(VoiceRecording.transcription_confidence).filter(
                    VoiceRecording.transcription_confidence.isnot(None)
                ).all()

                confidences = [float(c[0]) for c in confidence_results if c[0] is not None]
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                # Calculate total transcribed duration
                duration_results = db_session.query(VoiceRecording.duration_seconds).filter(
                    VoiceRecording.transcription.isnot(None),
                    VoiceRecording.duration_seconds.isnot(None)
                ).all()

                total_duration = sum(d[0] for d in duration_results if d[0])

                return {
                    'total_recordings': total_recordings,
                    'transcribed_recordings': transcribed_recordings,
                    'untranscribed_recordings': total_recordings - transcribed_recordings,
                    'transcription_rate': (transcribed_recordings / total_recordings * 100) if total_recordings > 0 else 0,
                    'average_confidence': round(avg_confidence, 3),
                    'total_transcribed_duration_seconds': total_duration,
                    'total_transcribed_duration_hours': round(total_duration / 3600, 2),
                    'confidence_distribution': {
                        'high_confidence': len([c for c in confidences if c >= 0.8]),
                        'medium_confidence': len([c for c in confidences if 0.6 <= c < 0.8]),
                        'low_confidence': len([c for c in confidences if c < 0.6])
                    }
                }

        except Exception as e:
            logger.error(f"Error getting transcription statistics: {e}")
            return {"error": str(e)}
