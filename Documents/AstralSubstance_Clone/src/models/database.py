"""
Database models and schema for AstralSubstance Clone

Defines SQLAlchemy models for storing extracted data from Keen.com and Google Voice.
"""

from datetime import datetime
from typing import Optional, Dict, Any
import json

from sqlalchemy import (
    create_engine, Column, Integer, String, DateTime, Text,
    Numeric, ForeignKey, Boolean, JSON
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
from pydantic import BaseModel, Field

Base = declarative_base()


class Customer(Base):
    """Customer information from Keen.com"""
    __tablename__ = 'customers'

    id = Column(Integer, primary_key=True)
    keen_customer_id = Column(String(50), unique=True, nullable=False, index=True)
    username = Column(String(100), nullable=True)
    nickname = Column(String(100), nullable=True)
    customer_since = Column(DateTime, nullable=True)
    total_call_count = Column(Integer, default=0)
    total_chat_count = Column(Integer, default=0)
    total_paid_mails = Column(Integer, default=0)
    total_earnings = Column(Numeric(10, 2), default=0.00)

    # Metadata
    extracted_at = Column(DateTime, default=func.now())
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    raw_data = Column(JSON, nullable=True)  # Store original API response

    # Relationships
    chat_sessions = relationship("ChatSession", back_populates="customer", cascade="all, delete-orphan")
    call_logs = relationship("CallLog", back_populates="customer", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Customer(id={self.keen_customer_id}, username={self.username})>"


class ChatSession(Base):
    """Chat session information"""
    __tablename__ = 'chat_sessions'

    id = Column(Integer, primary_key=True)
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=False)
    session_id = Column(String(100), nullable=True, index=True)
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)
    message_count = Column(Integer, default=0)
    session_type = Column(String(50), nullable=True)  # 'chat', 'email', etc.

    # Session metadata
    duration_minutes = Column(Integer, nullable=True)
    advisor_earnings = Column(Numeric(10, 2), nullable=True)
    session_rating = Column(Integer, nullable=True)

    # Timestamps
    extracted_at = Column(DateTime, default=func.now())
    raw_data = Column(JSON, nullable=True)

    # Relationships
    customer = relationship("Customer", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="session", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<ChatSession(id={self.session_id}, customer={self.customer_id})>"


class ChatMessage(Base):
    """Individual chat messages"""
    __tablename__ = 'chat_messages'

    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, ForeignKey('chat_sessions.id'), nullable=False)
    timestamp = Column(DateTime, nullable=False)
    speaker = Column(String(20), nullable=False)  # 'customer' or 'advisor'
    message_text = Column(Text, nullable=False)
    message_order = Column(Integer, nullable=False)

    # Message metadata
    message_type = Column(String(50), nullable=True)  # 'text', 'emoji', 'system'
    character_count = Column(Integer, nullable=True)

    # Timestamps
    extracted_at = Column(DateTime, default=func.now())

    # Relationships
    session = relationship("ChatSession", back_populates="messages")

    def __repr__(self):
        return f"<ChatMessage(session={self.session_id}, speaker={self.speaker}, order={self.message_order})>"


class CallLog(Base):
    """Call log information from Keen.com"""
    __tablename__ = 'call_logs'

    id = Column(Integer, primary_key=True)
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=False)
    call_timestamp = Column(DateTime, nullable=False, index=True)
    duration_seconds = Column(Integer, nullable=True)
    call_type = Column(String(50), nullable=True)  # 'incoming', 'outgoing'
    amount = Column(Numeric(10, 2), nullable=True)
    transaction_id = Column(String(100), nullable=True, index=True)

    # Call metadata
    call_quality = Column(String(20), nullable=True)
    call_rating = Column(Integer, nullable=True)
    advisor_notes = Column(Text, nullable=True)

    # Timestamps
    extracted_at = Column(DateTime, default=func.now())
    raw_data = Column(JSON, nullable=True)

    # Relationships
    customer = relationship("Customer", back_populates="call_logs")
    voice_recording = relationship("VoiceRecording", back_populates="call_log", uselist=False)

    def __repr__(self):
        return f"<CallLog(customer={self.customer_id}, timestamp={self.call_timestamp})>"


class VoiceRecording(Base):
    """Google Voice recording information"""
    __tablename__ = 'voice_recordings'

    id = Column(Integer, primary_key=True)
    file_path = Column(String(500), nullable=False)
    original_filename = Column(String(200), nullable=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    duration_seconds = Column(Integer, nullable=True)
    file_size_bytes = Column(Integer, nullable=True)

    # Audio metadata
    sample_rate = Column(Integer, nullable=True)
    audio_format = Column(String(20), nullable=True)
    channels = Column(Integer, nullable=True)

    # Phone number information (for Google Voice recordings)
    phone_number = Column(String(50), nullable=True)
    contact_name = Column(String(200), nullable=True)

    # Transcription
    transcription = Column(Text, nullable=True)
    transcription_confidence = Column(Numeric(3, 2), nullable=True)
    transcription_method = Column(String(50), nullable=True)  # 'google', 'whisper', etc.
    transcription_details = Column(JSON, nullable=True)  # Detailed transcription data (segments, speakers, etc.)

    # Correlation with call logs
    call_log_id = Column(Integer, ForeignKey('call_logs.id'), nullable=True, index=True)
    correlation_confidence = Column(Numeric(3, 2), nullable=True)
    correlation_method = Column(String(50), nullable=True)

    # Processing status
    processed = Column(Boolean, default=False)
    processing_error = Column(Text, nullable=True)

    # Timestamps
    extracted_at = Column(DateTime, default=func.now())
    processed_at = Column(DateTime, nullable=True)

    # Relationships
    call_log = relationship("CallLog", back_populates="voice_recording")

    def __repr__(self):
        return f"<VoiceRecording(file={self.original_filename}, timestamp={self.timestamp})>"


class DatasetEntry(Base):
    """Processed dataset entries ready for LLM training"""
    __tablename__ = 'dataset_entries'

    id = Column(Integer, primary_key=True)
    entry_type = Column(String(50), nullable=False)  # 'conversational', 'instruction_following', etc.
    conversation_id = Column(String(100), nullable=False, index=True)

    # Source references
    customer_id = Column(Integer, ForeignKey('customers.id'), nullable=True)
    chat_session_id = Column(Integer, ForeignKey('chat_sessions.id'), nullable=True)
    call_log_id = Column(Integer, ForeignKey('call_logs.id'), nullable=True)

    # Dataset content
    input_text = Column(Text, nullable=True)
    output_text = Column(Text, nullable=True)
    instruction = Column(Text, nullable=True)
    conversation_data = Column(JSON, nullable=True)  # Full conversation in JSON format

    # Metadata
    metadata = Column(JSON, nullable=True)
    character_count = Column(Integer, nullable=True)
    token_count_estimate = Column(Integer, nullable=True)

    # Quality metrics
    quality_score = Column(Numeric(3, 2), nullable=True)
    completeness_score = Column(Numeric(3, 2), nullable=True)

    # Dataset splits
    dataset_split = Column(String(20), nullable=True)  # 'train', 'validation', 'test'

    # Timestamps
    created_at = Column(DateTime, default=func.now())

    def __repr__(self):
        return f"<DatasetEntry(type={self.entry_type}, conversation={self.conversation_id})>"


class ExtractionLog(Base):
    """Log of extraction operations"""
    __tablename__ = 'extraction_logs'

    id = Column(Integer, primary_key=True)
    operation_type = Column(String(50), nullable=False)  # 'customer_list', 'customer_detail', etc.
    status = Column(String(20), nullable=False)  # 'success', 'error', 'partial'

    # Operation details
    records_processed = Column(Integer, default=0)
    records_successful = Column(Integer, default=0)
    records_failed = Column(Integer, default=0)

    # Error information
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)

    # Performance metrics
    duration_seconds = Column(Numeric(10, 3), nullable=True)
    memory_usage_mb = Column(Integer, nullable=True)

    # Timestamps
    started_at = Column(DateTime, nullable=False)
    completed_at = Column(DateTime, nullable=True)

    def __repr__(self):
        return f"<ExtractionLog(type={self.operation_type}, status={self.status})>"


# Pydantic models for API responses and data validation
class CustomerData(BaseModel):
    """Pydantic model for customer data validation"""
    keen_customer_id: str = Field(..., description="Keen.com customer ID")
    username: Optional[str] = Field(None, description="Customer username")
    nickname: Optional[str] = Field(None, description="Customer display name")
    customer_since: Optional[datetime] = Field(None, description="Customer registration date")
    total_call_count: int = Field(0, description="Total number of calls")
    total_chat_count: int = Field(0, description="Total number of chats")
    total_earnings: float = Field(0.0, description="Total earnings from customer")
    raw_data: Optional[Dict[str, Any]] = Field(None, description="Raw API response")


class ChatMessageData(BaseModel):
    """Pydantic model for chat message validation"""
    timestamp: datetime = Field(..., description="Message timestamp")
    speaker: str = Field(..., description="Message speaker (customer/advisor)")
    message_text: str = Field(..., description="Message content")
    message_order: int = Field(..., description="Message order in conversation")
    message_type: Optional[str] = Field("text", description="Message type")


class VoiceRecordingData(BaseModel):
    """Pydantic model for voice recording validation"""
    file_path: str = Field(..., description="Path to audio file")
    timestamp: datetime = Field(..., description="Recording timestamp")
    duration_seconds: Optional[int] = Field(None, description="Recording duration")
    file_size_bytes: Optional[int] = Field(None, description="File size")
    phone_number: Optional[str] = Field(None, description="Phone number associated with call")
    contact_name: Optional[str] = Field(None, description="Contact name from Google Voice")
    transcription: Optional[str] = Field(None, description="Audio transcription")


# Database utility functions
class DatabaseManager:
    """Database connection and session management"""

    def __init__(self, database_url: str):
        self.engine = create_engine(database_url, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

    def create_tables(self):
        """Create all database tables"""
        Base.metadata.create_all(bind=self.engine)

    def get_session(self):
        """Get database session"""
        return self.SessionLocal()

    def close(self):
        """Close database connection"""
        self.engine.dispose()
