"""
Data Correlation Module

Correlates Google Voice recordings with Keen.com call logs using timestamp
and duration matching algorithms.
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import math

from loguru import logger
from sqlalchemy.orm import Session

from ..models.database import CallLog, VoiceRecording


@dataclass
class CorrelationMatch:
    """Represents a correlation match between call log and voice recording"""
    call_log: CallLog
    voice_recording: VoiceRecording
    confidence_score: float
    timestamp_diff_seconds: float
    duration_diff_seconds: Optional[float]
    correlation_method: str


class DataCorrelator:
    """
    Correlates Google Voice recordings with Keen.com call logs
    
    Uses multiple matching strategies:
    1. Timestamp proximity matching
    2. Duration similarity matching
    3. Combined scoring algorithm
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.correlation_config = config.get('correlation', {})
        
        # Matching parameters
        self.timestamp_tolerance_minutes = self.correlation_config.get('timestamp_tolerance_minutes', 5)
        self.duration_tolerance_percent = self.correlation_config.get('duration_tolerance_percent', 10)
        self.confidence_threshold = self.correlation_config.get('confidence_threshold', 0.6)
        
        # Scoring weights
        weights = self.correlation_config.get('weights', {})
        self.timestamp_weight = weights.get('timestamp', 0.7)
        self.duration_weight = weights.get('duration', 0.3)
    
    def correlate_recordings_with_calls(
        self, 
        db_session: Session,
        customer_id: Optional[int] = None
    ) -> List[CorrelationMatch]:
        """
        Correlate all unmatched voice recordings with call logs
        
        Args:
            db_session: Database session
            customer_id: Optional customer ID to limit correlation scope
            
        Returns:
            List of correlation matches
        """
        logger.info("Starting voice recording correlation process")
        
        # Get unmatched voice recordings
        recordings_query = db_session.query(VoiceRecording).filter(
            VoiceRecording.call_log_id.is_(None)
        )
        
        # Get call logs (optionally filtered by customer)
        calls_query = db_session.query(CallLog)
        if customer_id:
            calls_query = calls_query.filter(CallLog.customer_id == customer_id)
            recordings_query = recordings_query.join(CallLog).filter(CallLog.customer_id == customer_id)
        
        voice_recordings = recordings_query.all()
        call_logs = calls_query.all()
        
        logger.info(f"Found {len(voice_recordings)} unmatched recordings and {len(call_logs)} call logs")
        
        if not voice_recordings or not call_logs:
            logger.warning("No recordings or call logs to correlate")
            return []
        
        # Perform correlation
        matches = self._find_best_matches(voice_recordings, call_logs)
        
        # Filter by confidence threshold
        high_confidence_matches = [
            match for match in matches 
            if match.confidence_score >= self.confidence_threshold
        ]
        
        logger.info(f"Found {len(high_confidence_matches)} high-confidence matches out of {len(matches)} total matches")
        
        return high_confidence_matches
    
    def _find_best_matches(
        self, 
        recordings: List[VoiceRecording], 
        call_logs: List[CallLog]
    ) -> List[CorrelationMatch]:
        """
        Find best matches between recordings and call logs
        
        Args:
            recordings: List of voice recordings
            call_logs: List of call logs
            
        Returns:
            List of correlation matches
        """
        matches = []
        used_call_logs = set()
        
        # Sort recordings by timestamp for efficient processing
        sorted_recordings = sorted(recordings, key=lambda r: r.timestamp)
        
        for recording in sorted_recordings:
            best_match = self._find_best_match_for_recording(recording, call_logs, used_call_logs)
            
            if best_match:
                matches.append(best_match)
                used_call_logs.add(best_match.call_log.id)
                
                logger.debug(
                    f"Matched recording {recording.original_filename} with call {best_match.call_log.id} "
                    f"(confidence: {best_match.confidence_score:.3f})"
                )
        
        return matches
    
    def _find_best_match_for_recording(
        self, 
        recording: VoiceRecording, 
        call_logs: List[CallLog],
        used_call_logs: set
    ) -> Optional[CorrelationMatch]:
        """
        Find the best matching call log for a given recording
        
        Args:
            recording: Voice recording to match
            call_logs: Available call logs
            used_call_logs: Set of already used call log IDs
            
        Returns:
            Best correlation match or None
        """
        best_match = None
        best_score = 0.0
        
        # Create time window for efficient filtering
        time_window_start = recording.timestamp - timedelta(minutes=self.timestamp_tolerance_minutes)
        time_window_end = recording.timestamp + timedelta(minutes=self.timestamp_tolerance_minutes)
        
        for call_log in call_logs:
            # Skip already used call logs
            if call_log.id in used_call_logs:
                continue
            
            # Quick timestamp filter
            if not (time_window_start <= call_log.call_timestamp <= time_window_end):
                continue
            
            # Calculate correlation score
            score, timestamp_diff, duration_diff = self._calculate_correlation_score(recording, call_log)
            
            if score > best_score:
                best_score = score
                best_match = CorrelationMatch(
                    call_log=call_log,
                    voice_recording=recording,
                    confidence_score=score,
                    timestamp_diff_seconds=timestamp_diff,
                    duration_diff_seconds=duration_diff,
                    correlation_method="timestamp_duration_combined"
                )
        
        return best_match
    
    def _calculate_correlation_score(
        self, 
        recording: VoiceRecording, 
        call_log: CallLog
    ) -> Tuple[float, float, Optional[float]]:
        """
        Calculate correlation score between recording and call log
        
        Args:
            recording: Voice recording
            call_log: Call log
            
        Returns:
            Tuple of (score, timestamp_diff_seconds, duration_diff_seconds)
        """
        # Calculate timestamp proximity score
        timestamp_diff = abs((recording.timestamp - call_log.call_timestamp).total_seconds())
        max_timestamp_diff = self.timestamp_tolerance_minutes * 60
        
        if timestamp_diff > max_timestamp_diff:
            return 0.0, timestamp_diff, None
        
        timestamp_score = 1.0 - (timestamp_diff / max_timestamp_diff)
        
        # Calculate duration similarity score
        duration_score = 0.5  # Default neutral score
        duration_diff = None
        
        if recording.duration_seconds and call_log.duration_seconds:
            duration_diff = abs(recording.duration_seconds - call_log.duration_seconds)
            max_duration = max(recording.duration_seconds, call_log.duration_seconds)
            
            # Calculate percentage difference
            duration_diff_percent = (duration_diff / max_duration) * 100
            
            if duration_diff_percent <= self.duration_tolerance_percent:
                duration_score = 1.0 - (duration_diff_percent / self.duration_tolerance_percent)
            else:
                duration_score = 0.0
        
        # Combined score
        combined_score = (timestamp_score * self.timestamp_weight) + (duration_score * self.duration_weight)
        
        return combined_score, timestamp_diff, duration_diff
    
    def apply_correlations(
        self, 
        db_session: Session, 
        matches: List[CorrelationMatch]
    ) -> int:
        """
        Apply correlation matches to the database
        
        Args:
            db_session: Database session
            matches: List of correlation matches to apply
            
        Returns:
            Number of correlations applied
        """
        applied_count = 0
        
        for match in matches:
            try:
                # Update voice recording with call log reference
                match.voice_recording.call_log_id = match.call_log.id
                match.voice_recording.correlation_confidence = match.confidence_score
                match.voice_recording.correlation_method = match.correlation_method
                
                db_session.add(match.voice_recording)
                applied_count += 1
                
                logger.debug(
                    f"Applied correlation: recording {match.voice_recording.id} -> call {match.call_log.id}"
                )
                
            except Exception as e:
                logger.error(f"Failed to apply correlation for recording {match.voice_recording.id}: {e}")
                continue
        
        try:
            db_session.commit()
            logger.info(f"Successfully applied {applied_count} correlations")
        except Exception as e:
            logger.error(f"Failed to commit correlations: {e}")
            db_session.rollback()
            applied_count = 0
        
        return applied_count
    
    def get_correlation_statistics(self, db_session: Session) -> Dict[str, Any]:
        """
        Get correlation statistics
        
        Args:
            db_session: Database session
            
        Returns:
            Dictionary with correlation statistics
        """
        total_recordings = db_session.query(VoiceRecording).count()
        correlated_recordings = db_session.query(VoiceRecording).filter(
            VoiceRecording.call_log_id.isnot(None)
        ).count()
        
        uncorrelated_recordings = total_recordings - correlated_recordings
        correlation_rate = (correlated_recordings / total_recordings * 100) if total_recordings > 0 else 0
        
        # Get confidence distribution
        confidence_stats = db_session.query(VoiceRecording.correlation_confidence).filter(
            VoiceRecording.correlation_confidence.isnot(None)
        ).all()
        
        confidences = [c[0] for c in confidence_stats if c[0] is not None]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        return {
            "total_recordings": total_recordings,
            "correlated_recordings": correlated_recordings,
            "uncorrelated_recordings": uncorrelated_recordings,
            "correlation_rate_percent": round(correlation_rate, 2),
            "average_confidence": round(float(avg_confidence), 3),
            "confidence_distribution": {
                "high_confidence": len([c for c in confidences if c >= 0.8]),
                "medium_confidence": len([c for c in confidences if 0.6 <= c < 0.8]),
                "low_confidence": len([c for c in confidences if c < 0.6])
            }
        }
    
    def manual_correlation(
        self, 
        db_session: Session, 
        recording_id: int, 
        call_log_id: int,
        confidence: float = 1.0
    ) -> bool:
        """
        Manually correlate a recording with a call log
        
        Args:
            db_session: Database session
            recording_id: Voice recording ID
            call_log_id: Call log ID
            confidence: Manual confidence score
            
        Returns:
            True if successful, False otherwise
        """
        try:
            recording = db_session.query(VoiceRecording).get(recording_id)
            call_log = db_session.query(CallLog).get(call_log_id)
            
            if not recording or not call_log:
                logger.error(f"Recording {recording_id} or call log {call_log_id} not found")
                return False
            
            recording.call_log_id = call_log_id
            recording.correlation_confidence = confidence
            recording.correlation_method = "manual"
            
            db_session.add(recording)
            db_session.commit()
            
            logger.info(f"Manually correlated recording {recording_id} with call {call_log_id}")
            return True
            
        except Exception as e:
            logger.error(f"Manual correlation failed: {e}")
            db_session.rollback()
            return False
