"""
LLM Dataset Formatter

Converts extracted and correlated data into various LLM training formats:
- Conversational format (ChatML, OpenAI format)
- Instruction-following format
- Prompt-completion pairs
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Generator
from pathlib import Path
import random

from loguru import logger
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

try:
    from ..models.database import Customer, ChatSession, ChatMessage, CallLog, VoiceRecording, DatasetEntry
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from models.database import Customer, ChatSession, ChatMessage, CallLog, VoiceRecording, DatasetEntry


class ConversationEntry(BaseModel):
    """Pydantic model for conversational dataset entries"""
    conversation_id: str = Field(..., description="Unique conversation identifier")
    customer_id: str = Field(..., description="Customer identifier")
    session_type: str = Field(..., description="Type of session (chat, call)")
    timestamp: datetime = Field(..., description="Conversation timestamp")
    messages: List[Dict[str, Any]] = Field(..., description="List of messages")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class InstructionEntry(BaseModel):
    """Pydantic model for instruction-following dataset entries"""
    instruction: str = Field(..., description="System instruction")
    input: str = Field(..., description="User input")
    output: str = Field(..., description="Expected output")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class LLMDatasetFormatter:
    """
    Formats extracted data into LLM training datasets

    Supports multiple output formats and handles data quality filtering
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.dataset_config = config.get('dataset', {})

        # Output settings
        self.max_conversation_length = self.dataset_config.get('output', {}).get('max_conversation_length', 10000)
        self.include_metadata = self.dataset_config.get('output', {}).get('include_metadata', True)
        self.preserve_timestamps = self.dataset_config.get('output', {}).get('preserve_timestamps', True)

        # Data splits
        splits = self.dataset_config.get('splits', {})
        self.train_split = splits.get('train', 0.8)
        self.validation_split = splits.get('validation', 0.1)
        self.test_split = splits.get('test', 0.1)

        # Output directory
        self.output_dir = Path(config['storage']['datasets_dir'])
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def build_conversational_dataset(
        self,
        db_session: Session,
        output_filename: str = "conversational_dataset.jsonl"
    ) -> Dict[str, int]:
        """
        Build conversational format dataset from chat sessions

        Args:
            db_session: Database session
            output_filename: Output file name

        Returns:
            Dictionary with statistics about the generated dataset
        """
        logger.info("Building conversational dataset")

        # Get all chat sessions with messages
        chat_sessions = db_session.query(ChatSession).join(ChatMessage).distinct().all()

        logger.info(f"Found {len(chat_sessions)} chat sessions with messages")

        conversations = []
        skipped_count = 0

        for session in chat_sessions:
            try:
                conversation = self._format_chat_session_as_conversation(session)
                if conversation:
                    conversations.append(conversation)
                else:
                    skipped_count += 1
            except Exception as e:
                logger.error(f"Error formatting session {session.id}: {e}")
                skipped_count += 1

        # Split dataset
        train_data, val_data, test_data = self._split_dataset(conversations)

        # Write datasets
        stats = {
            "total_conversations": len(conversations),
            "skipped_conversations": skipped_count,
            "train_size": len(train_data),
            "validation_size": len(val_data),
            "test_size": len(test_data)
        }

        self._write_jsonl_dataset(train_data, f"train_{output_filename}")
        self._write_jsonl_dataset(val_data, f"validation_{output_filename}")
        self._write_jsonl_dataset(test_data, f"test_{output_filename}")

        logger.info(f"Conversational dataset built: {stats}")
        return stats

    def build_instruction_dataset(
        self,
        db_session: Session,
        output_filename: str = "instruction_dataset.jsonl"
    ) -> Dict[str, int]:
        """
        Build instruction-following format dataset

        Args:
            db_session: Database session
            output_filename: Output file name

        Returns:
            Dictionary with statistics about the generated dataset
        """
        logger.info("Building instruction-following dataset")

        # Get chat sessions for instruction format
        chat_sessions = db_session.query(ChatSession).join(ChatMessage).distinct().all()

        instructions = []
        skipped_count = 0

        for session in chat_sessions:
            try:
                instruction_entries = self._format_chat_session_as_instructions(session)
                instructions.extend(instruction_entries)
            except Exception as e:
                logger.error(f"Error formatting session {session.id} as instructions: {e}")
                skipped_count += 1

        # Split dataset
        train_data, val_data, test_data = self._split_dataset(instructions)

        # Write datasets
        stats = {
            "total_instructions": len(instructions),
            "skipped_sessions": skipped_count,
            "train_size": len(train_data),
            "validation_size": len(val_data),
            "test_size": len(test_data)
        }

        self._write_jsonl_dataset(train_data, f"train_{output_filename}")
        self._write_jsonl_dataset(val_data, f"validation_{output_filename}")
        self._write_jsonl_dataset(test_data, f"test_{output_filename}")

        logger.info(f"Instruction dataset built: {stats}")
        return stats

    def _format_chat_session_as_conversation(self, session: ChatSession) -> Optional[ConversationEntry]:
        """
        Format a chat session as a conversational entry

        Args:
            session: Chat session to format

        Returns:
            ConversationEntry or None if session is invalid
        """
        if not session.messages:
            return None

        # Sort messages by order
        sorted_messages = sorted(session.messages, key=lambda m: m.message_order)

        # Format messages
        formatted_messages = []
        total_length = 0

        for message in sorted_messages:
            # Map speaker to role
            role = "user" if message.speaker == "customer" else "assistant"

            message_dict = {
                "role": role,
                "content": message.message_text
            }

            if self.preserve_timestamps:
                message_dict["timestamp"] = message.timestamp.isoformat()

            formatted_messages.append(message_dict)
            total_length += len(message.message_text)

            # Check length limit
            if total_length > self.max_conversation_length:
                logger.debug(f"Truncating conversation {session.id} due to length limit")
                break

        # Build metadata
        metadata = {}
        if self.include_metadata:
            metadata.update({
                "session_id": session.session_id,
                "duration_minutes": session.duration_minutes,
                "message_count": len(formatted_messages),
                "customer_since": session.customer.customer_since.isoformat() if session.customer.customer_since else None,
                "total_customer_chats": session.customer.total_chat_count,
                "advisor_earnings": float(session.advisor_earnings) if session.advisor_earnings else None
            })

            # Add call recording info if available
            if hasattr(session, 'call_log') and session.call_log and session.call_log.voice_recording:
                recording = session.call_log.voice_recording
                metadata["call_recording_available"] = True
                metadata["recording_path"] = recording.file_path
                metadata["recording_duration"] = recording.duration_seconds
                metadata["recording_transcribed"] = recording.transcription is not None

                # Add transcription info if available
                if recording.transcription:
                    metadata["transcription_confidence"] = float(recording.transcription_confidence) if recording.transcription_confidence else None
                    metadata["transcription_method"] = recording.transcription_method
                    metadata["transcription_length"] = len(recording.transcription)

                    # Add speaker diarization info if available
                    if recording.transcription_details:
                        try:
                            details = json.loads(recording.transcription_details)
                            if 'speaker_segments' in details:
                                metadata["speaker_segments_available"] = True
                                metadata["speaker_segments_count"] = len(details['speaker_segments'])
                        except (json.JSONDecodeError, TypeError):
                            pass
            else:
                metadata["call_recording_available"] = False

        return ConversationEntry(
            conversation_id=f"keen_chat_{session.id}",
            customer_id=session.customer.keen_customer_id,
            session_type="chat",
            timestamp=session.start_time or session.extracted_at,
            messages=formatted_messages,
            metadata=metadata
        )

    def _format_chat_session_as_instructions(self, session: ChatSession) -> List[InstructionEntry]:
        """
        Format a chat session as instruction-following entries

        Args:
            session: Chat session to format

        Returns:
            List of instruction entries
        """
        if not session.messages or len(session.messages) < 2:
            return []

        # Sort messages by order
        sorted_messages = sorted(session.messages, key=lambda m: m.message_order)

        instructions = []

        # Create instruction-response pairs
        for i in range(len(sorted_messages) - 1):
            current_msg = sorted_messages[i]
            next_msg = sorted_messages[i + 1]

            # Only create pairs where customer speaks first, advisor responds
            if current_msg.speaker == "customer" and next_msg.speaker == "advisor":

                # Build context from previous messages
                context_messages = sorted_messages[:i]
                context = self._build_conversation_context(context_messages)

                # Create instruction
                instruction = self._generate_instruction_prompt(session, context)

                # Build metadata
                metadata = {
                    "conversation_id": f"keen_chat_{session.id}",
                    "session_type": "chat",
                    "timestamp": current_msg.timestamp.isoformat(),
                    "message_pair_index": i
                }

                if self.include_metadata:
                    metadata.update({
                        "customer_id": session.customer.keen_customer_id,
                        "customer_since": session.customer.customer_since.isoformat() if session.customer.customer_since else None,
                        "session_duration": session.duration_minutes
                    })

                instruction_entry = InstructionEntry(
                    instruction=instruction,
                    input=current_msg.message_text,
                    output=next_msg.message_text,
                    metadata=metadata
                )

                instructions.append(instruction_entry)

        return instructions

    def _build_conversation_context(self, messages: List[ChatMessage]) -> str:
        """
        Build conversation context from previous messages

        Args:
            messages: List of previous messages

        Returns:
            Formatted context string
        """
        if not messages:
            return ""

        context_parts = []
        for msg in messages[-5:]:  # Last 5 messages for context
            speaker = "Customer" if msg.speaker == "customer" else "Advisor"
            context_parts.append(f"{speaker}: {msg.message_text}")

        return "\n".join(context_parts)

    def _generate_instruction_prompt(self, session: ChatSession, context: str) -> str:
        """
        Generate instruction prompt for the session

        Args:
            session: Chat session
            context: Conversation context

        Returns:
            Instruction prompt string
        """
        base_instruction = (
            "You are a psychic advisor helping customers with their personal concerns. "
            "Provide empathetic, insightful, and helpful guidance based on your intuitive abilities. "
            "Be supportive and understanding while offering practical advice."
        )

        if context:
            return f"{base_instruction}\n\nConversation context:\n{context}\n\nRespond to the customer's message:"
        else:
            return f"{base_instruction}\n\nRespond to the customer's message:"

    def _split_dataset(self, data: List[Any]) -> tuple:
        """
        Split dataset into train/validation/test sets

        Args:
            data: List of data entries

        Returns:
            Tuple of (train_data, val_data, test_data)
        """
        # Shuffle data
        shuffled_data = data.copy()
        random.shuffle(shuffled_data)

        total_size = len(shuffled_data)
        train_size = int(total_size * self.train_split)
        val_size = int(total_size * self.validation_split)

        train_data = shuffled_data[:train_size]
        val_data = shuffled_data[train_size:train_size + val_size]
        test_data = shuffled_data[train_size + val_size:]

        return train_data, val_data, test_data

    def _write_jsonl_dataset(self, data: List[Any], filename: str):
        """
        Write dataset to JSONL file

        Args:
            data: List of data entries
            filename: Output filename
        """
        output_path = self.output_dir / filename

        with open(output_path, 'w', encoding='utf-8') as f:
            for entry in data:
                if hasattr(entry, 'model_dump'):
                    json_line = json.dumps(entry.model_dump(), ensure_ascii=False, default=str)
                else:
                    json_line = json.dumps(entry, ensure_ascii=False, default=str)
                f.write(json_line + '\n')

        logger.info(f"Wrote {len(data)} entries to {output_path}")

    def generate_dataset_summary(self, db_session: Session) -> Dict[str, Any]:
        """
        Generate summary statistics about the available data

        Args:
            db_session: Database session

        Returns:
            Dictionary with dataset statistics
        """
        stats = {}

        # Customer statistics
        total_customers = db_session.query(Customer).count()
        customers_with_chats = db_session.query(Customer).join(ChatSession).distinct().count()
        customers_with_calls = db_session.query(Customer).join(CallLog).distinct().count()

        stats['customers'] = {
            'total': total_customers,
            'with_chats': customers_with_chats,
            'with_calls': customers_with_calls
        }

        # Chat statistics
        total_chat_sessions = db_session.query(ChatSession).count()
        total_chat_messages = db_session.query(ChatMessage).count()

        stats['chats'] = {
            'total_sessions': total_chat_sessions,
            'total_messages': total_chat_messages,
            'avg_messages_per_session': total_chat_messages / total_chat_sessions if total_chat_sessions > 0 else 0
        }

        # Call statistics
        total_calls = db_session.query(CallLog).count()
        calls_with_recordings = db_session.query(CallLog).join(VoiceRecording).count()

        stats['calls'] = {
            'total_calls': total_calls,
            'with_recordings': calls_with_recordings,
            'recording_rate': (calls_with_recordings / total_calls * 100) if total_calls > 0 else 0
        }

        # Voice recording statistics
        total_recordings = db_session.query(VoiceRecording).count()
        correlated_recordings = db_session.query(VoiceRecording).filter(
            VoiceRecording.call_log_id.isnot(None)
        ).count()

        stats['recordings'] = {
            'total': total_recordings,
            'correlated': correlated_recordings,
            'correlation_rate': (correlated_recordings / total_recordings * 100) if total_recordings > 0 else 0
        }

        return stats
