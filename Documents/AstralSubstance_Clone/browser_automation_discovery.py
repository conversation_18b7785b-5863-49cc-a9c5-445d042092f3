#!/usr/bin/env python3
"""
Browser automation to discover the full customer API endpoints
SAFETY: READ-ONLY operations only, no data modification
"""

import sys
from pathlib import Path
import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import requests

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class NetworkMonitor:
    """Monitor network requests during browser automation"""

    def __init__(self):
        self.requests = []
        self.api_endpoints = set()
        self.customer_endpoints = set()

    def add_request(self, request_data):
        """Add a network request to monitoring"""
        self.requests.append(request_data)

        # Check if it's an API endpoint
        url = request_data.get('url', '')
        if '/api/' in url or '/ajax/' in url or url.endswith('.json'):
            self.api_endpoints.add(url)

            # Check if it's customer-related
            if 'customer' in url.lower():
                self.customer_endpoints.add(url)

    def get_summary(self):
        """Get summary of discovered endpoints"""
        return {
            'total_requests': len(self.requests),
            'api_endpoints': list(self.api_endpoints),
            'customer_endpoints': list(self.customer_endpoints),
            'all_requests': self.requests
        }

def discover_customer_api():
    """Use browser automation to discover customer API endpoints"""

    print("🔍 BROWSER AUTOMATION - CUSTOMER API DISCOVERY")
    print("=" * 60)
    print("⚠️  SAFETY MODE: READ-ONLY OPERATIONS ONLY")
    print("⚠️  NO DATA WILL BE MODIFIED OR DELETED")
    print("=" * 60)

    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']

    # Setup Chrome options for automation
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')

    # Enable network logging
    chrome_options.add_argument('--enable-logging')
    chrome_options.add_argument('--log-level=0')
    chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})

    # Initialize network monitor
    network_monitor = NetworkMonitor()
    driver = None

    try:
        # Start browser
        print("\n🌐 Starting Chrome browser...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)

        # Set authentication cookies
        print("🔐 Setting authentication cookies...")
        setup_authentication(driver, keen_config)

        # Monitor customer management pages
        print("\n📊 Monitoring customer management pages...")
        monitor_customer_pages(driver, keen_config, network_monitor)

        # Analyze discovered endpoints
        print("\n🔍 Analyzing discovered endpoints...")
        analyze_discovered_endpoints(network_monitor, keen_config)

        # Save discovery results
        print("\n💾 Saving discovery results...")
        save_discovery_results(network_monitor)

    except Exception as e:
        print(f"❌ Error during browser automation: {e}")
    finally:
        if driver:
            try:
                driver.quit()
                print("🔒 Browser closed safely")
            except:
                pass

def setup_authentication(driver, keen_config):
    """Setup authentication cookies in the browser"""

    base_url = keen_config['base_url']

    # Navigate to the main page first
    driver.get(base_url)
    time.sleep(2)

    # Add authentication cookies
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            try:
                driver.add_cookie({
                    'name': name,
                    'value': value,
                    'domain': '.keen.com'
                })
                print(f"  ✅ Added cookie: {name}")
            except Exception as e:
                print(f"  ⚠️  Failed to add cookie {name}: {e}")

    print("🔐 Authentication cookies set")

def monitor_customer_pages(driver, keen_config, network_monitor):
    """Monitor customer management pages for API calls"""

    base_url = keen_config['base_url']

    # Pages to monitor
    customer_pages = [
        '/app/myaccount/customers',
        '/app/myaccount/customer-list',
        '/app/myaccount/conversations',
        '/app/myaccount/feedback',
    ]

    for page_path in customer_pages:
        print(f"\n📄 Monitoring page: {page_path}")

        try:
            # Clear previous logs
            driver.get('data:,')  # Blank page
            time.sleep(1)

            # Navigate to the page
            full_url = f"{base_url}{page_path}"
            print(f"  🌐 Loading: {full_url}")

            driver.get(full_url)

            # Wait for page to load
            wait = WebDriverWait(driver, 10)
            try:
                wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                print("  ✅ Page loaded")
            except TimeoutException:
                print("  ⚠️  Page load timeout")
                continue

            # Wait for potential AJAX calls
            print("  ⏳ Waiting for AJAX calls...")
            time.sleep(5)

            # Look for customer-related elements
            find_customer_elements(driver, page_path)

            # Capture network logs
            capture_network_logs(driver, network_monitor, page_path)

            # Try to trigger customer data loading
            trigger_customer_loading(driver, page_path)

        except Exception as e:
            print(f"  ❌ Error monitoring {page_path}: {e}")

def find_customer_elements(driver, page_path):
    """Find customer-related elements on the page"""

    print("  🔍 Looking for customer-related elements...")

    # Look for common customer UI elements
    customer_selectors = [
        '[data-test*="customer"]',
        '[class*="customer"]',
        '[id*="customer"]',
        'table tbody tr',  # Customer tables
        '.list-item',      # Customer list items
        '.customer-row',
        '.customer-item',
        '.user-row',
        '.user-item',
    ]

    found_elements = 0

    for selector in customer_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"    ✅ Found {len(elements)} elements with selector: {selector}")
                found_elements += len(elements)

                # Sample first few elements
                for i, element in enumerate(elements[:3]):
                    try:
                        text = element.text.strip()[:100]
                        if text:
                            print(f"      Sample {i+1}: {text}")
                    except:
                        pass

        except Exception as e:
            pass  # Ignore selector errors

    if found_elements == 0:
        print("    ℹ️  No obvious customer elements found")
    else:
        print(f"    📊 Total customer-related elements: {found_elements}")

def capture_network_logs(driver, network_monitor, page_path):
    """Capture network logs from the browser"""

    print("  📡 Capturing network logs...")

    try:
        # Get performance logs
        logs = driver.get_log('performance')

        api_calls = 0
        customer_calls = 0

        for log in logs:
            try:
                message = json.loads(log['message'])

                if message['message']['method'] == 'Network.responseReceived':
                    response = message['message']['params']['response']
                    url = response['url']
                    status = response['status']

                    # Filter for API calls
                    if any(pattern in url for pattern in ['/api/', '/ajax/', '.json', '/graphql']):
                        api_calls += 1

                        request_data = {
                            'url': url,
                            'status': status,
                            'method': response.get('method', 'GET'),
                            'page': page_path,
                            'timestamp': log['timestamp']
                        }

                        network_monitor.add_request(request_data)

                        if 'customer' in url.lower():
                            customer_calls += 1
                            print(f"    🎯 Customer API call: {url}")
                        elif api_calls <= 5:  # Show first 5 API calls
                            print(f"    🔗 API call: {url}")

            except Exception as e:
                pass  # Ignore log parsing errors

        print(f"    📊 Found {api_calls} API calls, {customer_calls} customer-related")

    except Exception as e:
        print(f"    ❌ Error capturing logs: {e}")

def trigger_customer_loading(driver, page_path):
    """Try to trigger customer data loading through UI interactions"""

    print("  🖱️  Attempting to trigger customer data loading...")

    # SAFETY: Only perform READ-ONLY interactions
    safe_interactions = [
        # Look for pagination buttons
        ('button[aria-label*="next"]', 'Next page button'),
        ('button[aria-label*="page"]', 'Page button'),
        ('.pagination button', 'Pagination button'),

        # Look for filter/search elements
        ('input[type="search"]', 'Search input'),
        ('input[placeholder*="search"]', 'Search input'),
        ('input[placeholder*="filter"]', 'Filter input'),

        # Look for dropdown menus
        ('select', 'Dropdown select'),
        ('.dropdown-toggle', 'Dropdown toggle'),

        # Look for tabs
        ('.tab', 'Tab'),
        ('[role="tab"]', 'Tab element'),
    ]

    for selector, description in safe_interactions:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"    ✅ Found {description}: {len(elements)} elements")

                # For search inputs, just focus (don't type)
                if 'input' in selector:
                    try:
                        elements[0].click()
                        time.sleep(1)
                        print(f"      👆 Focused on {description}")
                    except:
                        pass

                # For buttons/tabs, just hover (don't click)
                elif 'button' in selector or 'tab' in selector:
                    try:
                        driver.execute_script("arguments[0].scrollIntoView();", elements[0])
                        time.sleep(1)
                        print(f"      👁️  Scrolled to {description}")
                    except:
                        pass

        except Exception as e:
            pass  # Ignore interaction errors

    # Wait for any triggered requests
    time.sleep(3)

def analyze_discovered_endpoints(network_monitor, keen_config):
    """Analyze discovered endpoints for customer data"""

    summary = network_monitor.get_summary()

    print(f"📊 DISCOVERY ANALYSIS")
    print(f"  Total requests monitored: {summary['total_requests']}")
    print(f"  API endpoints found: {len(summary['api_endpoints'])}")
    print(f"  Customer endpoints found: {len(summary['customer_endpoints'])}")

    if summary['customer_endpoints']:
        print(f"\n🎯 CUSTOMER ENDPOINTS DISCOVERED:")
        for endpoint in summary['customer_endpoints']:
            print(f"  - {endpoint}")

        # Test discovered customer endpoints
        print(f"\n🧪 Testing discovered customer endpoints...")
        test_discovered_customer_endpoints(summary['customer_endpoints'], keen_config)

    if summary['api_endpoints']:
        print(f"\n🔗 ALL API ENDPOINTS:")
        for endpoint in summary['api_endpoints'][:10]:  # Show first 10
            print(f"  - {endpoint}")

def test_discovered_customer_endpoints(endpoints, keen_config):
    """Test discovered customer endpoints"""

    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)

    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }

    for endpoint in endpoints:
        print(f"  🧪 Testing: {endpoint}")

        try:
            # Try GET request
            response = session.get(endpoint, headers=headers, timeout=15)

            if response.status_code == 200:
                print(f"    ✅ GET successful ({len(response.content)} bytes)")

                if response.content:
                    try:
                        data = response.json()
                        customer_count = count_customers_in_response(data)
                        if customer_count > 0:
                            print(f"    🎉 FOUND {customer_count} CUSTOMERS!")

                            if customer_count > 1000:
                                print(f"    🚀 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                                save_customer_endpoint_data(endpoint, data)

                    except json.JSONDecodeError:
                        print(f"    📄 Non-JSON response")
                else:
                    print(f"    ℹ️  Empty response")
            else:
                print(f"    ❌ Status: {response.status_code}")

        except Exception as e:
            print(f"    ❌ Error: {e}")

def count_customers_in_response(data):
    """Count customers in API response"""

    def count_items(obj, depth=0):
        if depth > 5:
            return 0

        count = 0
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'customer' in key.lower() and isinstance(value, list):
                    count += len(value)
                elif isinstance(value, (dict, list)):
                    count += count_items(value, depth + 1)
        elif isinstance(obj, list):
            if obj and isinstance(obj[0], dict):
                first_item = obj[0]
                customer_fields = ['id', 'name', 'email', 'username', 'customerId', 'userName']
                if any(field in first_item for field in customer_fields):
                    count += len(obj)

            for item in obj:
                count += count_items(item, depth + 1)

        return count

    return count_items(data)

def save_discovery_results(network_monitor):
    """Save discovery results to files"""

    summary = network_monitor.get_summary()

    # Save complete discovery results
    with open('data/browser_discovery_results.json', 'w') as f:
        json.dump(summary, f, indent=2)
    print("💾 Discovery results saved to: data/browser_discovery_results.json")

    # Save just the customer endpoints for easy access
    if summary['customer_endpoints']:
        with open('data/discovered_customer_endpoints.json', 'w') as f:
            json.dump(list(summary['customer_endpoints']), f, indent=2)
        print("💾 Customer endpoints saved to: data/discovered_customer_endpoints.json")

def save_customer_endpoint_data(endpoint, data):
    """Save customer endpoint data"""

    filename = f"data/customer_endpoint_{endpoint.replace('/', '_').replace(':', '_')}.json"

    endpoint_data = {
        'endpoint': endpoint,
        'customer_count': count_customers_in_response(data),
        'discovery_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'data': data
    }

    with open(filename, 'w') as f:
        json.dump(endpoint_data, f, indent=2)

    print(f"    💾 Customer data saved to: {filename}")

if __name__ == "__main__":
    # Check if selenium is installed
    try:
        import selenium
        discover_customer_api()
    except ImportError:
        print("❌ Selenium not installed. Installing...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "selenium"])
        print("✅ Selenium installed. Please run the script again.")
