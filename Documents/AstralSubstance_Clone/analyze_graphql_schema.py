#!/usr/bin/env python3
"""
Analyze GraphQL schema to find the correct query fields for customer data
"""

import sys
from pathlib import Path
import requests
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def analyze_graphql_schema():
    """Analyze the GraphQL schema to find correct query fields"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
    }
    
    base_url = keen_config['base_url']
    graphql_url = f"{base_url}/api/graphql"
    
    print("🔍 ANALYZING GRAPHQL SCHEMA")
    print("=" * 60)
    
    # Get schema with focus on Query type
    schema_data = get_query_fields(session, headers, graphql_url)
    
    if schema_data:
        analyze_query_fields(schema_data)
        test_discovered_queries(session, headers, graphql_url, schema_data)

def get_query_fields(session, headers, graphql_url):
    """Get the Query type fields from GraphQL schema"""
    
    query = {
        "query": """
        query {
            __schema {
                queryType {
                    name
                    fields {
                        name
                        description
                        type {
                            name
                            kind
                            ofType {
                                name
                                kind
                            }
                        }
                        args {
                            name
                            type {
                                name
                                kind
                                ofType {
                                    name
                                    kind
                                }
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'data' in data and '__schema' in data['data']:
                query_type = data['data']['__schema']['queryType']
                print(f"✅ Retrieved Query type: {query_type['name']}")
                print(f"📊 Available fields: {len(query_type['fields'])}")
                
                # Save schema for analysis
                with open('data/query_fields.json', 'w') as f:
                    json.dump(query_type, f, indent=2)
                print(f"💾 Query fields saved to: data/query_fields.json")
                
                return query_type
            else:
                print(f"❌ No schema data in response")
                return None
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def analyze_query_fields(query_type):
    """Analyze query fields to find customer-related ones"""
    
    print(f"\n📋 ANALYZING QUERY FIELDS")
    print("=" * 40)
    
    fields = query_type['fields']
    
    # Find customer-related fields
    customer_fields = []
    user_fields = []
    advisor_fields = []
    session_fields = []
    call_fields = []
    
    for field in fields:
        field_name = field['name'].lower()
        
        if 'customer' in field_name:
            customer_fields.append(field)
        elif 'user' in field_name:
            user_fields.append(field)
        elif 'advisor' in field_name:
            advisor_fields.append(field)
        elif 'session' in field_name:
            session_fields.append(field)
        elif 'call' in field_name:
            call_fields.append(field)
    
    print(f"👥 Customer fields ({len(customer_fields)}):")
    for field in customer_fields:
        print(f"  - {field['name']}: {get_type_name(field['type'])}")
        if field.get('args'):
            args = [f"{arg['name']}: {get_type_name(arg['type'])}" for arg in field['args']]
            print(f"    Args: {', '.join(args)}")
    
    print(f"\n👤 User fields ({len(user_fields)}):")
    for field in user_fields:
        print(f"  - {field['name']}: {get_type_name(field['type'])}")
    
    print(f"\n🎯 Advisor fields ({len(advisor_fields)}):")
    for field in advisor_fields:
        print(f"  - {field['name']}: {get_type_name(field['type'])}")
    
    print(f"\n💬 Session fields ({len(session_fields)}):")
    for field in session_fields:
        print(f"  - {field['name']}: {get_type_name(field['type'])}")
    
    print(f"\n📞 Call fields ({len(call_fields)}):")
    for field in call_fields:
        print(f"  - {field['name']}: {get_type_name(field['type'])}")
    
    # Look for any field that might return customer data
    print(f"\n🔍 ALL AVAILABLE FIELDS:")
    for field in fields[:20]:  # Show first 20 fields
        print(f"  - {field['name']}: {get_type_name(field['type'])}")
    
    if len(fields) > 20:
        print(f"  ... and {len(fields) - 20} more fields")
    
    return {
        'customer_fields': customer_fields,
        'user_fields': user_fields,
        'advisor_fields': advisor_fields,
        'session_fields': session_fields,
        'call_fields': call_fields,
        'all_fields': fields
    }

def get_type_name(type_info):
    """Get a readable type name from GraphQL type info"""
    if type_info['kind'] == 'NON_NULL':
        return f"{get_type_name(type_info['ofType'])}!"
    elif type_info['kind'] == 'LIST':
        return f"[{get_type_name(type_info['ofType'])}]"
    else:
        return type_info['name'] or 'Unknown'

def test_discovered_queries(session, headers, graphql_url, query_type):
    """Test the discovered query fields"""
    
    print(f"\n🧪 TESTING DISCOVERED QUERIES")
    print("=" * 40)
    
    fields = query_type['fields']
    
    # Test fields that might contain customer data
    test_fields = []
    
    for field in fields:
        field_name = field['name'].lower()
        if any(keyword in field_name for keyword in ['customer', 'user', 'advisor', 'session', 'call']):
            test_fields.append(field)
    
    # Also test some common fields
    common_fields = ['me', 'viewer', 'currentUser', 'profile']
    for field in fields:
        if field['name'] in common_fields:
            test_fields.append(field)
    
    print(f"Testing {len(test_fields)} potentially relevant fields...")
    
    for field in test_fields[:10]:  # Test first 10 fields
        test_single_field(session, headers, graphql_url, field)

def test_single_field(session, headers, graphql_url, field):
    """Test a single GraphQL field"""
    
    field_name = field['name']
    print(f"\n  Testing field: {field_name}")
    
    # Build a simple query for this field
    try:
        # Simple query without arguments first
        simple_query = {
            "query": f"""
            query {{
                {field_name} {{
                    __typename
                }}
            }}
            """,
            "variables": {}
        }
        
        response = session.post(graphql_url, headers=headers, json=simple_query, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                error_msg = data['errors'][0]['message']
                if 'does not exist' in error_msg:
                    print(f"    ❌ Field doesn't exist")
                elif 'required' in error_msg.lower() or 'argument' in error_msg.lower():
                    print(f"    ⚠️  Requires arguments: {error_msg}")
                else:
                    print(f"    ❌ Error: {error_msg}")
            elif 'data' in data:
                result = data['data'].get(field_name)
                if result is not None:
                    print(f"    ✅ Success! Type: {type(result)}")
                    if isinstance(result, dict) and '__typename' in result:
                        print(f"      GraphQL type: {result['__typename']}")
                else:
                    print(f"    ✅ Field exists but returned null")
        else:
            print(f"    ❌ HTTP error: {response.status_code}")
            
    except Exception as e:
        print(f"    ❌ Error: {e}")

if __name__ == "__main__":
    analyze_graphql_schema()
