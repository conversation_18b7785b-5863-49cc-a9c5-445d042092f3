#!/usr/bin/env python3
"""
Test authentication with Keen.com using the exact query from the curl request
"""

import sys
from pathlib import Path
import requests
import json
import brotli

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import <PERSON>fig<PERSON>oa<PERSON>

def test_keen_auth():
    """Test Keen.com authentication with the exact query from curl"""

    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']

    # Setup session with cookies
    session = requests.Session()

    # Add all cookies from config
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)

    # Setup headers (from the curl request)
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Uid': '',
        'X-Domain-ID': '1',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://www.keen.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.keen.com/app/myaccount/feedback',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Priority': 'u=0',
        'TE': 'trailers'
    }

    # The exact query from the curl request
    query_data = {
        "query": """query($advisorId:Int $listingId:Int $isHighlighted:Boolean $sortBy:[Sort] $pageSize:Int $pageNumber:Int){
            ratingsAndReviews(advisorId:$advisorId listingId:$listingId isHighlighted:$isHighlighted sortBy:$sortBy pageSize:$pageSize pageNumber:$pageNumber){
                averageRating
                totalEdges
                edges{
                    node{
                        id
                        rating
                        review
                        date
                        isHighlighted
                        advisorStrengths
                        target{
                            type
                            source{
                                id
                                sessionExists
                                masterTransactionId
                                customer{
                                    id
                                    userName
                                    nickname
                                }
                                listing{
                                    id
                                    category{
                                        id
                                        name
                                    }
                                    advisor{
                                        id
                                        userName
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }""",
        "variables": {
            "listingId": "12471990",
            "advisorId": 56392386,
            "isHighlighted": False,
            "pageNumber": 1,
            "pageSize": "100",
            "sortBy": [{"field": "date", "order": "DESCENDING"}]
        }
    }

    # Make the request
    url = f"{keen_config['base_url']}{keen_config['graphql_endpoint']}"
    print(f"Testing authentication with: {url}")
    print(f"Cookies loaded: {len(session.cookies)}")

    try:
        response = session.post(url, headers=headers, json=query_data, timeout=30)
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")

        if response.status_code == 200:
            print("✅ Got 200 response!")
            print(f"Response content length: {len(response.content)}")
            print(f"Content encoding: {response.headers.get('Content-Encoding', 'none')}")

            # Try to parse JSON directly (requests should handle decompression)
            try:
                data = response.json()
                print("✅ JSON parsing successful!")
                print(f"Response data keys: {list(data.keys())}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                # Try manual text parsing as fallback
                try:
                    response_text = response.text
                    print(f"Response text preview: {response_text[:200]}...")
                    data = json.loads(response_text)
                    print("✅ Manual JSON parsing successful!")
                except Exception as e2:
                    print(f"❌ Manual parsing also failed: {e2}")
                    print(f"Raw response content: {response.content[:500]}...")
                    return False

            if 'data' in data and 'ratingsAndReviews' in data['data']:
                reviews = data['data']['ratingsAndReviews']
                print(f"Found {reviews.get('totalEdges', 0)} reviews")
                print(f"Average rating: {reviews.get('averageRating', 'N/A')}")

                # Show first review if available
                edges = reviews.get('edges', [])
                if edges:
                    first_review = edges[0]['node']
                    print(f"First review: {first_review.get('review', 'No review text')[:100]}...")

                    # Check if customer data is available
                    target = first_review.get('target', {})
                    source = target.get('source', {})
                    customer = source.get('customer', {})
                    if customer:
                        print(f"Customer found: {customer.get('userName', 'Unknown')} ({customer.get('id', 'No ID')})")

                return True
            else:
                print("❌ Unexpected response structure")
                print(f"Response: {json.dumps(data, indent=2)[:500]}...")
                return False
        else:
            print(f"❌ Authentication failed with status {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            return False

    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_keen_auth()
    sys.exit(0 if success else 1)
