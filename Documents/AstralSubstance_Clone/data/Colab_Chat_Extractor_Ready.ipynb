{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Keen.com Chat Transcript Extractor\\n", "\\n", "This notebook extracts chat transcripts using your authentication cookies and Google Colab's IP address.\\n", "\\n", "**Instructions:**\\n", "1. Run the cell below\\n", "2. Wait for extraction to complete\\n", "3. Download the results JSON file\\n", "\\n", "**Your cookies are already embedded in the code below.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [["# GOOGLE COLAB CHAT TRANSCRIPT EXTRACTOR", "# Copy and paste this entire script into a Google Colab notebook cell and run it", "", "# Setup (run this first)", "!pip install selenium webdriver-manager requests beautifulsoup4 > /dev/null 2>&1", "!apt-get update > /dev/null 2>&1", "!apt-get install -y chromium-browser chromium-chromedriver > /dev/null 2>&1", "!apt-get install -y xvfb > /dev/null 2>&1", "!pip install pyvirtualdisplay > /dev/null 2>&1", "", "import os", "os.environ['PATH'] += ':/usr/lib/chromium-browser/'", "", "# Main extraction code", "import json", "import time", "import requests", "from datetime import datetime", "from selenium import webdriver", "from selenium.webdriver.common.by import By", "from selenium.webdriver.chrome.options import Options", "from selenium.webdriver.support.ui import WebDriverWait", "from selenium.webdriver.support import expected_conditions as EC", "from pyvirtualdisplay import Display", "import re", "", "print(\"🚀 GOOGLE COLAB CHAT TRANSCRIPT EXTRACTOR\")", "print(\"=\" * 60)", "", "# Get Colab IP address", "try:", "    response = requests.get('https://httpbin.org/ip', timeout=10)", "    colab_ip = response.json().get('origin', 'Unknown')", "    print(f\"🌐 Colab IP Address: {colab_ip}\")", "except:", "    print(\"🌐 Colab IP Address: Unknown\")", "", "# Your actual authentication cookies", "KEEN_COOKIES = {", "    \"KeenUid\": \"Uid=eTRPMya5tB7-kgTqrCmjFQ%3d%3d&firstVisitTime=2025-03-21%2010:52:23%20AM&ipAddress=**************,**************&ANNON=N\",", "    \"KeenTKT\": \"MPS=N&Tkt=-103-%7ctMQ%3d%3d%7caLTE%3d%7cb%7cpMA%3d%3d%7cqMA%3d%3d%7coMQ%3d%3d%7cnQXN0cmFsIFN1YnN0YW5jZQ%3d%3d%7cuZVRSUE15YTV0Qjcta2dUcXJDbWpGUT09%7cdMTc0ODA3OTk3OS43MTk1%7clMTc0ODA3OTk3OS43MTk1%7cc%7ckMQ%3d%3d%7cs6D2A426C6383BB19D485541ECCC46A57&Rbm=y\", ", "    \"KeenUser\": \"UserUid=eTRPMya5tB7-kgTqrCmjFQ==&RccLastSync=2025-05-24+09%3a46&IsAdvisor=True&BecameFbm=False&BecameRCC=False&BecameFbmLastSync=5%2f24%2f2025+9%3a46%3a19+AM\",", "    \"SessionId\": \"7f0d309c-be38-f011-bf3f-98f2b31428e6\"", "}", "", "# Interactions to extract", "INTERACTIONS = [", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"User50690268\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"priority\": \"high\",", "        \"source\": \"user_example\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2023-11-06\",", "        \"earnings\": \"$1,153.72\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"Tianah428\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"priority\": \"high\",", "        \"source\": \"extraction_log\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2025-05-08\",", "        \"earnings\": \"$12.66\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"Tianah428\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"priority\": \"high\",", "        \"source\": \"extraction_log\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2025-05-08\",", "        \"earnings\": \"$12.66\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"isaluckystar2112\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2025-05-17\",", "        \"earnings\": \"$27.45\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"isaluckystar2112\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2025-05-17\",", "        \"earnings\": \"$27.45\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"isaluckystar2112\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2025-05-17\",", "        \"earnings\": \"$27.45\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"Benevolent Watcher\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2023-12-19\",", "        \"earnings\": \"$83.54\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"Benevolent Watcher\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2023-12-19\",", "        \"earnings\": \"$83.54\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"Benevolent Watcher\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2023-12-19\",", "        \"earnings\": \"$83.54\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"User99838055\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2025-02-28\",", "        \"earnings\": \"$42.23\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"User99838055\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2025-02-28\",", "        \"earnings\": \"$42.23\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"User99838055\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2025-02-28\",", "        \"earnings\": \"$42.23\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"User50690268\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2023-11-06\",", "        \"earnings\": \"$1,153.72\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"User50690268\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2023-11-06\",", "        \"earnings\": \"$1,153.72\"", "    },", "    {", "        \"customer_id\": \"********\",", "        \"customer_username\": \"User50690268\",", "        \"interaction_id\": \"********\",", "        \"interaction_type\": \"chat\",", "        \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",", "        \"priority\": \"normal\",", "        \"source\": \"generated_pattern\",", "        \"customer_nickname\": null,", "        \"customer_since\": \"2023-11-06\",", "        \"earnings\": \"$1,153.72\"", "    }", "]", "", "print(f\"🔐 Using your authentication cookies\")", "print(f\"💬 Extracting {len(INTERACTIONS)} chat transcripts\")", "", "# Setup browser", "print(\"\\n🔧 Setting up Chrome browser...\")", "display = Display(visible=0, size=(1920, 1080))", "display.start()", "", "chrome_options = Options()", "chrome_options.add_argument(\"--headless\")", "chrome_options.add_argument(\"--no-sandbox\")", "chrome_options.add_argument(\"--disable-dev-shm-usage\")", "chrome_options.add_argument(\"--disable-gpu\")", "chrome_options.add_argument(\"--window-size=1920,1080\")", "chrome_options.add_argument(\"--disable-blink-features=AutomationControlled\")", "chrome_options.add_experimental_option(\"excludeSwitches\", [\"enable-automation\"])", "chrome_options.add_experimental_option('useAutomationExtension', False)", "", "# Random user agent", "user_agents = [", "    \"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\",", "    \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\"", "]", "chrome_options.add_argument(f\"--user-agent={user_agents[0]}\")", "", "driver = webdriver.Chrome(options=chrome_options)", "driver.execute_script(\"Object.defineProperty(navigator, 'webdriver', {get: () => undefined})\")", "", "print(\"✅ Browser setup complete\")", "", "# Authenticate with Keen.com", "print(\"\\n🔐 Authenticating with Keen.com...\")", "try:", "    driver.get(\"https://www.keen.com\")", "    time.sleep(3)", "    ", "    # Set authentication cookies", "    for name, value in KEEN_COOKIES.items():", "        if value and len(value) > 10:  # Valid cookie value", "            driver.add_cookie({", "                'name': name,", "                'value': value,", "                'domain': '.keen.com'", "            })", "    ", "    # Refresh to apply cookies", "    driver.refresh()", "    time.sleep(2)", "    ", "    # Test authentication", "    driver.get(\"https://www.keen.com/app/#/myaccount/customers\")", "    time.sleep(3)", "    ", "    if \"login\" not in driver.current_url.lower():", "        print(\"✅ Authentication successful\")", "        auth_success = True", "    else:", "        print(\"❌ Authentication failed - redirected to login\")", "        auth_success = False", "        ", "except Exception as e:", "    print(f\"❌ Authentication error: {e}\")", "    auth_success = False", "", "# Extract chat transcripts", "chat_transcripts = {}", "errors = []", "", "if auth_success:", "    print(f\"\\n💬 Extracting chat transcripts...\")", "    ", "    for i, interaction in enumerate(INTERACTIONS, 1):", "        customer_username = interaction['customer_username']", "        interaction_id = interaction['interaction_id']", "        chat_url = interaction['chat_url']", "        ", "        print(f\"\\n👤 {i}/{len(INTERACTIONS)}: {customer_username} (Chat {interaction_id})\")", "        ", "        try:", "            # Navigate to chat transcript page", "            driver.get(chat_url)", "            time.sleep(4)", "            ", "            # Wait for page to load", "            WebDriverWait(driver, 15).until(", "                EC.presence_of_element_located((By.TAG_NAME, \"body\"))", "            )", "            ", "            # Extract messages using multiple strategies", "            messages = []", "            ", "            # Strategy 1: Look for message elements", "            message_selectors = [", "                '.message', '.chat-message', '.conversation-message',", "                '[class*=\"message\"]', '[class*=\"chat\"]', '[class*=\"transcript\"]',", "                '.dialogue', '.conversation-line'", "            ]", "            ", "            for selector in message_selectors:", "                try:", "                    elements = driver.find_elements(By.CSS_SELECTOR, selector)", "                    ", "                    for j, element in enumerate(elements):", "                        text = element.text.strip()", "                        if text and len(text) > 3:", "                            # Infer sender type", "                            text_lower = text.lower()", "                            if any(pattern in text_lower for pattern in ['i see', 'i sense', 'the cards', 'spirit', 'energy']):", "                                sender_type = 'advisor'", "                            elif any(pattern in text_lower for pattern in ['will i', 'should i', 'when will', 'what about']):", "                                sender_type = 'customer'", "                            else:", "                                sender_type = 'unknown'", "                            ", "                            message = {", "                                'sequence': j + 1,", "                                'content': text,", "                                'sender_type': sender_type,", "                                'element_class': element.get_attribute('class') or '',", "                                'extraction_method': selector", "                            }", "                            messages.append(message)", "                    ", "                    if messages:  # Found messages with this selector", "                        break", "                        ", "                except:", "                    continue", "            ", "            # Strategy 2: If no structured messages, try page text", "            if not messages:", "                try:", "                    page_text = driver.find_element(By.TAG_NAME, 'body').text", "                    lines = [line.strip() for line in page_text.split('\\n') if line.strip()]", "                    ", "                    for j, line in enumerate(lines):", "                        if (len(line) > 10 and ", "                            not any(skip in line.lower() for skip in ['navigation', 'menu', 'header', 'footer', 'copyright'])):", "                            ", "                            # Infer sender type", "                            text_lower = line.lower()", "                            if any(pattern in text_lower for pattern in ['i see', 'i sense', 'the cards']):", "                                sender_type = 'advisor'", "                            elif any(pattern in text_lower for pattern in ['will i', 'should i', 'when will']):", "                                sender_type = 'customer'", "                            else:", "                                sender_type = 'unknown'", "                            ", "                            message = {", "                                'sequence': j + 1,", "                                'content': line,", "                                'sender_type': sender_type,", "                                'extraction_method': 'page_text'", "                            }", "                            messages.append(message)", "                            ", "                            if len(messages) >= 20:  # Limit to 20 messages", "                                break", "                except:", "                    pass", "            ", "            # Save transcript if messages found", "            if messages:", "                customer_id = interaction['customer_id']", "                ", "                if customer_id not in chat_transcripts:", "                    chat_transcripts[customer_id] = {", "                        'customer_info': {", "                            'customer_id': customer_id,", "                            'username': customer_username", "                        },", "                        'transcripts': []", "                    }", "                ", "                transcript = {", "                    'interaction_id': interaction_id,", "                    'customer_id': customer_id,", "                    'customer_username': customer_username,", "                    'chat_url': chat_url,", "                    'messages': messages,", "                    'message_count': len(messages),", "                    'extraction_timestamp': datetime.now().isoformat(),", "                    'extraction_method': 'google_colab_selenium',", "                    'colab_ip': colab_ip", "                }", "                ", "                chat_transcripts[customer_id]['transcripts'].append(transcript)", "                print(f\"    ✅ Extracted {len(messages)} messages\")", "            else:", "                print(f\"    ℹ️  No messages found\")", "                ", "        except Exception as e:", "            error_msg = f\"Error processing {interaction_id}: {e}\"", "            print(f\"    ❌ {error_msg}\")", "            errors.append(error_msg)", "        ", "        # Rate limiting", "        time.sleep(3)", "", "# Save results", "print(f\"\\n💾 Saving results...\")", "", "dataset = {", "    'metadata': {", "        'extraction_timestamp': datetime.now().isoformat(),", "        'extraction_method': 'google_colab_with_auth_cookies',", "        'colab_ip': colab_ip,", "        'customers_with_transcripts': len(chat_transcripts),", "        'total_transcripts': sum(len(c['transcripts']) for c in chat_transcripts.values()),", "        'total_messages': sum(", "            sum(len(t.get('messages', [])) for t in c['transcripts']) ", "            for c in chat_transcripts.values()", "        ),", "        'errors': len(errors)", "    },", "    'chat_transcripts': chat_transcripts,", "    'errors': errors", "}", "", "# Save to file", "with open('colab_chat_extraction_results.json', 'w') as f:", "    json.dump(dataset, f, indent=2)", "", "print(f\"✅ Results saved to colab_chat_extraction_results.json\")", "print(f\"📊 Customers with transcripts: {len(chat_transcripts)}\")", "print(f\"💬 Total transcripts: {dataset['metadata']['total_transcripts']}\")", "print(f\"📝 Total messages: {dataset['metadata']['total_messages']}\")", "print(f\"❌ Errors: {len(errors)}\")", "", "# Display sample results", "if chat_transcripts:", "    print(f\"\\n📋 SAMPLE EXTRACTED CONVERSATIONS:\")", "    for i, (customer_id, data) in enumerate(list(chat_transcripts.items())[:3], 1):", "        transcripts = data['transcripts']", "        username = data['customer_info']['username']", "        print(f\"  {i}. Customer: {username} ({customer_id})\")", "        print(f\"     Transcripts: {len(transcripts)}\")", "        ", "        if transcripts and transcripts[0].get('messages'):", "            sample_messages = transcripts[0]['messages'][:3]", "            for j, msg in enumerate(sample_messages, 1):", "                sender = msg.get('sender_type', 'unknown').title()", "                content = msg.get('content', '')[:50] + \"...\" if len(msg.get('content', '')) > 50 else msg.get('content', '')", "                print(f\"       {j}. {sender}: {content}\")", "", "# Download file", "try:", "    from google.colab import files", "    files.download('colab_chat_extraction_results.json')", "    print(\"\\n📥 File automatically downloaded!\")", "except:", "    print(\"\\nℹ️  File saved in Colab environment\")", "", "# Cleanup", "driver.quit()", "", "print(f\"\\n🎉 GOOGLE COLAB EXTRACTION COMPLETE!\")", "print(f\"✅ Successfully used your authentication cookies with <PERSON>b's IP\")", ""]]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 4}