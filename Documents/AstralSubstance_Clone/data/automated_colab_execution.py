
# Google Colab Chat Transcript Extractor
# Automated execution with authentication

# Setup cell
!pip install selenium webdriver-manager requests beautifulsoup4 > /dev/null 2>&1
!apt-get update > /dev/null 2>&1
!apt-get install -y chromium-browser chromium-chromedriver > /dev/null 2>&1
!apt-get install -y xvfb > /dev/null 2>&1
!pip install pyvirtualdisplay > /dev/null 2>&1

import os
os.environ['PATH'] += ':/usr/lib/chromium-browser/'

# Main extraction code
import json
import time
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from pyvirtualdisplay import Display
import re

class ColabChatExtractor:
    def __init__(self):
        self.driver = None
        self.chat_transcripts = {}
        self.errors = []
        
        # Your actual Keen.com authentication cookies
        self.keen_cookies = {
            "KeenUid": "Uid=eTRPMya5tB7-kgTqrCmjFQ%3d%3d&firstVisitTime=2025-03-21%2010:52:23%20AM&ipAddress=**************,**************&ANNON=N",
            "KeenTKT": "MPS=N&Tkt=-103-%7ctMQ%3d%3d%7caLTE%3d%7cb%7cpMA%3d%3d%7cqMA%3d%3d%7coMQ%3d%3d%7cnQXN0cmFsIFN1YnN0YW5jZQ%3d%3d%7cuZVRSUE15YTV0Qjcta2dUcXJDbWpGUT09%7cdMTc0ODA3OTk3OS43MTk1%7clMTc0ODA3OTk3OS43MTk1%7cc%7ckMQ%3d%3d%7cs6D2A426C6383BB19D485541ECCC46A57&Rbm=y", 
            "KeenUser": "UserUid=eTRPMya5tB7-kgTqrCmjFQ==&RccLastSync=2025-05-24+09%3a46&IsAdvisor=True&BecameFbm=False&BecameRCC=False&BecameFbmLastSync=5%2f24%2f2025+9%3a46%3a19+AM",
            "SessionId": "7f0d309c-be38-f011-bf3f-98f2b31428e6"
        }
        
        # High-priority interaction data
        self.pending_interactions = [
            {
                "customer_id": "********",
                "customer_username": "User50690268", 
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "high"
            },
            {
                "customer_id": "********",
                "customer_username": "Tianah428", 
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "high"
            },
            {
                "customer_id": "********",
                "customer_username": "Tianah428", 
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "high"
            },
            {
                "customer_id": "********",
                "customer_username": "isaluckystar2112", 
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "normal"
            },
            {
                "customer_id": "********",
                "customer_username": "isaluckystar2112", 
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "normal"
            },
            {
                "customer_id": "********",
                "customer_username": "isaluckystar2112", 
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "normal"
            },
            {
                "customer_id": "********",
                "customer_username": "Benevolent Watcher", 
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "normal"
            },
            {
                "customer_id": "********",
                "customer_username": "Benevolent Watcher", 
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "normal"
            },
            {
                "customer_id": "********",
                "customer_username": "Benevolent Watcher", 
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "normal"
            },
            {
                "customer_id": "********",
                "customer_username": "User99838055", 
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "normal"
            },
        ]
        
    def setup_colab_browser(self):
        print("🔧 Setting up browser in Colab...")
        
        display = Display(visible=0, size=(1920, 1080))
        display.start()
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Browser setup complete")
        return True
    
    def authenticate_keen(self):
        print("🔐 Authenticating with Keen.com...")
        
        try:
            self.driver.get("https://www.keen.com")
            time.sleep(3)
            
            # Set authentication cookies
            for name, value in self.keen_cookies.items():
                if value and len(value) > 10:  # Valid cookie value
                    self.driver.add_cookie({
                        'name': name,
                        'value': value,
                        'domain': '.keen.com'
                    })
            
            # Refresh to apply cookies
            self.driver.refresh()
            time.sleep(2)
            
            # Test authentication by navigating to customer page
            self.driver.get("https://www.keen.com/app/#/myaccount/customers")
            time.sleep(3)
            
            # Check if we're authenticated (not redirected to login)
            if "login" not in self.driver.current_url.lower():
                print("✅ Keen.com authentication successful")
                return True
            else:
                print("❌ Keen.com authentication failed - redirected to login")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def extract_chat_transcript(self, interaction):
        try:
            chat_url = interaction['chat_url']
            interaction_id = interaction['interaction_id']
            customer_username = interaction['customer_username']
            
            print(f"  💬 Extracting chat {interaction_id} for {customer_username}...")
            
            # Navigate to chat transcript page
            self.driver.get(chat_url)
            time.sleep(4)
            
            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Extract messages
            messages = self.extract_messages_from_page()
            
            if messages:
                transcript = {
                    'interaction_id': interaction_id,
                    'customer_id': interaction['customer_id'],
                    'customer_username': customer_username,
                    'chat_url': chat_url,
                    'messages': messages,
                    'message_count': len(messages),
                    'extraction_timestamp': datetime.now().isoformat(),
                    'extraction_method': 'google_colab_automated',
                    'colab_ip': self.get_colab_ip()
                }
                
                print(f"    ✅ Extracted {len(messages)} messages")
                return transcript
            else:
                print(f"    ℹ️  No messages found")
                return None
                
        except Exception as e:
            print(f"    ❌ Error extracting chat {interaction.get('interaction_id', 'unknown')}: {e}")
            return None
    
    def extract_messages_from_page(self):
        messages = []
        
        try:
            # Multiple strategies to find messages
            message_selectors = [
                '.message', '.chat-message', '.conversation-message',
                '[class*="message"]', '[class*="chat"]', '[class*="transcript"]',
                'p', 'div[class*="text"]', '.dialogue', '.conversation'
            ]
            
            for selector in message_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for i, element in enumerate(elements):
                        text = element.text.strip()
                        if text and len(text) > 3:
                            message = {
                                'sequence': i + 1,
                                'content': text,
                                'sender_type': self.infer_sender_type(text),
                                'element_class': element.get_attribute('class') or '',
                                'extraction_method': selector
                            }
                            
                            # Try to get timestamp
                            try:
                                timestamp_elem = element.find_element(By.CSS_SELECTOR, '.timestamp, .time, [data-time]')
                                if timestamp_elem:
                                    message['timestamp'] = timestamp_elem.text or timestamp_elem.get_attribute('data-time')
                            except:
                                pass
                            
                            messages.append(message)
                    
                    if messages:  # Found messages with this selector
                        break
                        
                except:
                    continue
            
            # If no structured messages, try page text extraction
            if not messages:
                page_text = self.driver.find_element(By.TAG_NAME, 'body').text
                lines = [line.strip() for line in page_text.split('\n') if line.strip()]
                
                for i, line in enumerate(lines):
                    if len(line) > 10 and not any(skip in line.lower() for skip in ['navigation', 'menu', 'header', 'footer']):
                        message = {
                            'sequence': i + 1,
                            'content': line,
                            'sender_type': self.infer_sender_type(line),
                            'extraction_method': 'page_text'
                        }
                        messages.append(message)
            
            # Process messages for ordering
            if messages:
                messages = self.process_message_ordering(messages)
            
        except Exception as e:
            print(f"      ❌ Error extracting messages: {e}")
        
        return messages
    
    def infer_sender_type(self, text):
        text_lower = text.lower()
        
        # Advisor patterns (psychic/advisor language)
        advisor_patterns = [
            'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',
            'let me', 'i can help', 'what i\'m getting', 'i\'m seeing',
            'the universe', 'your guides', 'i\'m picking up', 'i\'m feeling',
            'what i\'m sensing', 'the energy around', 'your aura'
        ]
        
        # Customer patterns (questions and concerns)
        customer_patterns = [
            'will i', 'should i', 'when will', 'what about', 'can you tell me',
            'i want to know', 'my question', 'help me understand', 'what do you see',
            'i\'m wondering', 'can you help', 'what should i do', 'i need to know'
        ]
        
        advisor_score = sum(1 for pattern in advisor_patterns if pattern in text_lower)
        customer_score = sum(1 for pattern in customer_patterns if pattern in text_lower)
        
        if advisor_score > customer_score:
            return 'advisor'
        elif customer_score > advisor_score:
            return 'customer'
        else:
            return 'unknown'
    
    def process_message_ordering(self, messages):
        # Sort by timestamp if available
        timestamped = [msg for msg in messages if msg.get('timestamp')]
        non_timestamped = [msg for msg in messages if not msg.get('timestamp')]
        
        if timestamped:
            try:
                timestamped.sort(key=lambda x: x.get('timestamp', ''))
                for i, msg in enumerate(timestamped):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = True
            except:
                for i, msg in enumerate(timestamped):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = False
        
        for i, msg in enumerate(non_timestamped):
            msg['sequence'] = len(timestamped) + i + 1
            msg['order_resolved'] = False
        
        return timestamped + non_timestamped
    
    def get_colab_ip(self):
        try:
            response = requests.get('https://httpbin.org/ip', timeout=10)
            if response.status_code == 200:
                return response.json().get('origin', 'Unknown')
        except:
            pass
        return 'Unknown'
    
    def run_extraction(self):
        print("🚀 AUTOMATED GOOGLE COLAB CHAT EXTRACTOR")
        print("=" * 60)
        
        # Get Colab IP
        colab_ip = self.get_colab_ip()
        print(f"🌐 Colab IP: {colab_ip}")
        
        # Setup browser
        if not self.setup_colab_browser():
            print("❌ Browser setup failed")
            return
        
        # Authenticate
        if not self.authenticate_keen():
            print("❌ Authentication failed")
            return
        
        print(f"\n💬 Extracting {len(self.pending_interactions)} chat transcripts...")
        
        for i, interaction in enumerate(self.pending_interactions, 1):
            customer_username = interaction['customer_username']
            interaction_id = interaction['interaction_id']
            
            print(f"\n👤 {i}/{len(self.pending_interactions)}: {customer_username} (Chat {interaction_id})")
            
            try:
                transcript = self.extract_chat_transcript(interaction)
                
                if transcript:
                    customer_id = interaction['customer_id']
                    
                    if customer_id not in self.chat_transcripts:
                        self.chat_transcripts[customer_id] = {
                            'customer_info': {
                                'customer_id': customer_id,
                                'username': customer_username
                            },
                            'transcripts': []
                        }
                    
                    self.chat_transcripts[customer_id]['transcripts'].append(transcript)
                
            except Exception as e:
                error_msg = f"Error processing {interaction_id}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)
            
            # Rate limiting
            time.sleep(3)
        
        # Save results
        self.save_results()
        
        # Cleanup
        if self.driver:
            self.driver.quit()
    
    def save_results(self):
        print(f"\n💾 Saving extraction results...")
        
        dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'google_colab_automated',
                'colab_ip': self.get_colab_ip(),
                'customers_with_transcripts': len(self.chat_transcripts),
                'total_transcripts': sum(len(c['transcripts']) for c in self.chat_transcripts.values()),
                'total_messages': sum(
                    sum(len(t.get('messages', [])) for t in c['transcripts']) 
                    for c in self.chat_transcripts.values()
                ),
                'errors': len(self.errors)
            },
            'chat_transcripts': self.chat_transcripts,
            'errors': self.errors
        }
        
        # Save to file
        with open('automated_colab_chat_transcripts.json', 'w') as f:
            json.dump(dataset, f, indent=2)
        
        print(f"✅ Results saved!")
        print(f"📊 Customers with transcripts: {len(self.chat_transcripts)}")
        print(f"💬 Total transcripts: {dataset['metadata']['total_transcripts']}")
        print(f"📝 Total messages: {dataset['metadata']['total_messages']}")
        print(f"❌ Errors: {len(self.errors)}")
        
        # Display sample results
        if self.chat_transcripts:
            print(f"\n📋 SAMPLE EXTRACTED CONVERSATIONS:")
            for i, (customer_id, data) in enumerate(list(self.chat_transcripts.items())[:3], 1):
                transcripts = data['transcripts']
                username = data['customer_info']['username']
                print(f"  {i}. Customer: {username} ({customer_id})")
                print(f"     Transcripts: {len(transcripts)}")
                
                if transcripts and transcripts[0].get('messages'):
                    sample_messages = transcripts[0]['messages'][:3]
                    for j, msg in enumerate(sample_messages, 1):
                        sender = msg.get('sender_type', 'unknown').title()
                        content = msg.get('content', '')[:50] + "..." if len(msg.get('content', '')) > 50 else msg.get('content', '')
                        print(f"       {j}. {sender}: {content}")
        
        # Try to download file if in Colab
        try:
            from google.colab import files
            files.download('automated_colab_chat_transcripts.json')
            print("\n📥 File automatically downloaded!")
        except:
            print("\nℹ️  File saved in Colab environment")
        
        return dataset

# Execute the extraction
print("🎯 Starting automated chat transcript extraction...")
extractor = ColabChatExtractor()
results = extractor.run_extraction()

print("\n🎉 AUTOMATED EXTRACTION COMPLETE!")
