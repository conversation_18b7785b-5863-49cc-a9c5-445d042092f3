<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Keen</title>
    <meta name="description" content="" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=0, viewport-fit=cover"
    />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="CACHE-CONTROL" content="NO-CACHE" />
    <meta http-equiv="Expires" content="-1" />
    <meta name="robots" content="noindex">

    <base href="/app/" />
<meta name="ingenio-web/config/environment" content="%7B%22modulePrefix%22%3A%22ingenio-web%22%2C%22environment%22%3A%22production%22%2C%22rootURL%22%3A%22/app/%22%2C%22baseURL%22%3A%22/app/%22%2C%22locationType%22%3A%22auto%22%2C%22EmberENV%22%3A%7B%22domainId%22%3A1%2C%22domainName%22%3A%22Keen%22%2C%22apiRoot%22%3A%22/api%22%2C%22cloverApiRoot%22%3A%22https%3A//clover-api.ingenio.com/api%22%2C%22logApiRoot%22%3A%22/api%22%2C%22urls%22%3A%7B%22callStatus%22%3A%22/DomainOverrides/Advice/Calls/AjaxWs/CallStatus.asmx/GetStatus%22%2C%22myAccountPage%22%3A%22../myaccount%22%2C%22ccap%22%3A%22https%3A//ccap-trans.ingenio.com/%22%2C%22riskScript%22%3A%22https%3A//risk.checkout.com/cdn/risk/1.2/risk.js%22%2C%22loginPage%22%3A%22../Registration/LoginStarter.aspx%22%2C%22errorPage%22%3A%22../ServerError.aspx%22%2C%22forgotPasswordPage%22%3A%22../Registration/ForgotPassword.aspx%22%2C%22idvAuthPage%22%3A%22../secure/credentials/IDAuth.asp%3FReturnUrl%3D%22%2C%22cvnRequiredPage%22%3A%22../secure/Payments/Pay/DepositConfirm.aspx%3FReturnUrl%3D%22%2C%22zendeskDomainNameUs%22%3A%22https%3A//help.keen.com/hc/en-us%22%2C%22nextJSRoot%22%3A%22%22%7D%2C%22paypal%22%3A%7B%22expressCheckoutUrl%22%3A%22https%3A//www.paypal.com/cgi-bin/webscr%3Fcmd%3D_express-checkout%22%7D%2C%22applePay%22%3A%7B%22merchantId%22%3A%22merchant.com.ingenio.prod%22%7D%2C%22shouldAuthenticate%22%3Atrue%2C%22FEATURES%22%3A%7B%7D%2C%22minimumListingPrice%22%3A1.99%2C%22minimumPsychicPartySessionPrice%22%3A250%2C%22minimumHourlySessionPrice%22%3A100%2C%22minimumDepositAmount%22%3A1%2C%22repeatUserMinimumDepositAmount%22%3A10%2C%22maximumDepositAmount%22%3A1000%2C%22minimumBalanceMinutesForConversation%22%3A1%2C%22maximumNumberOfPaymentMethods%22%3A6%2C%22maximumNumberOfPayPalAccounts%22%3A1%2C%22gaProfile%22%3A%22UA-1347088-1%22%2C%22optimizelyProjectId%22%3A%************%22%2C%22gtmContainerId%22%3A%22GTM-TW5K2R%22%2C%22gaTagId%22%3A%22G-27R89QXH5C%22%2C%22googleAdsTagId%22%3A%22AW-*********%22%2C%22mixPanelToken%22%3A%22566bb5486d5a68f0ee7b3c62620db272%22%2C%22newMixPanelToken%22%3A%22b26c37959d41819eddd5cd6f443cb2b2%22%2C%22siftKey%22%3A%2239796971f6%22%2C%22listings%22%3A%7B%22edit%22%3A%7B%22photo%22%3A%7B%22aspectRatio%22%3A1%7D%7D%7D%2C%22@sentry/ember%22%3A%7B%22enabled%22%3Atrue%2C%22dsn%22%3A%22https%3A//<EMAIL>/2704171%22%2C%22ignoreErrors%22%3A%5B%22TransitionAborted%22%2C%22TransitionAborted.*%22%2C%7B%7D%2C%22The%20adapter%20operation%20was%20aborted%22%2C%22Adapter%20operation%20failed%22%2C%22ResizeObserver%20loop%20limit%20exceeded%22%2C%7B%7D%2C%7B%7D%2C%7B%7D%5D%2C%22disablePerformance%22%3Atrue%7D%2C%22biFbTids%22%3A%5B%2244YATHAT%22%2C%220ISCPX6Z%22%2C%22XQCXOCNZ%22%5D%2C%22checkoutPublicKey%22%3A%22pk_n37djaeznookmebv4t4ejezksmx%22%2C%22name%22%3A%22production%22%7D%2C%22EmberHammertime%22%3A%7B%22touchActionOnAction%22%3Afalse%2C%22touchActionAttributes%22%3A%5B%22%22%5D%2C%22touchActionSelectors%22%3A%5B%22.x-toggle-component%22%5D%2C%22touchActionProperties%22%3A%22%22%7D%2C%22APP%22%3A%7B%22name%22%3A%22ingenio-web%22%2C%22version%22%3A%220.1.0%22%7D%2C%22hotjar%22%3A%7B%22id%22%3A344517%7D%2C%22metricsAdapters%22%3A%5B%7B%22name%22%3A%22snapchat-pixel%22%2C%22environments%22%3A%5B%22all%22%5D%2C%22config%22%3A%7B%22id%22%3A%22e0555508-c06e-49b8-97b9-d90681d48600%22%7D%7D%2C%7B%22name%22%3A%22fb-pixel%22%2C%22environments%22%3A%5B%22all%22%5D%2C%22config%22%3A%7B%22betterImpressionId%22%3A%22666635363792303%22%7D%7D%2C%7B%22name%22%3A%22tiktok-pixel%22%2C%22environments%22%3A%5B%22all%22%5D%2C%22config%22%3A%7B%22id%22%3A%22C1NPOM1LRI5O97LI1VEG%22%7D%7D%5D%2C%22contentSecurityPolicy%22%3A%7B%22default-src%22%3A%22%27none%27%22%2C%22script-src%22%3A%22%27self%27%20%27unsafe-inline%27%20%27unsafe-eval%27%20*.tiktok.com%20*.marketo.com%20*.marketo.net%20sc-static.net%20*.google-analytics.com%20pixel-geo.prfct.co%20google.com%20*.mxpnl.com%20*.googleadservices.com%20%20*.optimizely.com%20*.prfct.co%20tag.marinsm.com%20*.google-analytics.com%20use.typekit.net%20connect.facebook.net%20*.googletagmanager.com%20*.googleapis.com%20maps.gstatic.com%20*.convertro.com%20*.getjaco.com%20*.cloudfront.net%20script.crazyegg.com%20seal.digicert.com%20*.ingenio.com%20mpsnare.iesnare.com%20*.hotjar.com%20*.acsbapp.com%20*.doubleclick.net%20cdn.sift.com%20*.snapchat.com%20*.checkout.com%20*.pubnub.com%20*.cookiebot.com%20*.cdn-apple.com%22%2C%22font-src%22%3A%22%27self%27%20https%3A//fonts.gstatic.com%20https%3A//fonts.googleapis.com%20https%3A//keen.dev.ingenio.com/%22%2C%22connect-src%22%3A%22*%22%2C%22frame-src%22%3A%22*%22%2C%22img-src%22%3A%22%27self%27%20data%3A%20*%20*.google.co.in%22%2C%22style-src%22%3A%22%27self%27%20%27unsafe-eval%27%20%27unsafe-inline%27%20*.marketo.com%20https%3A//fonts.googleapis.com%22%2C%22media-src%22%3A%22%27self%27%20data%3A%22%7D%2C%22contentSecurityPolicyHeader%22%3A%22Content-Security-Policy-Report-Only%22%2C%22ember-paper%22%3A%7B%22insertFontLinks%22%3Atrue%7D%2C%22exportApplicationGlobal%22%3Afalse%2C%22ember-modal-dialog%22%3A%7B%7D%2C%22ember-addon%22%3A%7B%22versionCompatibility%22%3A%7B%22ember%22%3A%22%3E%3D2.0.0%22%7D%7D%7D" />
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,400italic"> <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">

    <link rel="stylesheet" href="/app/assets/bootstrap-5a6d3269eab587ecc5d747f6860413db.css" integrity="" />
    <link rel="stylesheet" href="/app/assets/vendor-73088be9284266346067e35a45f5f24e.css" integrity="" />
    <link rel="stylesheet" href="/app/assets/app-0bd17df10cdfde4d1b8517be5aaaa5a5.css" integrity="" />
    <!-- TODO find proper place for import -->

    <script src="/app/assets/mixpanel-snippet-fd2b09af7c9909de56326272d5f32aa1.js" integrity=""></script>

    <!-- Google Analytics  -->
    <script
      type="text/javascript"
      src="https://www.google-analytics.com/analytics.js"
    ></script>
    <!-- End Google Analytics -->

    <!-- Checkout.com Script -->
    <script src="https://cdn.checkout.com/js/framesv2.min.js"></script>
    <script id="risk-js" async src="https://risk.checkout.com/cdn/risk/1.2/risk.js"></script>
    <script lang="javascript">
      const script = document.getElementById('risk-js');

      script.addEventListener('load', () => {
        console.log('RISK INITED!!!')
      });
    </script>

    <!-- PubNub -->
    <script src="https://cdn.pubnub.com/sdk/javascript/pubnub.9.1.0.min.js"></script>

    <!-- AppleJS SDK -->
    <script crossorigin 
      src="https://applepay.cdn-apple.com/jsapi/1.latest/apple-pay-sdk.js"> 
    </script>

    <!-- RTP tag -->
    <script type='text/javascript'>
      (function (c, h, a, f, i, e) {
        c[a] = c[a] || function () {
          (c[a].q = c[a].q || []).push(arguments)
        };
        c[a].a = i;
        c[a].e = e;
        var g = h.createElement("script");
        g.async = true;
        g.type = "text/javascript";
        g.src = f + '?aid=' + i;
        var b = h.getElementsByTagName("script")[0];
        b.parentNode.insertBefore(g, b);
      })(window, document, "rtp", "//sjrtp4-cdn.marketo.com/rtp-api/v1/rtp.js", "ingenio");

      rtp('send', 'view');
      rtp('get', 'campaign', true);
    </script>
    <!-- End of RTP tag -->

    <!-- Crazy Egg -->
    <script type="text/javascript">
      setTimeout(function () {
        var a = document.createElement("script");
        var b = document.getElementsByTagName("script")[0];
        a.src =
          document.location.protocol +
          "//script.crazyegg.com/pages/scripts/0022/3950.js?" +
          Math.floor(new Date().getTime() / 3600000);
        a.async = true;
        a.type = "text/javascript";
        b.parentNode.insertBefore(a, b);
      }, 1);
    </script>
    <!-- End: Crazy Egg -->

    
  </head>

  <body>
    <!-- Start: Iovation 
    TODO: We should avoid those hidden field and check with iovation team to see if we can directly get it from the iovation
    object which is created by the script.
  -->
    <script>
      //iovation code
      var io_operation = "ioBegin";
      var io_bbout_element_id = "io_blackbox";
      var io_install_stm = false;
      var io_install_flash = false;
      var io_exclude_stm = 12;
    </script>
    <script
      language="Javascript"
      src="https://mpsnare.iesnare.com/snare.js"
    ></script>
    <input name="io_blackbox" type="hidden" id="io_blackbox" />
    <!-- End: Iovation-->

    

    <script src="/app/assets/vendor-b35a347044e2d86376324f9afb959651.js" integrity=""></script>
    <script src="/app/assets/ingenio-web-0e291f9cc09a9f9920990ced8dd425b3.js" integrity=""></script>

    
          <div id="ember-basic-dropdown-wormhole"></div>
          <div id="paper-wormhole"></div>
          <div id="paper-toast-fab-wormhole"></div>
        

    <!-- TODO: workaround for http-only cookie change. we should revisit. -->
    <script type="text/javascript">
      window.ENV = { keenUid: "" };
    </script>
  </body>
</html>
