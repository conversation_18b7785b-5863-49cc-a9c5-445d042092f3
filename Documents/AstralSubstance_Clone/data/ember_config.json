{"modulePrefix": "ingenio-web", "environment": "production", "rootURL": "/app/", "baseURL": "/app/", "locationType": "auto", "EmberENV": {"domainId": 1, "domainName": "<PERSON><PERSON>", "apiRoot": "/api", "cloverApiRoot": "https://clover-api.ingenio.com/api", "logApiRoot": "/api", "urls": {"callStatus": "/DomainOverrides/Advice/Calls/AjaxWs/CallStatus.asmx/GetStatus", "myAccountPage": "../myaccount", "ccap": "https://ccap-trans.ingenio.com/", "riskScript": "https://risk.checkout.com/cdn/risk/1.2/risk.js", "loginPage": "../Registration/LoginStarter.aspx", "errorPage": "../ServerError.aspx", "forgotPasswordPage": "../Registration/ForgotPassword.aspx", "idvAuthPage": "../secure/credentials/IDAuth.asp?ReturnUrl=", "cvnRequiredPage": "../secure/Payments/Pay/DepositConfirm.aspx?ReturnUrl=", "zendeskDomainNameUs": "https://help.keen.com/hc/en-us", "nextJSRoot": ""}, "paypal": {"expressCheckoutUrl": "https://www.paypal.com/cgi-bin/webscr?cmd=_express-checkout"}, "applePay": {"merchantId": "merchant.com.ingenio.prod"}, "shouldAuthenticate": true, "FEATURES": {}, "minimumListingPrice": 1.99, "minimumPsychicPartySessionPrice": 250, "minimumHourlySessionPrice": 100, "minimumDepositAmount": 1, "repeatUserMinimumDepositAmount": 10, "maximumDepositAmount": 1000, "minimumBalanceMinutesForConversation": 1, "maximumNumberOfPaymentMethods": 6, "maximumNumberOfPayPalAccounts": 1, "gaProfile": "UA-1347088-1", "optimizelyProjectId": "**********", "gtmContainerId": "GTM-TW5K2R", "gaTagId": "G-27R89QXH5C", "googleAdsTagId": "AW-*********", "mixPanelToken": "566bb5486d5a68f0ee7b3c62620db272", "newMixPanelToken": "b26c37959d41819eddd5cd6f443cb2b2", "siftKey": "39796971f6", "listings": {"edit": {"photo": {"aspectRatio": 1}}}, "@sentry/ember": {"enabled": true, "dsn": "https://<EMAIL>/2704171", "ignoreErrors": ["TransitionAborted", "TransitionAborted.*", {}, "The adapter operation was aborted", "Adapter operation failed", "ResizeObserver loop limit exceeded", {}, {}, {}], "disablePerformance": true}, "biFbTids": ["44YATHAT", "0ISCPX6Z", "XQCXOCNZ"], "checkoutPublicKey": "pk_n37djaeznookmebv4t4ejezksmx", "name": "production"}, "EmberHammertime": {"touchActionOnAction": false, "touchActionAttributes": [""], "touchActionSelectors": [".x-toggle-component"], "touchActionProperties": ""}, "APP": {"name": "ingenio-web", "version": "0.1.0"}, "hotjar": {"id": 344517}, "metricsAdapters": [{"name": "snapchat-pixel", "environments": ["all"], "config": {"id": "e0555508-c06e-49b8-97b9-d90681d48600"}}, {"name": "fb-pixel", "environments": ["all"], "config": {"betterImpressionId": "666635363792303"}}, {"name": "tiktok-pixel", "environments": ["all"], "config": {"id": "C1NPOM1LRI5O97LI1VEG"}}], "contentSecurityPolicy": {"default-src": "'none'", "script-src": "'self' 'unsafe-inline' 'unsafe-eval' *.tiktok.com *.marketo.com *.marketo.net sc-static.net *.google-analytics.com pixel-geo.prfct.co google.com *.mxpnl.com *.googleadservices.com  *.optimizely.com *.prfct.co tag.marinsm.com *.google-analytics.com use.typekit.net connect.facebook.net *.googletagmanager.com *.googleapis.com maps.gstatic.com *.convertro.com *.getjaco.com *.cloudfront.net script.crazyegg.com seal.digicert.com *.ingenio.com mpsnare.iesnare.com *.hotjar.com *.acsbapp.com *.doubleclick.net cdn.sift.com *.snapchat.com *.checkout.com *.pubnub.com *.cookiebot.com *.cdn-apple.com", "font-src": "'self' https://fonts.gstatic.com https://fonts.googleapis.com https://keen.dev.ingenio.com/", "connect-src": "*", "frame-src": "*", "img-src": "'self' data: * *.google.co.in", "style-src": "'self' 'unsafe-eval' 'unsafe-inline' *.marketo.com https://fonts.googleapis.com", "media-src": "'self' data:"}, "contentSecurityPolicyHeader": "Content-Security-Policy-Report-Only", "ember-paper": {"insertFontLinks": true}, "exportApplicationGlobal": false, "ember-modal-dialog": {}, "ember-addon": {"versionCompatibility": {"ember": ">=2.0.0"}}}