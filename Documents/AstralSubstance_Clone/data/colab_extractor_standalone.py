# Google Colab Setup\n
# Run this cell first in Google Colab
!pip install selenium webdriver-manager requests beautifulsoup4

# Install Chrome and ChromeDriver
!apt-get update
!apt-get install -y chromium-browser chromium-chromedriver

# Set up display for headless browsing
!apt-get install -y xvfb
!pip install pyvirtualdisplay

import os
os.environ['PATH'] += ':/usr/lib/chromium-browser/'
\n\n# Main Extraction Code\n
import json
import time
import requests
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from pyvirtualdisplay import Display
import re

class ColabChatExtractor:
    """Extract chat transcripts using Google Colab's IP"""

    def __init__(self):
        self.driver = None
        self.chat_transcripts = {}
        self.errors = []

        # Your Keen.com authentication cookies
        self.keen_cookies = {
            "KeenUid": "YOUR_KEEN_UID_HERE",
            "KeenTKT": "YOUR_KEEN_TKT_HERE",
            "KeenUser": "YOUR_KEEN_USER_HERE",
            "SessionId": "YOUR_SESSION_ID_HERE"
        }

        # Customer interaction data (from our previous extraction)
        self.pending_interactions = []

    def setup_colab_browser(self):
        """Setup Chrome browser in Colab environment"""

        # Start virtual display
        display = Display(visible=0, size=(1920, 1080))
        display.start()

        # Chrome options for Colab
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Random user agent
        user_agents = [
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0"
        ]
        chrome_options.add_argument(f"--user-agent={user_agents[0]}")

        # Initialize driver
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        print("✅ Browser setup complete in Colab")
        return True

    def authenticate_keen(self):
        """Authenticate with Keen.com using cookies"""

        try:
            # Navigate to Keen.com
            self.driver.get("https://www.keen.com")
            time.sleep(3)

            # Set authentication cookies
            for name, value in self.keen_cookies.items():
                if value and not value.startswith('YOUR_'):
                    self.driver.add_cookie({
                        'name': name,
                        'value': value,
                        'domain': '.keen.com'
                    })

            # Refresh to apply cookies
            self.driver.refresh()
            time.sleep(2)

            print("✅ Authenticated with Keen.com")
            return True

        except Exception as e:
            print(f"❌ Authentication failed: {e}")
            return False

    def load_pending_interactions(self):
        """Load the interaction IDs we need to extract"""

        # Sample interaction data (replace with actual data from your extraction)
        sample_interactions = [
            {
                "customer_id": "********",
                "customer_username": "User97925248",
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "high"
            },
            {
                "customer_id": "********",
                "customer_username": "User50690268",
                "interaction_id": "********",
                "interaction_type": "chat",
                "chat_url": "https://www.keen.com/myaccount/transactions/chat-details?id=********",
                "priority": "high"
            }
            # Add more interactions here from your data
        ]

        self.pending_interactions = sample_interactions
        print(f"📋 Loaded {len(self.pending_interactions)} pending interactions")

    def extract_chat_transcript(self, interaction):
        """Extract a single chat transcript"""

        try:
            chat_url = interaction['chat_url']
            interaction_id = interaction['interaction_id']

            print(f"  💬 Extracting chat {interaction_id}...")

            # Navigate to chat transcript page
            self.driver.get(chat_url)
            time.sleep(4)

            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Extract messages
            messages = self.extract_messages_from_page()

            if messages:
                transcript = {
                    'interaction_id': interaction_id,
                    'customer_id': interaction['customer_id'],
                    'customer_username': interaction['customer_username'],
                    'chat_url': chat_url,
                    'messages': messages,
                    'message_count': len(messages),
                    'extraction_timestamp': datetime.now().isoformat(),
                    'extraction_method': 'google_colab',
                    'colab_ip': self.get_colab_ip()
                }

                print(f"    ✅ Extracted {len(messages)} messages")
                return transcript
            else:
                print(f"    ℹ️  No messages found")
                return None

        except Exception as e:
            print(f"    ❌ Error extracting chat {interaction.get('interaction_id', 'unknown')}: {e}")
            return None

    def extract_messages_from_page(self):
        """Extract messages from chat transcript page"""

        messages = []

        try:
            # Multiple strategies to find messages
            message_selectors = [
                '.message',
                '.chat-message',
                '.conversation-message',
                '[class*="message"]',
                '[class*="chat"]',
                'p',
                'div[class*="text"]',
                '.transcript-line'
            ]

            for selector in message_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for i, element in enumerate(elements):
                        text = element.text.strip()
                        if text and len(text) > 3:
                            message = {
                                'sequence': i + 1,
                                'content': text,
                                'sender_type': self.infer_sender_type(text),
                                'element_class': element.get_attribute('class') or '',
                                'extraction_method': selector
                            }

                            # Try to get timestamp
                            try:
                                timestamp_elem = element.find_element(By.CSS_SELECTOR, '.timestamp, .time, [data-time]')
                                if timestamp_elem:
                                    message['timestamp'] = timestamp_elem.text or timestamp_elem.get_attribute('data-time')
                            except:
                                pass

                            messages.append(message)

                    if messages:  # Found messages with this selector
                        break

                except:
                    continue

            # If no structured messages, try page text extraction
            if not messages:
                page_text = self.driver.find_element(By.TAG_NAME, 'body').text
                lines = [line.strip() for line in page_text.split('\n') if line.strip()]

                for i, line in enumerate(lines):
                    if len(line) > 10:
                        message = {
                            'sequence': i + 1,
                            'content': line,
                            'sender_type': self.infer_sender_type(line),
                            'extraction_method': 'page_text'
                        }
                        messages.append(message)

            # Process messages for ordering
            if messages:
                messages = self.process_message_ordering(messages)

        except Exception as e:
            print(f"      ❌ Error extracting messages: {e}")

        return messages

    def infer_sender_type(self, text):
        """Infer if message is from advisor or customer"""

        text_lower = text.lower()

        # Advisor patterns
        advisor_patterns = [
            'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',
            'let me', 'i can help', 'what i\'m getting', 'i\'m seeing',
            'the universe', 'your guides', 'i\'m picking up'
        ]

        # Customer patterns
        customer_patterns = [
            'will i', 'should i', 'when will', 'what about', 'can you tell me',
            'i want to know', 'my question', 'help me understand'
        ]

        advisor_score = sum(1 for pattern in advisor_patterns if pattern in text_lower)
        customer_score = sum(1 for pattern in customer_patterns if pattern in text_lower)

        if advisor_score > customer_score:
            return 'advisor'
        elif customer_score > advisor_score:
            return 'customer'
        else:
            return 'unknown'

    def process_message_ordering(self, messages):
        """Process message ordering to resolve issues"""

        # Sort by timestamp if available
        timestamped = [msg for msg in messages if msg.get('timestamp')]
        non_timestamped = [msg for msg in messages if not msg.get('timestamp')]

        if timestamped:
            try:
                # Simple timestamp sorting
                timestamped.sort(key=lambda x: x.get('timestamp', ''))

                for i, msg in enumerate(timestamped):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = True

            except:
                for i, msg in enumerate(timestamped):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = False

        # Handle non-timestamped messages
        for i, msg in enumerate(non_timestamped):
            msg['sequence'] = len(timestamped) + i + 1
            msg['order_resolved'] = False

        return timestamped + non_timestamped

    def get_colab_ip(self):
        """Get Colab's current IP address"""

        try:
            response = requests.get('https://httpbin.org/ip', timeout=10)
            if response.status_code == 200:
                return response.json().get('origin', 'Unknown')
        except:
            pass
        return 'Unknown'

    def run_extraction(self):
        """Run the complete extraction process"""

        print("🚀 GOOGLE COLAB CHAT EXTRACTOR")
        print("=" * 50)

        # Check Colab IP
        colab_ip = self.get_colab_ip()
        print(f"🌐 Colab IP: {colab_ip}")

        # Setup browser
        if not self.setup_colab_browser():
            return

        # Authenticate
        if not self.authenticate_keen():
            return

        # Load interactions to extract
        self.load_pending_interactions()

        # Extract chat transcripts
        print(f"\n💬 Extracting {len(self.pending_interactions)} chat transcripts...")

        for i, interaction in enumerate(self.pending_interactions, 1):
            customer_username = interaction['customer_username']
            interaction_id = interaction['interaction_id']

            print(f"\n👤 {i}/{len(self.pending_interactions)}: {customer_username} (Chat {interaction_id})")

            try:
                transcript = self.extract_chat_transcript(interaction)

                if transcript:
                    customer_id = interaction['customer_id']

                    if customer_id not in self.chat_transcripts:
                        self.chat_transcripts[customer_id] = {
                            'customer_info': {
                                'customer_id': customer_id,
                                'username': customer_username
                            },
                            'transcripts': []
                        }

                    self.chat_transcripts[customer_id]['transcripts'].append(transcript)

            except Exception as e:
                error_msg = f"Error processing {interaction_id}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)

            # Rate limiting
            time.sleep(3)

        # Save results
        self.save_results()

        # Cleanup
        if self.driver:
            self.driver.quit()

    def save_results(self):
        """Save extraction results"""

        print(f"\n💾 Saving results...")

        # Create dataset
        dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'google_colab',
                'colab_ip': self.get_colab_ip(),
                'customers_with_transcripts': len(self.chat_transcripts),
                'total_transcripts': sum(len(c['transcripts']) for c in self.chat_transcripts.values()),
                'total_messages': sum(
                    sum(len(t.get('messages', [])) for t in c['transcripts'])
                    for c in self.chat_transcripts.values()
                )
            },
            'chat_transcripts': self.chat_transcripts,
            'errors': self.errors
        }

        # Save as JSON
        with open('colab_chat_transcripts.json', 'w') as f:
            json.dump(dataset, f, indent=2)

        print(f"✅ Results saved to colab_chat_transcripts.json")
        print(f"📊 Extracted transcripts for {len(self.chat_transcripts)} customers")
        print(f"💬 Total transcripts: {dataset['metadata']['total_transcripts']}")
        print(f"📝 Total messages: {dataset['metadata']['total_messages']}")

        # Download file in Colab
        try:
            from google.colab import files
            files.download('colab_chat_transcripts.json')
            print("📥 File downloaded to your computer")
        except:
            print("ℹ️  File saved in Colab environment")

# Run the extraction
if __name__ == "__main__":
    extractor = ColabChatExtractor()
    extractor.run_extraction()
