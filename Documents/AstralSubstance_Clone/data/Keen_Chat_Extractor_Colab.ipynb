{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Keen.com Chat Transcript Extractor\\n", "\\n", "This notebook extracts chat transcripts from Keen.com using Google Colab's IP address.\\n", "\\n", "## Setup Instructions\\n", "1. Run the setup cell first\\n", "2. Update your authentication cookies in the main code\\n", "3. Add your interaction IDs to extract\\n", "4. Run the extraction\\n", "\\n", "**Benefits of using Colab:**\\n", "- Fresh IP address from Google's infrastructure\\n", "- Free compute resources\\n", "- No local setup required\\n", "- Easy file download"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SETUP CELL - Run this first\\n", "\n# Run this cell first in Google Colab\n!pip install selenium webdriver-manager requests beautifulsoup4\n\n# Install Chrome and ChromeDriver\n!apt-get update\n!apt-get install -y chromium-browser chromium-chromedriver\n\n# Set up display for headless browsing\n!apt-get install -y xvfb\n!pip install pyvirtualdisplay\n\nimport os\nos.environ['PATH'] += ':/usr/lib/chromium-browser/'\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MAIN EXTRACTION CODE\\n", "# Update the cookies and interaction data before running\\n", "\nimport json\nimport time\nimport requests\nfrom datetime import datetime\nfrom selenium import webdriver\nfrom selenium.webdriver.common.by import By\nfrom selenium.webdriver.chrome.options import Options\nfrom selenium.webdriver.support.ui import WebDriverWait\nfrom selenium.webdriver.support import expected_conditions as EC\nfrom pyvirtualdisplay import Display\nimport re\n\nclass ColabChatExtractor:\n    \"\"\"Extract chat transcripts using Google Colab's IP\"\"\"\n\n    def __init__(self):\n        self.driver = None\n        self.chat_transcripts = {}\n        self.errors = []\n\n        # Your Keen.com authentication cookies\n        self.keen_cookies = {\n            \"KeenUid\": \"YOUR_KEEN_UID_HERE\",\n            \"KeenTKT\": \"YOUR_KEEN_TKT_HERE\",\n            \"KeenUser\": \"YOUR_KEEN_USER_HERE\",\n            \"SessionId\": \"YOUR_SESSION_ID_HERE\"\n        }\n\n        # Customer interaction data (from our previous extraction)\n        self.pending_interactions = []\n\n    def setup_colab_browser(self):\n        \"\"\"Setup Chrome browser in Colab environment\"\"\"\n\n        # Start virtual display\n        display = Display(visible=0, size=(1920, 1080))\n        display.start()\n\n        # Chrome options for Colab\n        chrome_options = Options()\n        chrome_options.add_argument(\"--headless\")\n        chrome_options.add_argument(\"--no-sandbox\")\n        chrome_options.add_argument(\"--disable-dev-shm-usage\")\n        chrome_options.add_argument(\"--disable-gpu\")\n        chrome_options.add_argument(\"--window-size=1920,1080\")\n        chrome_options.add_argument(\"--disable-blink-features=AutomationControlled\")\n        chrome_options.add_experimental_option(\"excludeSwitches\", [\"enable-automation\"])\n        chrome_options.add_experimental_option('useAutomationExtension', False)\n\n        # Random user agent\n        user_agents = [\n            \"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\",\n            \"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0\"\n        ]\n        chrome_options.add_argument(f\"--user-agent={user_agents[0]}\")\n\n        # Initialize driver\n        self.driver = webdriver.Chrome(options=chrome_options)\n        self.driver.execute_script(\"Object.defineProperty(navigator, 'webdriver', {get: () => undefined})\")\n\n        print(\"✅ Browser setup complete in Colab\")\n        return True\n\n    def authenticate_keen(self):\n        \"\"\"Authenticate with Keen.com using cookies\"\"\"\n\n        try:\n            # Navigate to Keen.com\n            self.driver.get(\"https://www.keen.com\")\n            time.sleep(3)\n\n            # Set authentication cookies\n            for name, value in self.keen_cookies.items():\n                if value and not value.startswith('YOUR_'):\n                    self.driver.add_cookie({\n                        'name': name,\n                        'value': value,\n                        'domain': '.keen.com'\n                    })\n\n            # Refresh to apply cookies\n            self.driver.refresh()\n            time.sleep(2)\n\n            print(\"✅ Authenticated with Keen.com\")\n            return True\n\n        except Exception as e:\n            print(f\"❌ Authentication failed: {e}\")\n            return False\n\n    def load_pending_interactions(self):\n        \"\"\"Load the interaction IDs we need to extract\"\"\"\n\n        # Sample interaction data (replace with actual data from your extraction)\n        sample_interactions = [\n            {\n                \"customer_id\": \"********\",\n                \"customer_username\": \"User97925248\",\n                \"interaction_id\": \"********\",\n                \"interaction_type\": \"chat\",\n                \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",\n                \"priority\": \"high\"\n            },\n            {\n                \"customer_id\": \"********\",\n                \"customer_username\": \"User50690268\",\n                \"interaction_id\": \"********\",\n                \"interaction_type\": \"chat\",\n                \"chat_url\": \"https://www.keen.com/myaccount/transactions/chat-details?id=********\",\n                \"priority\": \"high\"\n            }\n            # Add more interactions here from your data\n        ]\n\n        self.pending_interactions = sample_interactions\n        print(f\"📋 Loaded {len(self.pending_interactions)} pending interactions\")\n\n    def extract_chat_transcript(self, interaction):\n        \"\"\"Extract a single chat transcript\"\"\"\n\n        try:\n            chat_url = interaction['chat_url']\n            interaction_id = interaction['interaction_id']\n\n            print(f\"  💬 Extracting chat {interaction_id}...\")\n\n            # Navigate to chat transcript page\n            self.driver.get(chat_url)\n            time.sleep(4)\n\n            # Wait for page to load\n            WebDriverWait(self.driver, 15).until(\n                EC.presence_of_element_located((By.TAG_NAME, \"body\"))\n            )\n\n            # Extract messages\n            messages = self.extract_messages_from_page()\n\n            if messages:\n                transcript = {\n                    'interaction_id': interaction_id,\n                    'customer_id': interaction['customer_id'],\n                    'customer_username': interaction['customer_username'],\n                    'chat_url': chat_url,\n                    'messages': messages,\n                    'message_count': len(messages),\n                    'extraction_timestamp': datetime.now().isoformat(),\n                    'extraction_method': 'google_colab',\n                    'colab_ip': self.get_colab_ip()\n                }\n\n                print(f\"    ✅ Extracted {len(messages)} messages\")\n                return transcript\n            else:\n                print(f\"    ℹ️  No messages found\")\n                return None\n\n        except Exception as e:\n            print(f\"    ❌ Error extracting chat {interaction.get('interaction_id', 'unknown')}: {e}\")\n            return None\n\n    def extract_messages_from_page(self):\n        \"\"\"Extract messages from chat transcript page\"\"\"\n\n        messages = []\n\n        try:\n            # Multiple strategies to find messages\n            message_selectors = [\n                '.message',\n                '.chat-message',\n                '.conversation-message',\n                '[class*=\"message\"]',\n                '[class*=\"chat\"]',\n                'p',\n                'div[class*=\"text\"]',\n                '.transcript-line'\n            ]\n\n            for selector in message_selectors:\n                try:\n                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)\n\n                    for i, element in enumerate(elements):\n                        text = element.text.strip()\n                        if text and len(text) > 3:\n                            message = {\n                                'sequence': i + 1,\n                                'content': text,\n                                'sender_type': self.infer_sender_type(text),\n                                'element_class': element.get_attribute('class') or '',\n                                'extraction_method': selector\n                            }\n\n                            # Try to get timestamp\n                            try:\n                                timestamp_elem = element.find_element(By.CSS_SELECTOR, '.timestamp, .time, [data-time]')\n                                if timestamp_elem:\n                                    message['timestamp'] = timestamp_elem.text or timestamp_elem.get_attribute('data-time')\n                            except:\n                                pass\n\n                            messages.append(message)\n\n                    if messages:  # Found messages with this selector\n                        break\n\n                except:\n                    continue\n\n            # If no structured messages, try page text extraction\n            if not messages:\n                page_text = self.driver.find_element(By.TAG_NAME, 'body').text\n                lines = [line.strip() for line in page_text.split('\\n') if line.strip()]\n\n                for i, line in enumerate(lines):\n                    if len(line) > 10:\n                        message = {\n                            'sequence': i + 1,\n                            'content': line,\n                            'sender_type': self.infer_sender_type(line),\n                            'extraction_method': 'page_text'\n                        }\n                        messages.append(message)\n\n            # Process messages for ordering\n            if messages:\n                messages = self.process_message_ordering(messages)\n\n        except Exception as e:\n            print(f\"      ❌ Error extracting messages: {e}\")\n\n        return messages\n\n    def infer_sender_type(self, text):\n        \"\"\"Infer if message is from advisor or customer\"\"\"\n\n        text_lower = text.lower()\n\n        # Advisor patterns\n        advisor_patterns = [\n            'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',\n            'let me', 'i can help', 'what i\\'m getting', 'i\\'m seeing',\n            'the universe', 'your guides', 'i\\'m picking up'\n        ]\n\n        # Customer patterns\n        customer_patterns = [\n            'will i', 'should i', 'when will', 'what about', 'can you tell me',\n            'i want to know', 'my question', 'help me understand'\n        ]\n\n        advisor_score = sum(1 for pattern in advisor_patterns if pattern in text_lower)\n        customer_score = sum(1 for pattern in customer_patterns if pattern in text_lower)\n\n        if advisor_score > customer_score:\n            return 'advisor'\n        elif customer_score > advisor_score:\n            return 'customer'\n        else:\n            return 'unknown'\n\n    def process_message_ordering(self, messages):\n        \"\"\"Process message ordering to resolve issues\"\"\"\n\n        # Sort by timestamp if available\n        timestamped = [msg for msg in messages if msg.get('timestamp')]\n        non_timestamped = [msg for msg in messages if not msg.get('timestamp')]\n\n        if timestamped:\n            try:\n                # Simple timestamp sorting\n                timestamped.sort(key=lambda x: x.get('timestamp', ''))\n\n                for i, msg in enumerate(timestamped):\n                    msg['sequence'] = i + 1\n                    msg['order_resolved'] = True\n\n            except:\n                for i, msg in enumerate(timestamped):\n                    msg['sequence'] = i + 1\n                    msg['order_resolved'] = False\n\n        # Handle non-timestamped messages\n        for i, msg in enumerate(non_timestamped):\n            msg['sequence'] = len(timestamped) + i + 1\n            msg['order_resolved'] = False\n\n        return timestamped + non_timestamped\n\n    def get_colab_ip(self):\n        \"\"\"Get Colab's current IP address\"\"\"\n\n        try:\n            response = requests.get('https://httpbin.org/ip', timeout=10)\n            if response.status_code == 200:\n                return response.json().get('origin', 'Unknown')\n        except:\n            pass\n        return 'Unknown'\n\n    def run_extraction(self):\n        \"\"\"Run the complete extraction process\"\"\"\n\n        print(\"🚀 GOOGLE COLAB CHAT EXTRACTOR\")\n        print(\"=\" * 50)\n\n        # Check Colab IP\n        colab_ip = self.get_colab_ip()\n        print(f\"🌐 Colab IP: {colab_ip}\")\n\n        # Setup browser\n        if not self.setup_colab_browser():\n            return\n\n        # Authenticate\n        if not self.authenticate_keen():\n            return\n\n        # Load interactions to extract\n        self.load_pending_interactions()\n\n        # Extract chat transcripts\n        print(f\"\\n💬 Extracting {len(self.pending_interactions)} chat transcripts...\")\n\n        for i, interaction in enumerate(self.pending_interactions, 1):\n            customer_username = interaction['customer_username']\n            interaction_id = interaction['interaction_id']\n\n            print(f\"\\n👤 {i}/{len(self.pending_interactions)}: {customer_username} (Chat {interaction_id})\")\n\n            try:\n                transcript = self.extract_chat_transcript(interaction)\n\n                if transcript:\n                    customer_id = interaction['customer_id']\n\n                    if customer_id not in self.chat_transcripts:\n                        self.chat_transcripts[customer_id] = {\n                            'customer_info': {\n                                'customer_id': customer_id,\n                                'username': customer_username\n                            },\n                            'transcripts': []\n                        }\n\n                    self.chat_transcripts[customer_id]['transcripts'].append(transcript)\n\n            except Exception as e:\n                error_msg = f\"Error processing {interaction_id}: {e}\"\n                print(f\"    ❌ {error_msg}\")\n                self.errors.append(error_msg)\n\n            # Rate limiting\n            time.sleep(3)\n\n        # Save results\n        self.save_results()\n\n        # Cleanup\n        if self.driver:\n            self.driver.quit()\n\n    def save_results(self):\n        \"\"\"Save extraction results\"\"\"\n\n        print(f\"\\n💾 Saving results...\")\n\n        # Create dataset\n        dataset = {\n            'metadata': {\n                'extraction_timestamp': datetime.now().isoformat(),\n                'extraction_method': 'google_colab',\n                'colab_ip': self.get_colab_ip(),\n                'customers_with_transcripts': len(self.chat_transcripts),\n                'total_transcripts': sum(len(c['transcripts']) for c in self.chat_transcripts.values()),\n                'total_messages': sum(\n                    sum(len(t.get('messages', [])) for t in c['transcripts'])\n                    for c in self.chat_transcripts.values()\n                )\n            },\n            'chat_transcripts': self.chat_transcripts,\n            'errors': self.errors\n        }\n\n        # Save as JSON\n        with open('colab_chat_transcripts.json', 'w') as f:\n            json.dump(dataset, f, indent=2)\n\n        print(f\"✅ Results saved to colab_chat_transcripts.json\")\n        print(f\"📊 Extracted transcripts for {len(self.chat_transcripts)} customers\")\n        print(f\"💬 Total transcripts: {dataset['metadata']['total_transcripts']}\")\n        print(f\"📝 Total messages: {dataset['metadata']['total_messages']}\")\n\n        # Download file in Colab\n        try:\n            from google.colab import files\n            files.download('colab_chat_transcripts.json')\n            print(\"📥 File downloaded to your computer\")\n        except:\n            print(\"ℹ️  File saved in Colab environment\")\n\n# Run the extraction\nif __name__ == \"__main__\":\n    extractor = ColabChatExtractor()\n    extractor.run_extraction()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Steps\\n", "\\n", "After extraction:\\n", "1. Download the JSON file\\n", "2. Upload to your main project\\n", "3. Process with message ordering resolution\\n", "4. Combine with other data sources\\n", "5. Create final LLM training dataset"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}