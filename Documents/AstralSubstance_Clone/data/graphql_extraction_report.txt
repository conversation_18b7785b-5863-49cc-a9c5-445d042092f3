
GRAPHQL CUSTOMER EXTRACTION REPORT
==================================

EXTRACTION SUMMARY:
- Timestamp: 2025-05-24T20:27:05.269911
- Method: graphql_pagination
- Source: /api/graphqlv0
- Total Customers: 4297

DATA QUALITY METRICS:
- Customers with username: 4297/4297 (100.0%)
- Customers with nickname: 0/4297 (0.0%)
- Customers with contact history: 2447/4297 (56.9%)
- Customers with earnings data: 4297/4297 (100.0%)

ERRORS ENCOUNTERED:
- None

NEXT STEPS:
1. Use customer IDs and transaction IDs for Google Voice correlation
2. Extract call recordings using phone number (*************
3. Transcribe recordings with Whisper
4. Correlate conversations with customer profiles
5. Create comprehensive LLM training dataset

FILES CREATED:
- graphql_complete_customers.json: Complete customer data
- graphql_customers_llm_format.json: LLM-ready format
- graphql_extraction_report.txt: This report
