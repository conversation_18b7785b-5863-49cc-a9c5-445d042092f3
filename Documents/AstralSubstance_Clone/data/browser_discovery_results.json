{"total_requests": 41, "api_endpoints": ["https://sentry.io/api/2704171/envelope/?sentry_key=fd8da8d0c41f4a66aec07fe2803d3d5f&sentry_version=7&sentry_client=sentry.javascript.browser%2F7.11.1", "https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js", "https://cdn.dynamicyield.com/api/8790624/api_static.js", "https://www.keen.com/api/users/current", "https://cdnjs.cloudflare.com/ajax/libs/lazysizes/4.1.5/lazysizes.min.js", "https://www.keen.com/api/advisors/********/customer-lists", "https://www.keen.com/api/graphqlv0", "https://www.keen.com/api/experiments/buckets", "https://www.keen.com/api/users/********/notifications", "https://www.keen.com/api/availability-channels", "https://www.keen.com/api/users/********/listings", "https://www.keen.com/api/session/attributes?names=Features.AlertMe.Enabled", "https://www.keen.com/api/graphql2", "https://analytics.tiktok.com/api/v2/pixel", "https://cdn.dynamicyield.com/api/8790624/api_dynamic.js", "https://analytics.tiktok.com/api/v2/pixel/act", "https://www.keen.com/api/v2/listings?&limit=6&categoryId=195&pageNumber=1&callAvailability=2&chatAvailability=2&sortType=33&optedIntoOffer=true"], "customer_endpoints": ["https://www.keen.com/api/advisors/********/customer-lists"], "all_requests": [{"url": "https://cdn.dynamicyield.com/api/8790624/api_static.js", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://cdn.dynamicyield.com/api/8790624/api_dynamic.js", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://www.keen.com/api/v2/listings?&limit=6&categoryId=195&pageNumber=1&callAvailability=2&chatAvailability=2&sortType=33&optedIntoOffer=true", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://tr.snapchat.com/config/com/e0555508-c06e-49b8-97b9-d90681d48600.json?v=3.46.2-**********", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://www.keen.com/api/availability-channels", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel/act", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://sentry.io/api/2704171/envelope/?sentry_key=fd8da8d0c41f4a66aec07fe2803d3d5f&sentry_version=7&sentry_client=sentry.javascript.browser%2F7.11.1", "status": 403, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://www.keen.com/api/experiments/buckets", "status": 201, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://www.keen.com/api/users/current", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://www.keen.com/api/advisors/********/customer-lists", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://www.keen.com/api/session/attributes?names=Features.AlertMe.Enabled", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://www.keen.com/api/users/********/notifications", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel/act", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://www.keen.com/api/graphqlv0", "status": 200, "method": "GET", "page": "/app/myaccount/customers", "timestamp": *************}, {"url": "https://sentry.io/api/2704171/envelope/?sentry_key=fd8da8d0c41f4a66aec07fe2803d3d5f&sentry_version=7&sentry_client=sentry.javascript.browser%2F7.11.1", "status": 403, "method": "GET", "page": "/app/myaccount/customer-list", "timestamp": *************}, {"url": "https://www.keen.com/api/experiments/buckets", "status": 201, "method": "GET", "page": "/app/myaccount/customer-list", "timestamp": *************}, {"url": "https://www.keen.com/api/users/current", "status": 200, "method": "GET", "page": "/app/myaccount/customer-list", "timestamp": *************}, {"url": "https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js", "status": 200, "method": "GET", "page": "/app/myaccount/customer-list", "timestamp": *************}, {"url": "https://cdnjs.cloudflare.com/ajax/libs/lazysizes/4.1.5/lazysizes.min.js", "status": 200, "method": "GET", "page": "/app/myaccount/customer-list", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel", "status": 200, "method": "GET", "page": "/app/myaccount/customer-list", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel/act", "status": 200, "method": "GET", "page": "/app/myaccount/customer-list", "timestamp": *************}, {"url": "https://sentry.io/api/2704171/envelope/?sentry_key=fd8da8d0c41f4a66aec07fe2803d3d5f&sentry_version=7&sentry_client=sentry.javascript.browser%2F7.11.1", "status": 403, "method": "GET", "page": "/app/myaccount/conversations", "timestamp": *************}, {"url": "https://www.keen.com/api/experiments/buckets", "status": 201, "method": "GET", "page": "/app/myaccount/conversations", "timestamp": *************}, {"url": "https://www.keen.com/api/users/current", "status": 200, "method": "GET", "page": "/app/myaccount/conversations", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel", "status": 200, "method": "GET", "page": "/app/myaccount/conversations", "timestamp": *************}, {"url": "https://cdnjs.cloudflare.com/ajax/libs/lazysizes/4.1.5/lazysizes.min.js", "status": 200, "method": "GET", "page": "/app/myaccount/conversations", "timestamp": *************}, {"url": "https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js", "status": 200, "method": "GET", "page": "/app/myaccount/conversations", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel", "status": 200, "method": "GET", "page": "/app/myaccount/conversations", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel/act", "status": 200, "method": "GET", "page": "/app/myaccount/conversations", "timestamp": *************}, {"url": "https://sentry.io/api/2704171/envelope/?sentry_key=fd8da8d0c41f4a66aec07fe2803d3d5f&sentry_version=7&sentry_client=sentry.javascript.browser%2F7.11.1", "status": 403, "method": "GET", "page": "/app/myaccount/feedback", "timestamp": *************}, {"url": "https://www.keen.com/api/experiments/buckets", "status": 201, "method": "GET", "page": "/app/myaccount/feedback", "timestamp": *************}, {"url": "https://www.keen.com/api/users/current", "status": 200, "method": "GET", "page": "/app/myaccount/feedback", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel", "status": 200, "method": "GET", "page": "/app/myaccount/feedback", "timestamp": *************}, {"url": "https://www.keen.com/api/users/********/listings", "status": 200, "method": "GET", "page": "/app/myaccount/feedback", "timestamp": *************}, {"url": "https://www.keen.com/api/session/attributes?names=Features.AlertMe.Enabled", "status": 200, "method": "GET", "page": "/app/myaccount/feedback", "timestamp": *************}, {"url": "https://analytics.tiktok.com/api/v2/pixel/act", "status": 200, "method": "GET", "page": "/app/myaccount/feedback", "timestamp": *************}, {"url": "https://www.keen.com/api/users/********/notifications", "status": 200, "method": "GET", "page": "/app/myaccount/feedback", "timestamp": *************}, {"url": "https://www.keen.com/api/graphql2", "status": 200, "method": "GET", "page": "/app/myaccount/feedback", "timestamp": *************}, {"url": "https://www.keen.com/api/graphql2", "status": 200, "method": "GET", "page": "/app/myaccount/feedback", "timestamp": *************}]}