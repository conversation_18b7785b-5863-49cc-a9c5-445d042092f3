{"name": "Query", "fields": [{"name": "advisor", "description": null, "type": {"name": "Advisor", "kind": "OBJECT", "ofType": null}, "args": [{"name": "id", "type": {"name": "ID", "kind": "SCALAR", "ofType": null}}, {"name": "userName", "type": {"name": "String", "kind": "SCALAR", "ofType": null}}]}, {"name": "countries", "description": null, "type": {"name": "CountryConnection", "kind": "OBJECT", "ofType": null}, "args": [{"name": "after", "type": {"name": "String", "kind": "SCALAR", "ofType": null}}, {"name": "before", "type": {"name": "String", "kind": "SCALAR", "ofType": null}}, {"name": "first", "type": {"name": "Int", "kind": "SCALAR", "ofType": null}}, {"name": "last", "type": {"name": "Int", "kind": "SCALAR", "ofType": null}}]}, {"name": "country", "description": null, "type": {"name": "Country", "kind": "OBJECT", "ofType": null}, "args": [{"name": "countryCode", "type": {"name": "String", "kind": "SCALAR", "ofType": null}}]}, {"name": "currentUser", "description": "Returns the current `User` or `null` if not authenticated\nor no `User` has context.", "type": {"name": "User", "kind": "INTERFACE", "ofType": null}, "args": []}, {"name": "listing", "description": null, "type": {"name": "Listing", "kind": "OBJECT", "ofType": null}, "args": [{"name": "id", "type": {"name": null, "kind": "NON_NULL", "ofType": {"name": "ID", "kind": "SCALAR"}}}]}, {"name": "listings", "description": null, "type": {"name": "ListingsConnection", "kind": "OBJECT", "ofType": null}, "args": []}, {"name": "supportedCurrencies", "description": null, "type": {"name": null, "kind": "LIST", "ofType": {"name": "<PERSON><PERSON><PERSON><PERSON>", "kind": "OBJECT"}}, "args": []}, {"name": "supportedLanguages", "description": null, "type": {"name": null, "kind": "NON_NULL", "ofType": {"name": null, "kind": "LIST"}}, "args": []}, {"name": "timeZones", "description": null, "type": {"name": null, "kind": "NON_NULL", "ofType": {"name": null, "kind": "LIST"}}, "args": []}]}