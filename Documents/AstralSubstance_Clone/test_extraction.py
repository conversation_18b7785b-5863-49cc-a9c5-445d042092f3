#!/usr/bin/env python3
"""
Test Keen.com data extraction using the working API
"""

import sys
from pathlib import Path
import requests
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON>oa<PERSON>

def test_keen_extraction():
    """Test Keen.com data extraction with the working API"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    
    # Add all cookies from config
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Setup headers (from the curl request)
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Uid': '',
        'X-Domain-ID': '1',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://www.keen.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.keen.com/app/myaccount/feedback',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Priority': 'u=0',
        'TE': 'trailers'
    }
    
    # The working query
    query_data = {
        "query": """query($advisorId:Int $listingId:Int $isHighlighted:Boolean $sortBy:[Sort] $pageSize:Int $pageNumber:Int){
            ratingsAndReviews(advisorId:$advisorId listingId:$listingId isHighlighted:$isHighlighted sortBy:$sortBy pageSize:$pageSize pageNumber:$pageNumber){
                averageRating 
                totalEdges 
                edges{
                    node{
                        id 
                        rating 
                        review 
                        date 
                        isHighlighted 
                        advisorStrengths 
                        target{
                            type 
                            source{
                                id 
                                sessionExists 
                                masterTransactionId 
                                customer{
                                    id 
                                    userName 
                                    nickname
                                }
                                listing{
                                    id 
                                    category{
                                        id 
                                        name
                                    }
                                    advisor{
                                        id 
                                        userName
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }""",
        "variables": {
            "listingId": "12471990",
            "advisorId": 56392386,
            "isHighlighted": False,
            "pageNumber": 1,
            "pageSize": "10",  # Get 10 reviews
            "sortBy": [{"field": "date", "order": "DESCENDING"}]
        }
    }
    
    # Make the request
    url = f"{keen_config['base_url']}{keen_config['graphql_endpoint']}"
    print(f"Extracting customer data from: {url}")
    
    try:
        response = session.post(url, headers=headers, json=query_data, timeout=30)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Data extraction successful!")
            
            if 'data' in data and 'ratingsAndReviews' in data['data']:
                reviews = data['data']['ratingsAndReviews']
                print(f"Found {reviews.get('totalEdges', 0)} total reviews")
                
                edges = reviews.get('edges', [])
                print(f"Processing {len(edges)} reviews from this page")
                
                customers_found = {}
                
                for i, edge in enumerate(edges):
                    review_node = edge.get('node', {})
                    target = review_node.get('target', {})
                    source = target.get('source', {})
                    customer_data = source.get('customer', {})
                    
                    if customer_data and customer_data.get('id'):
                        customer_id = customer_data['id']
                        customer_name = customer_data.get('userName', 'Unknown')
                        customer_nickname = customer_data.get('nickname', '')
                        
                        if customer_id not in customers_found:
                            customers_found[customer_id] = {
                                'id': customer_id,
                                'userName': customer_name,
                                'nickname': customer_nickname,
                                'reviews': []
                            }
                        
                        # Add review info
                        review_info = {
                            'id': review_node.get('id'),
                            'rating': review_node.get('rating'),
                            'review': review_node.get('review', '')[:100] + '...' if review_node.get('review') else '',
                            'date': review_node.get('date'),
                            'type': target.get('type')
                        }
                        customers_found[customer_id]['reviews'].append(review_info)
                
                print(f"\n🎉 Found {len(customers_found)} unique customers:")
                for customer_id, customer in customers_found.items():
                    print(f"  - {customer['userName']} ({customer['nickname']}) - ID: {customer_id}")
                    print(f"    Reviews: {len(customer['reviews'])}")
                    for review in customer['reviews'][:2]:  # Show first 2 reviews
                        print(f"      * {review['rating']}/5 - {review['review']}")
                    print()
                
                return True
            else:
                print("❌ Unexpected response structure")
                return False
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_keen_extraction()
    sys.exit(0 if success else 1)
