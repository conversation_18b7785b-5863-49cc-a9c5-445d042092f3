#!/usr/bin/env python3
"""
Query the main GraphQL endpoint to get ALL customer data
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import <PERSON>fig<PERSON><PERSON><PERSON>

def query_main_graphql():
    """Query the main GraphQL endpoint for comprehensive customer data"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Uid': '',
        'X-Domain-ID': '1',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://www.keen.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    base_url = keen_config['base_url']
    graphql_url = f"{base_url}/api/graphql"
    
    print("🔍 QUERYING MAIN GRAPHQL ENDPOINT")
    print("=" * 60)
    
    # Step 1: Get the full schema
    print("\n📊 Step 1: Getting full GraphQL schema...")
    schema_info = get_full_schema(session, headers, graphql_url)
    
    # Step 2: Query customer types
    print("\n👥 Step 2: Querying customer data...")
    customer_data = query_customer_data(session, headers, graphql_url)
    
    # Step 3: Try to get advisor customer list
    print("\n📋 Step 3: Getting advisor customer list...")
    advisor_customers = query_advisor_customers(session, headers, graphql_url)
    
    # Step 4: Test pagination on customer data
    print("\n📄 Step 4: Testing customer data pagination...")
    paginated_customers = test_customer_pagination(session, headers, graphql_url)

def get_full_schema(session, headers, graphql_url):
    """Get the full GraphQL schema to understand available queries"""
    
    introspection_query = {
        "query": """
        query IntrospectionQuery {
            __schema {
                queryType { name }
                mutationType { name }
                subscriptionType { name }
                types {
                    ...FullType
                }
            }
        }
        
        fragment FullType on __Type {
            kind
            name
            description
            fields(includeDeprecated: true) {
                name
                description
                args {
                    ...InputValue
                }
                type {
                    ...TypeRef
                }
                isDeprecated
                deprecationReason
            }
            inputFields {
                ...InputValue
            }
            interfaces {
                ...TypeRef
            }
            enumValues(includeDeprecated: true) {
                name
                description
                isDeprecated
                deprecationReason
            }
            possibleTypes {
                ...TypeRef
            }
        }
        
        fragment InputValue on __InputValue {
            name
            description
            type { ...TypeRef }
            defaultValue
        }
        
        fragment TypeRef on __Type {
            kind
            name
            ofType {
                kind
                name
                ofType {
                    kind
                    name
                    ofType {
                        kind
                        name
                        ofType {
                            kind
                            name
                            ofType {
                                kind
                                name
                                ofType {
                                    kind
                                    name
                                    ofType {
                                        kind
                                        name
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=introspection_query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            schema = data['data']['__schema']
            
            print(f"  ✅ Schema retrieved successfully")
            print(f"  📊 Query type: {schema['queryType']['name']}")
            print(f"  🔄 Mutation type: {schema.get('mutationType', {}).get('name', 'None')}")
            print(f"  📡 Subscription type: {schema.get('subscriptionType', {}).get('name', 'None')}")
            print(f"  🏷️  Total types: {len(schema['types'])}")
            
            # Find customer-related types
            customer_types = []
            query_fields = []
            
            for type_def in schema['types']:
                if type_def['name'] and 'customer' in type_def['name'].lower():
                    customer_types.append(type_def['name'])
                
                # Look for query fields
                if type_def['name'] == schema['queryType']['name'] and type_def['fields']:
                    for field in type_def['fields']:
                        if 'customer' in field['name'].lower():
                            query_fields.append(field['name'])
            
            print(f"  👥 Customer types: {customer_types}")
            print(f"  🔍 Customer query fields: {query_fields}")
            
            # Save schema for analysis
            with open('data/graphql_schema.json', 'w') as f:
                json.dump(data, f, indent=2)
            print(f"  💾 Full schema saved to: data/graphql_schema.json")
            
            return {'customer_types': customer_types, 'query_fields': query_fields}
            
        else:
            print(f"  ❌ Schema query failed: {response.status_code}")
            return {}
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {}

def query_customer_data(session, headers, graphql_url):
    """Query customer data using discovered fields"""
    
    # Try common customer queries
    customer_queries = [
        {
            "name": "advisorCustomers",
            "query": """
            query {
                advisorCustomers {
                    id
                    name
                    email
                    phone
                    totalSessions
                    totalSpent
                    lastContact
                    createdAt
                }
            }
            """
        },
        {
            "name": "customers",
            "query": """
            query {
                customers {
                    id
                    name
                    email
                    totalSessions
                    totalSpent
                }
            }
            """
        },
        {
            "name": "advisorCustomerList",
            "query": """
            query {
                advisorCustomerList {
                    customers {
                        id
                        name
                        email
                        phone
                        totalSessions
                        totalSpent
                        lastContact
                    }
                }
            }
            """
        }
    ]
    
    for query_info in customer_queries:
        print(f"  Testing query: {query_info['name']}")
        
        try:
            query_data = {
                "query": query_info['query'],
                "variables": {}
            }
            
            response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"    ❌ GraphQL errors: {data['errors']}")
                elif 'data' in data:
                    result_data = data['data']
                    print(f"    ✅ Success! Data keys: {list(result_data.keys())}")
                    
                    # Check for customer data
                    for key, value in result_data.items():
                        if value:
                            if isinstance(value, list):
                                print(f"      📊 {key}: {len(value)} items")
                                if value and isinstance(value[0], dict):
                                    print(f"        Sample keys: {list(value[0].keys())}")
                            elif isinstance(value, dict):
                                print(f"      📊 {key}: {list(value.keys())}")
                            else:
                                print(f"      📊 {key}: {type(value)}")
                    
                    return data
                else:
                    print(f"    ❌ No data in response")
            else:
                print(f"    ❌ HTTP error: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(1)
    
    return {}

def query_advisor_customers(session, headers, graphql_url):
    """Query advisor customers with pagination"""
    
    # Try advisor customer queries with pagination
    advisor_queries = [
        {
            "name": "advisorCustomers with pagination",
            "query": """
            query($first: Int, $after: String) {
                advisorCustomers(first: $first, after: $after) {
                    edges {
                        node {
                            id
                            name
                            email
                            phone
                            totalSessions
                            totalSpent
                            lastContact
                            createdAt
                        }
                        cursor
                    }
                    pageInfo {
                        hasNextPage
                        hasPreviousPage
                        startCursor
                        endCursor
                    }
                    totalCount
                }
            }
            """,
            "variables": {"first": 50}
        },
        {
            "name": "advisorCustomerList with limit",
            "query": """
            query($limit: Int, $offset: Int) {
                advisorCustomerList(limit: $limit, offset: $offset) {
                    customers {
                        id
                        name
                        email
                        phone
                        totalSessions
                        totalSpent
                        lastContact
                    }
                    totalCount
                    hasMore
                }
            }
            """,
            "variables": {"limit": 50, "offset": 0}
        }
    ]
    
    for query_info in advisor_queries:
        print(f"  Testing: {query_info['name']}")
        
        try:
            query_data = {
                "query": query_info['query'],
                "variables": query_info['variables']
            }
            
            response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"    ❌ GraphQL errors: {data['errors']}")
                elif 'data' in data:
                    result_data = data['data']
                    print(f"    ✅ Success! Data keys: {list(result_data.keys())}")
                    
                    # Analyze the response structure
                    for key, value in result_data.items():
                        if value:
                            print(f"      📊 {key}: {type(value)}")
                            if isinstance(value, dict):
                                if 'edges' in value:
                                    edges = value['edges']
                                    print(f"        📄 {len(edges)} edges found")
                                    if edges:
                                        sample_node = edges[0].get('node', {})
                                        print(f"        👤 Sample customer: {list(sample_node.keys())}")
                                elif 'customers' in value:
                                    customers = value['customers']
                                    print(f"        👥 {len(customers)} customers found")
                                    if customers:
                                        print(f"        👤 Sample customer: {list(customers[0].keys())}")
                    
                    return data
                else:
                    print(f"    ❌ No data in response")
            else:
                print(f"    ❌ HTTP error: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(1)
    
    return {}

def test_customer_pagination(session, headers, graphql_url):
    """Test pagination to get ALL customer data"""
    
    print(f"  🎯 Testing pagination to get ALL customers...")
    
    # Use a simple query that's likely to work
    pagination_query = {
        "query": """
        query($first: Int, $after: String) {
            viewer {
                advisorCustomers(first: $first, after: $after) {
                    edges {
                        node {
                            id
                            name
                            email
                        }
                        cursor
                    }
                    pageInfo {
                        hasNextPage
                        endCursor
                    }
                    totalCount
                }
            }
        }
        """,
        "variables": {"first": 10}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=pagination_query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"    ❌ GraphQL errors: {data['errors']}")
            elif 'data' in data:
                print(f"    ✅ Pagination query successful!")
                return data
            else:
                print(f"    ❌ No data in response")
        else:
            print(f"    ❌ HTTP error: {response.status_code}")
            
    except Exception as e:
        print(f"    ❌ Error: {e}")
    
    return {}

if __name__ == "__main__":
    query_main_graphql()
