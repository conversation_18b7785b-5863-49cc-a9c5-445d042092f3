#!/usr/bin/env python3
"""
Extract customer data using minimal working GraphQL query
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON>oa<PERSON>

def extract_customers_minimal():
    """Extract customer data using minimal working query"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
    }
    
    base_url = keen_config['base_url']
    graphql_url = f"{base_url}/api/graphql"
    
    print("🚀 EXTRACTING CUSTOMER DATA (MINIMAL)")
    print("=" * 60)
    
    # Start with the most basic query
    print("\n🔍 Step 1: Testing basic customer query...")
    basic_customers = test_basic_customer_query(session, headers, graphql_url)
    
    if basic_customers:
        print("\n📊 Step 2: Expanding customer data...")
        expanded_customers = expand_customer_data(session, headers, graphql_url)
        
        print("\n🔄 Step 3: Getting all customers with pagination...")
        all_customers = get_all_customers_paginated(session, headers, graphql_url)
        
        print("\n📋 Step 4: Getting customer lists...")
        customer_lists = get_customer_lists(session, headers, graphql_url)
        
        print("\n📊 Step 5: Final summary...")
        final_summary(all_customers, customer_lists)

def test_basic_customer_query(session, headers, graphql_url):
    """Test the most basic customer query"""
    
    query = {
        "query": """
        query {
            currentUser {
                ... on Advisor {
                    customers {
                        edges {
                            node {
                                id
                                customerId
                                userName
                                nickname
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Error: {data['errors'][0]['message']}")
                return None
            elif 'data' in data and data['data']['currentUser'] and 'customers' in data['data']['currentUser']:
                customers = data['data']['currentUser']['customers']
                edges = customers.get('edges', [])
                
                print(f"  ✅ SUCCESS! Found {len(edges)} customers")
                
                if edges:
                    sample = edges[0]['node']
                    print(f"  👤 Sample customer: {sample}")
                
                return edges
            else:
                print(f"  ❌ No customer data")
                return None
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return None

def expand_customer_data(session, headers, graphql_url):
    """Expand customer query to get more fields"""
    
    query = {
        "query": """
        query {
            currentUser {
                ... on Advisor {
                    customers {
                        edges {
                            node {
                                id
                                customerId
                                userName
                                nickname
                                emailAddress
                                lastContactDate
                                since
                                pastEarnings {
                                    amount
                                }
                                ratingsAverage
                                comments
                                blockedAt
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Error: {data['errors'][0]['message']}")
                # Try without the problematic fields
                return try_without_problematic_fields(session, headers, graphql_url)
            elif 'data' in data and data['data']['currentUser'] and 'customers' in data['data']['currentUser']:
                customers = data['data']['currentUser']['customers']
                edges = customers.get('edges', [])
                
                print(f"  ✅ Expanded query successful! {len(edges)} customers with full data")
                
                if edges:
                    sample = edges[0]['node']
                    print(f"  👤 Sample expanded customer:")
                    for key, value in sample.items():
                        if value is not None:
                            print(f"    {key}: {value}")
                
                return edges
            else:
                print(f"  ❌ No customer data")
                return None
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return None

def try_without_problematic_fields(session, headers, graphql_url):
    """Try query without fields that might be causing issues"""
    
    query = {
        "query": """
        query {
            currentUser {
                ... on Advisor {
                    customers {
                        edges {
                            node {
                                id
                                customerId
                                userName
                                nickname
                                emailAddress
                                lastContactDate
                                since
                                ratingsAverage
                                comments
                                blockedAt
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Still error: {data['errors'][0]['message']}")
                return None
            elif 'data' in data and data['data']['currentUser'] and 'customers' in data['data']['currentUser']:
                customers = data['data']['currentUser']['customers']
                edges = customers.get('edges', [])
                
                print(f"  ✅ Query without problematic fields successful! {len(edges)} customers")
                return edges
            else:
                print(f"  ❌ No customer data")
                return None
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return None

def get_all_customers_paginated(session, headers, graphql_url):
    """Get all customers using pagination"""
    
    all_customers = []
    
    # Try different pagination sizes
    for page_size in [50, 100, 200]:
        print(f"  🔄 Trying pagination with page size {page_size}...")
        
        query = {
            "query": """
            query($first: Int) {
                currentUser {
                    ... on Advisor {
                        customers(first: $first) {
                            edges {
                                node {
                                    id
                                    customerId
                                    userName
                                    nickname
                                    emailAddress
                                    lastContactDate
                                    since
                                    ratingsAverage
                                    comments
                                    blockedAt
                                }
                                cursor
                            }
                            pageInfo {
                                hasNextPage
                                endCursor
                            }
                        }
                    }
                }
            }
            """,
            "variables": {"first": page_size}
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"    ❌ Error with page size {page_size}: {data['errors'][0]['message']}")
                    continue
                elif 'data' in data and data['data']['currentUser'] and 'customers' in data['data']['currentUser']:
                    customers = data['data']['currentUser']['customers']
                    edges = customers.get('edges', [])
                    page_info = customers.get('pageInfo', {})
                    
                    print(f"    ✅ Got {len(edges)} customers")
                    print(f"    📄 Has next page: {page_info.get('hasNextPage', False)}")
                    
                    all_customers = edges.copy()
                    
                    # Get additional pages if available
                    if page_info.get('hasNextPage'):
                        print(f"    🔄 Getting additional pages...")
                        additional_pages = get_additional_customer_pages(session, headers, graphql_url, page_info.get('endCursor'), page_size)
                        all_customers.extend(additional_pages)
                    
                    print(f"    🎉 Total customers with pagination: {len(all_customers)}")
                    break
                else:
                    print(f"    ❌ No customer data with page size {page_size}")
                    
        except Exception as e:
            print(f"    ❌ Error with page size {page_size}: {e}")
    
    # Save all customers
    if all_customers:
        customer_data = {
            'totalExtracted': len(all_customers),
            'customers': [edge['node'] for edge in all_customers]
        }
        
        with open('data/all_customers_minimal.json', 'w') as f:
            json.dump(customer_data, f, indent=2)
        print(f"  💾 All customers saved to: data/all_customers_minimal.json")
    
    return all_customers

def get_additional_customer_pages(session, headers, graphql_url, start_cursor, page_size):
    """Get additional pages of customers"""
    
    additional_customers = []
    current_cursor = start_cursor
    page_num = 2
    
    while current_cursor and page_num <= 20:  # Limit to 20 pages
        print(f"      📄 Page {page_num}...")
        
        query = {
            "query": """
            query($first: Int, $after: String) {
                currentUser {
                    ... on Advisor {
                        customers(first: $first, after: $after) {
                            edges {
                                node {
                                    id
                                    customerId
                                    userName
                                    nickname
                                    emailAddress
                                    lastContactDate
                                    since
                                    ratingsAverage
                                    comments
                                    blockedAt
                                }
                                cursor
                            }
                            pageInfo {
                                hasNextPage
                                endCursor
                            }
                        }
                    }
                }
            }
            """,
            "variables": {"first": page_size, "after": current_cursor}
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"        ❌ Error on page {page_num}")
                    break
                elif 'data' in data and data['data']['currentUser'] and 'customers' in data['data']['currentUser']:
                    customers = data['data']['currentUser']['customers']
                    edges = customers.get('edges', [])
                    page_info = customers.get('pageInfo', {})
                    
                    print(f"        ✅ {len(edges)} customers")
                    additional_customers.extend(edges)
                    
                    if page_info.get('hasNextPage'):
                        current_cursor = page_info.get('endCursor')
                        page_num += 1
                    else:
                        break
                else:
                    break
            else:
                break
                
        except Exception as e:
            print(f"        ❌ Error on page {page_num}: {e}")
            break
        
        time.sleep(0.5)
    
    return additional_customers

def get_customer_lists(session, headers, graphql_url):
    """Get customer lists"""
    
    query = {
        "query": """
        query {
            currentUser {
                ... on Advisor {
                    customerLists {
                        edges {
                            node {
                                id
                                name
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Error: {data['errors'][0]['message']}")
                return []
            elif 'data' in data and data['data']['currentUser'] and 'customerLists' in data['data']['currentUser']:
                customer_lists = data['data']['currentUser']['customerLists']
                edges = customer_lists.get('edges', [])
                
                print(f"  ✅ Found {len(edges)} customer lists")
                
                for edge in edges:
                    list_node = edge['node']
                    print(f"    📋 {list_node.get('name', 'Unnamed')} (ID: {list_node.get('id')})")
                
                return edges
            else:
                print(f"  ❌ No customer lists")
                return []
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return []

def final_summary(all_customers, customer_lists):
    """Final summary of extraction"""
    
    print(f"🎯 FINAL EXTRACTION SUMMARY")
    print("=" * 50)
    
    if all_customers:
        customers = [edge['node'] for edge in all_customers]
        
        print(f"👥 TOTAL CUSTOMERS EXTRACTED: {len(customers)}")
        
        # Data analysis
        with_email = sum(1 for c in customers if c.get('emailAddress'))
        with_nickname = sum(1 for c in customers if c.get('nickname'))
        with_ratings = sum(1 for c in customers if c.get('ratingsAverage'))
        blocked = sum(1 for c in customers if c.get('blockedAt'))
        
        print(f"\n📊 DATA COMPLETENESS:")
        print(f"  Email addresses: {with_email}/{len(customers)} ({with_email/len(customers)*100:.1f}%)")
        print(f"  Nicknames: {with_nickname}/{len(customers)} ({with_nickname/len(customers)*100:.1f}%)")
        print(f"  Ratings: {with_ratings}/{len(customers)} ({with_ratings/len(customers)*100:.1f}%)")
        print(f"  Blocked customers: {blocked}/{len(customers)} ({blocked/len(customers)*100:.1f}%)")
        
        # Show some sample customers
        print(f"\n👤 SAMPLE CUSTOMERS:")
        for i, customer in enumerate(customers[:5], 1):
            name = customer.get('userName', 'Unknown')
            email = customer.get('emailAddress', 'No email')
            nickname = customer.get('nickname', 'No nickname')
            print(f"  {i}. {name} ({nickname}) - {email}")
    
    if customer_lists:
        print(f"\n📋 CUSTOMER LISTS: {len(customer_lists)}")
    
    print(f"\n🎉 EXTRACTION COMPLETE!")
    print(f"✅ Ready for Google Voice correlation and transcription!")

if __name__ == "__main__":
    extract_customers_minimal()
