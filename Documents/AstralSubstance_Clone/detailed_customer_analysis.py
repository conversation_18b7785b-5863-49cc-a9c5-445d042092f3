#!/usr/bin/env python3
"""
Detailed analysis of customer elements found on the page
"""

import sys
from pathlib import Path
import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

def detailed_customer_analysis():
    """Detailed analysis of customer elements"""
    
    print("🔍 DETAILED CUSTOMER ANALYSIS")
    print("=" * 60)
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup browser
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = None
    
    try:
        # Start browser
        print("\n🌐 Starting browser...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Set authentication
        setup_authentication(driver, keen_config)
        
        # Navigate to customer page
        customer_url = f"{keen_config['base_url']}/app/myaccount/customers"
        driver.get(customer_url)
        
        # Wait for page load
        wait = WebDriverWait(driver, 15)
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        time.sleep(5)
        
        print("✅ Customer page loaded!")
        
        # Analyze all potential customer elements
        print("\n📊 ANALYZING ALL POTENTIAL CUSTOMER ELEMENTS...")
        analyze_all_customer_elements(driver)
        
        # Save page structure for analysis
        print("\n💾 SAVING PAGE STRUCTURE...")
        save_page_structure(driver)
        
        print("\n⏸️  KEEPING BROWSER OPEN FOR MANUAL INSPECTION...")
        print("🔍 You can now manually inspect the customer interface")
        print("📋 Look for:")
        print("  - How customers are displayed (table, list, cards)")
        print("  - What happens when you click on a customer")
        print("  - How conversations/sessions are shown")
        print("  - Any pagination or 'load more' functionality")
        print("⌨️  Press Enter when done...")
        
        input("Press Enter to continue...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if driver:
            driver.quit()

def setup_authentication(driver, keen_config):
    """Setup authentication cookies"""
    
    base_url = keen_config['base_url']
    driver.get(base_url)
    time.sleep(2)
    
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            try:
                driver.add_cookie({
                    'name': name,
                    'value': value,
                    'domain': '.keen.com'
                })
            except Exception as e:
                print(f"  ⚠️  Failed to add cookie {name}: {e}")

def analyze_all_customer_elements(driver):
    """Analyze all potential customer elements without filtering"""
    
    # Get all .list-item elements (48 found)
    print("\n📋 ANALYZING .list-item ELEMENTS:")
    list_items = driver.find_elements(By.CSS_SELECTOR, '.list-item')
    print(f"  Found {len(list_items)} .list-item elements")
    
    for i, item in enumerate(list_items[:10]):  # Analyze first 10
        analyze_element_detailed(item, f"list-item-{i+1}")
    
    # Get all customer link elements (27 found)
    print("\n🔗 ANALYZING CUSTOMER LINK ELEMENTS:")
    customer_links = driver.find_elements(By.CSS_SELECTOR, 'a[href*="customer"]')
    print(f"  Found {len(customer_links)} customer link elements")
    
    for i, link in enumerate(customer_links[:10]):  # Analyze first 10
        analyze_element_detailed(link, f"customer-link-{i+1}")
    
    # Look for table rows
    print("\n📊 ANALYZING TABLE ELEMENTS:")
    table_rows = driver.find_elements(By.CSS_SELECTOR, 'tbody tr')
    print(f"  Found {len(table_rows)} table rows")
    
    for i, row in enumerate(table_rows[:10]):  # Analyze first 10
        analyze_element_detailed(row, f"table-row-{i+1}")
    
    # Look for any clickable elements with data attributes
    print("\n🖱️  ANALYZING CLICKABLE DATA ELEMENTS:")
    clickable_data = driver.find_elements(By.CSS_SELECTOR, '[data-id], [data-customer-id], [data-user-id]')
    print(f"  Found {len(clickable_data)} elements with data attributes")
    
    for i, elem in enumerate(clickable_data[:10]):  # Analyze first 10
        analyze_element_detailed(elem, f"data-element-{i+1}")

def analyze_element_detailed(element, element_id):
    """Detailed analysis of a single element"""
    
    print(f"\n  🔍 {element_id}:")
    
    try:
        # Basic info
        tag_name = element.tag_name
        classes = element.get_attribute('class') or 'no-class'
        print(f"    📋 <{tag_name}> class='{classes}'")
        
        # Text content
        text = element.text.strip()
        if text:
            # Show first 200 chars, preserving line breaks
            lines = text.split('\n')
            display_lines = []
            char_count = 0
            
            for line in lines:
                line = line.strip()
                if line:
                    if char_count + len(line) > 200:
                        break
                    display_lines.append(line)
                    char_count += len(line)
            
            display_text = '\n      '.join(display_lines)
            print(f"    📄 Text:\n      {display_text}")
            
            if char_count < len(text):
                print(f"    📄 ... (truncated, total length: {len(text)})")
        
        # Important attributes
        important_attrs = [
            'id', 'data-id', 'data-customer-id', 'data-user-id',
            'href', 'onclick', 'data-ember-action'
        ]
        
        for attr in important_attrs:
            value = element.get_attribute(attr)
            if value:
                display_value = value[:100] + ('...' if len(value) > 100 else '')
                print(f"    🔗 {attr}: {display_value}")
        
        # Check if clickable
        is_clickable = (
            element.get_attribute('onclick') or
            element.get_attribute('href') or
            element.tag_name.lower() in ['a', 'button'] or
            'clickable' in classes.lower() or
            'cursor: pointer' in element.get_attribute('style', '')
        )
        
        if is_clickable:
            print(f"    🖱️  CLICKABLE")
        
        # Look for nested customer info
        nested_info = extract_nested_customer_info(element)
        if nested_info:
            print(f"    👤 Nested customer info:")
            for key, value in nested_info.items():
                print(f"      {key}: {value}")
        
    except Exception as e:
        print(f"    ❌ Error analyzing {element_id}: {e}")

def extract_nested_customer_info(element):
    """Extract potential customer information from nested elements"""
    
    info = {}
    
    try:
        # Look for email patterns
        email_elements = element.find_elements(By.CSS_SELECTOR, '[href^="mailto:"], .email, [data-email]')
        for email_elem in email_elements:
            email_text = email_elem.text or email_elem.get_attribute('href') or email_elem.get_attribute('data-email')
            if email_text and '@' in email_text:
                info['email'] = email_text.replace('mailto:', '')
                break
        
        # Look for name/username patterns
        name_elements = element.find_elements(By.CSS_SELECTOR, '.name, .username, .customer-name, h1, h2, h3')
        for name_elem in name_elements:
            name_text = name_elem.text.strip()
            if name_text and len(name_text) > 2 and len(name_text) < 50:
                info['name'] = name_text
                break
        
        # Look for ID patterns
        id_elements = element.find_elements(By.CSS_SELECTOR, '[data-id], [data-customer-id], [data-user-id]')
        for id_elem in id_elements:
            for attr in ['data-id', 'data-customer-id', 'data-user-id']:
                id_value = id_elem.get_attribute(attr)
                if id_value:
                    info['id'] = id_value
                    break
            if 'id' in info:
                break
        
        # Look for date patterns
        date_elements = element.find_elements(By.CSS_SELECTOR, '.date, .timestamp, [data-date]')
        for date_elem in date_elements:
            date_text = date_elem.text or date_elem.get_attribute('data-date')
            if date_text and ('/' in date_text or '-' in date_text):
                info['date'] = date_text
                break
        
        # Look for money/earnings patterns
        money_elements = element.find_elements(By.CSS_SELECTOR, '.money, .earnings, .amount')
        for money_elem in money_elements:
            money_text = money_elem.text.strip()
            if money_text and '$' in money_text:
                info['earnings'] = money_text
                break
        
    except:
        pass
    
    return info

def save_page_structure(driver):
    """Save page structure for offline analysis"""
    
    try:
        # Save page source
        with open('data/customer_page_source.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print("  💾 Page source saved to: data/customer_page_source.html")
        
        # Save page structure analysis
        structure_data = {
            'url': driver.current_url,
            'title': driver.title,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'elements_found': {}
        }
        
        # Count different element types
        selectors_to_count = [
            '.list-item',
            'a[href*="customer"]',
            'tbody tr',
            '[data-id]',
            '[data-customer-id]',
            '[data-user-id]',
            '.customer',
            '.user',
            'table',
            '.pagination',
            'button',
            'a'
        ]
        
        for selector in selectors_to_count:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                structure_data['elements_found'][selector] = len(elements)
            except:
                structure_data['elements_found'][selector] = 0
        
        with open('data/customer_page_structure.json', 'w') as f:
            json.dump(structure_data, f, indent=2)
        print("  💾 Page structure saved to: data/customer_page_structure.json")
        
    except Exception as e:
        print(f"  ❌ Error saving page structure: {e}")

if __name__ == "__main__":
    detailed_customer_analysis()
