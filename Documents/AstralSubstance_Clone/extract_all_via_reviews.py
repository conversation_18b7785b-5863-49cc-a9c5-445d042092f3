#!/usr/bin/env python3
"""
Extract ALL customer data via the working ratingsAndReviews endpoint
This approach uses the proven working API to get comprehensive customer data
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON><PERSON><PERSON>

def extract_all_via_reviews():
    """Extract ALL customer data via the working ratingsAndReviews API"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers from working curl request
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Uid': '',
        'X-Domain-ID': '1',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://www.keen.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.keen.com/app/myaccount/feedback',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Priority': 'u=0',
        'TE': 'trailers'
    }
    
    base_url = keen_config['base_url']
    graphql_url = f"{base_url}/api/graphql2"
    
    print("🚀 EXTRACTING ALL CUSTOMER DATA VIA REVIEWS")
    print("=" * 60)
    
    # Step 1: Get total count
    print("\n📊 Step 1: Getting total review count...")
    total_reviews = get_total_review_count(session, headers, graphql_url)
    
    if total_reviews > 0:
        # Step 2: Extract all customers from all reviews
        print(f"\n👥 Step 2: Extracting customers from all {total_reviews} reviews...")
        all_customers = extract_customers_from_all_reviews(session, headers, graphql_url, total_reviews)
        
        # Step 3: Analyze and save data
        print(f"\n📊 Step 3: Analyzing extracted customer data...")
        analyze_and_save_customers(all_customers)
    else:
        print("❌ No reviews found, cannot extract customer data")

def get_total_review_count(session, headers, graphql_url):
    """Get the total number of reviews available"""
    
    query_data = {
        "query": """query($advisorId:Int $listingId:Int){
            ratingsAndReviews(advisorId:$advisorId listingId:$listingId pageSize:1 pageNumber:1){
                totalEdges
                averageRating
            }
        }""",
        "variables": {
            "listingId": "12471990",
            "advisorId": 56392386
        }
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'data' in data and 'ratingsAndReviews' in data['data']:
                reviews = data['data']['ratingsAndReviews']
                total_edges = reviews.get('totalEdges', 0)
                avg_rating = reviews.get('averageRating', 0)
                
                print(f"  ✅ Total reviews: {total_edges}")
                print(f"  ⭐ Average rating: {avg_rating:.2f}")
                
                return total_edges
            else:
                print(f"  ❌ No review data in response")
                return 0
        else:
            print(f"  ❌ HTTP error: {response.status_code}")
            return 0
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return 0

def extract_customers_from_all_reviews(session, headers, graphql_url, total_reviews):
    """Extract customers from all reviews using pagination"""
    
    all_customers = {}  # Use dict to avoid duplicates
    page_size = 100  # Optimal page size from our testing
    total_pages = (total_reviews // page_size) + 1
    
    print(f"  📄 Plan: Extract from {total_pages} pages of {page_size} reviews each")
    
    for page_num in range(1, total_pages + 1):
        print(f"  📄 Processing page {page_num}/{total_pages}...")
        
        query_data = {
            "query": """query($advisorId:Int $listingId:Int $pageSize:Int $pageNumber:Int){
                ratingsAndReviews(advisorId:$advisorId listingId:$listingId pageSize:$pageSize pageNumber:$pageNumber){
                    edges{
                        node{
                            id
                            rating
                            review
                            date
                            isHighlighted
                            advisorStrengths
                            target{
                                type
                                source{
                                    id
                                    sessionExists
                                    masterTransactionId
                                    customer{
                                        id
                                        userName
                                        nickname
                                    }
                                    listing{
                                        id
                                        category{
                                            id
                                            name
                                        }
                                        advisor{
                                            id
                                            userName
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }""",
            "variables": {
                "listingId": "12471990",
                "advisorId": 56392386,
                "pageSize": str(page_size),
                "pageNumber": page_num
            }
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and 'ratingsAndReviews' in data['data']:
                    reviews = data['data']['ratingsAndReviews']
                    edges = reviews.get('edges', [])
                    
                    print(f"    ✅ Got {len(edges)} reviews")
                    
                    # Extract customers from this page
                    page_customers = extract_customers_from_page(edges)
                    
                    # Add to all customers (dict automatically handles duplicates)
                    for customer_id, customer_data in page_customers.items():
                        if customer_id in all_customers:
                            # Merge review data
                            all_customers[customer_id]['reviews'].extend(customer_data['reviews'])
                            all_customers[customer_id]['total_reviews'] += customer_data['total_reviews']
                        else:
                            all_customers[customer_id] = customer_data
                    
                    print(f"    👥 Found {len(page_customers)} unique customers on this page")
                    print(f"    📊 Total unique customers so far: {len(all_customers)}")
                    
                    # If we got fewer reviews than expected, we've reached the end
                    if len(edges) < page_size:
                        print(f"    ℹ️  Reached end of reviews (got {len(edges)} < {page_size})")
                        break
                        
                else:
                    print(f"    ❌ No review data on page {page_num}")
                    break
            else:
                print(f"    ❌ HTTP error on page {page_num}: {response.status_code}")
                break
                
        except Exception as e:
            print(f"    ❌ Error on page {page_num}: {e}")
            break
        
        # Rate limiting
        time.sleep(1)
    
    print(f"\n  🎉 Extraction complete!")
    print(f"  👥 Total unique customers extracted: {len(all_customers)}")
    
    return all_customers

def extract_customers_from_page(edges):
    """Extract customer data from a page of reviews"""
    
    customers = {}
    
    for edge in edges:
        review_node = edge.get('node', {})
        target = review_node.get('target', {})
        source = target.get('source', {})
        customer_data = source.get('customer', {})
        
        if customer_data and customer_data.get('id'):
            customer_id = customer_data['id']
            
            # Create comprehensive customer record
            customer_record = {
                'id': customer_id,
                'userName': customer_data.get('userName'),
                'nickname': customer_data.get('nickname'),
                'total_reviews': 1,
                'reviews': [{
                    'review_id': review_node.get('id'),
                    'rating': review_node.get('rating'),
                    'review_text': review_node.get('review'),
                    'date': review_node.get('date'),
                    'is_highlighted': review_node.get('isHighlighted'),
                    'advisor_strengths': review_node.get('advisorStrengths'),
                    'session_id': source.get('id'),
                    'session_exists': source.get('sessionExists'),
                    'master_transaction_id': source.get('masterTransactionId'),
                    'target_type': target.get('type')
                }],
                'session_data': {
                    'session_ids': [source.get('id')] if source.get('id') else [],
                    'transaction_ids': [source.get('masterTransactionId')] if source.get('masterTransactionId') else []
                }
            }
            
            customers[customer_id] = customer_record
    
    return customers

def analyze_and_save_customers(all_customers):
    """Analyze and save the extracted customer data"""
    
    print(f"📊 CUSTOMER DATA ANALYSIS")
    print("=" * 40)
    
    if not all_customers:
        print("❌ No customers extracted")
        return
    
    customers_list = list(all_customers.values())
    
    print(f"👥 TOTAL CUSTOMERS: {len(customers_list)}")
    
    # Calculate statistics
    total_reviews = sum(c['total_reviews'] for c in customers_list)
    customers_with_username = sum(1 for c in customers_list if c.get('userName'))
    customers_with_nickname = sum(1 for c in customers_list if c.get('nickname'))
    customers_with_sessions = sum(1 for c in customers_list if c['session_data']['session_ids'])
    
    print(f"\n📊 DATA STATISTICS:")
    print(f"  Total reviews processed: {total_reviews}")
    print(f"  Customers with username: {customers_with_username}/{len(customers_list)} ({customers_with_username/len(customers_list)*100:.1f}%)")
    print(f"  Customers with nickname: {customers_with_nickname}/{len(customers_list)} ({customers_with_nickname/len(customers_list)*100:.1f}%)")
    print(f"  Customers with session data: {customers_with_sessions}/{len(customers_list)} ({customers_with_sessions/len(customers_list)*100:.1f}%)")
    
    # Top customers by review count
    top_customers = sorted(customers_list, key=lambda x: x['total_reviews'], reverse=True)[:10]
    
    print(f"\n🏆 TOP 10 CUSTOMERS BY REVIEW COUNT:")
    for i, customer in enumerate(top_customers, 1):
        name = customer.get('userName', 'Unknown')
        nickname = customer.get('nickname', '')
        review_count = customer['total_reviews']
        print(f"  {i:2d}. {name} ({nickname}) - {review_count} reviews")
    
    # Calculate average ratings
    customers_with_ratings = []
    for customer in customers_list:
        ratings = [r['rating'] for r in customer['reviews'] if r.get('rating')]
        if ratings:
            avg_rating = sum(ratings) / len(ratings)
            customers_with_ratings.append({
                'customer': customer,
                'avg_rating': avg_rating,
                'rating_count': len(ratings)
            })
    
    if customers_with_ratings:
        top_rated = sorted(customers_with_ratings, key=lambda x: x['avg_rating'], reverse=True)[:5]
        
        print(f"\n⭐ TOP 5 HIGHEST RATED CUSTOMERS:")
        for i, item in enumerate(top_rated, 1):
            customer = item['customer']
            name = customer.get('userName', 'Unknown')
            avg_rating = item['avg_rating']
            rating_count = item['rating_count']
            print(f"  {i}. {name} - {avg_rating:.2f}/5 ({rating_count} ratings)")
    
    # Save comprehensive data
    output_data = {
        'extraction_summary': {
            'total_customers': len(customers_list),
            'total_reviews_processed': total_reviews,
            'customers_with_username': customers_with_username,
            'customers_with_nickname': customers_with_nickname,
            'customers_with_sessions': customers_with_sessions,
            'extraction_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        },
        'customers': customers_list
    }
    
    # Save to JSON file
    with open('data/all_customers_via_reviews.json', 'w') as f:
        json.dump(output_data, f, indent=2)
    print(f"\n💾 Complete customer data saved to: data/all_customers_via_reviews.json")
    
    # Save a simplified version for easy processing
    simplified_customers = []
    for customer in customers_list:
        simplified = {
            'id': customer['id'],
            'userName': customer.get('userName'),
            'nickname': customer.get('nickname'),
            'total_reviews': customer['total_reviews'],
            'session_ids': customer['session_data']['session_ids'],
            'transaction_ids': customer['session_data']['transaction_ids']
        }
        simplified_customers.append(simplified)
    
    with open('data/customers_simplified.json', 'w') as f:
        json.dump(simplified_customers, f, indent=2)
    print(f"💾 Simplified customer data saved to: data/customers_simplified.json")
    
    print(f"\n🎉 EXTRACTION COMPLETE!")
    print(f"✅ {len(customers_list)} customers with {total_reviews} reviews extracted!")
    print(f"✅ Ready for Google Voice correlation and transcription!")

if __name__ == "__main__":
    extract_all_via_reviews()
