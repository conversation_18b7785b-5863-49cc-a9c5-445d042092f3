# AstralSubstance Clone Configuration

# Keen.com API Configuration
keen:
  base_url: "https://www.keen.com"
  graphql_endpoint: "/api/graphql2"

  # Authentication - Update these with your actual session cookies
  cookies:
    KeenUid: "Uid=eTRPMya5tB7-kgTqrCmjFQ%3d%3d&firstVisitTime=2025-03-21%2010:52:23%20AM&ipAddress=**************,**************&ANNON=N"
    KeenTKT: "MPS=N&Tkt=-103-%7ctMQ%3d%3d%7caLTE%3d%7cb%7cpMA%3d%3d%7cqMA%3d%3d%7coMQ%3d%3d%7cnQXN0cmFsIFN1YnN0YW5jZQ%3d%3d%7cuZVRSUE15YTV0Qjcta2dUcXJDbWpGUT09%7cdMTc0ODA3OTk3OS43MTk1%7clMTc0ODA3OTk3OS43MTk1%7cc%7ckMQ%3d%3d%7cs6D2A426C6383BB19D485541ECCC46A57&Rbm=y"
    KeenUser: "UserUid=eTRPMya5tB7-kgTqrCmjFQ==&RccLastSync=2025-05-24+09%3a46&IsAdvisor=True&BecameFbm=False&BecameRCC=False&BecameFbmLastSync=5%2f24%2f2025+9%3a46%3a19+AM"
    SessionId: "7f0d309c-be38-f011-bf3f-98f2b31428e6"
    SiftSessionId: "481fcbb6-f67d-abe4-2790-c18a8b9e250f"
    iterableEndUserId: "<EMAIL>"
    PassThrough: "Channel=Direct/SEO"
    pnctest: "1"
    TidTracking: "tid=null&trackingContextId=1"

  # Request settings
  rate_limiting:
    base_delay: 2.5  # seconds between requests
    max_retries: 3
    backoff_factor: 2.0
    max_concurrent: 2

  # Pagination settings
  pagination:
    default_page_size: 100
    max_page_size: 500

# Google Voice Configuration
google_voice:
  recordings_directory: "data/raw/voice_recordings"
  supported_formats: [".wav", ".mp3", ".m4a", ".ogg", ".webm"]

  # Keen.com phone number for filtering calls
  keen_phone_number: "(*************"

  # Authentication - Update these with your Google Voice session cookies
  cookies:
    SID: "g.a000wghQUFkpG1DcrLuGT6LLse38PFhHpUJl4SlA_7Qsix4P8s8Cf3Vpd94IndZmU6CwwII32AACgYKAQ8SARESFQHGX2Mi1SPZDELz0YMSMwOz3gzrfBoVAUF8yKpBMkcuefQiizQ92MOQ_4xH0076"
    HSID: "AKewvW3bHmqc392rV"
    SSID: "ATZZbJJrVwj0OYO6g"
    APISID: "eUyqAoSM-RDhmZY0/Ap1b08-HrFIPVaJpc"
    SAPISID: "VgMcw9RP8XVcfoRp/AvG_lUbrbcg4uLPXd"
    __Secure-1PSID: "g.a000wghQUFkpG1DcrLuGT6LLse38PFhHpUJl4SlA_7Qsix4P8s8ClIjn3T8M09YcK7DXC1yoJwACgYKAfASARESFQHGX2Mi8l_2NgaOc7T6fyp63vMPsxoVAUF8yKoIZ5Q7m2XR9a_R77D2uPSp0076"
    __Secure-3PSID: "g.a000wghQUFkpG1DcrLuGT6LLse38PFhHpUJl4SlA_7Qsix4P8s8CVz0dgDSx9OT0Jgd-RJVRtgACgYKAa8SARESFQHGX2MiBcp_StRg7aNJuN_8mOpKPhoVAUF8yKpdCwvwVToRzhhMVLILewJc0076"
    __Secure-1PAPISID: "VgMcw9RP8XVcfoRp/AvG_lUbrbcg4uLPXd"
    __Secure-3PAPISID: "VgMcw9RP8XVcfoRp/AvG_lUbrbcg4uLPXd"
    SIDCC: "AKEyXzXMps0cQRlLSYvREOQq1zJjBkHo46N4Fi0ko7HMnZGxLIZWLb2p_f25jmsnnImp_n9J3So"
    __Secure-1PSIDCC: "AKEyXzVRZuZr2tLURDgs2p6cYWrOQUNWbNhOCtU6b69YIrMLFDOQFwUTJovDw7iIGQiS6SvTsvh7"
    __Secure-3PSIDCC: "AKEyXzV6QbVlkpHkkqe8c5wzAKdMnke-GyhykohlJk62kd_a4fUYfBQtAEozo1CJPmB46MMEX8M"
    NID: "524=jc1Q2P4WEh2l5FaZT0FgwhETMNdWhzMRriSTtfgW51XAhGDpBBeCqa8kmYpw_7wzRobcGLXM6cXyOdzCHJCGw25qoSXyx13o2QbW2ixjAuZUr5ywJpUaSwRqzz9T9aoo-qlLEC1TPIi2eSbQyvk5CfJYSUQw_sqlnGD_2HMz1RX213wPfx7mz9pBYsDP2x4QdPpsP6i7yyPQJfvKaOchJbxD9_IQVuuYzFDV9gHddn5lWMww83J78p_KauHIKt3BM_632cO3XMzf-0SVUlT_PguXzqxM3ZMZChNpyj76zkMDnxvoChRZ2yUQ2IHOMcB9ZMUPSyQrGwtzlpDEiYQFciJqC7SCnVSKfeKNzsLQoWmTizhoazTU-p242wdsjc59MYmsXvgorGcuNyPIjJxZMYFkccmMHwI1xbYaoD_3l8KZ0MEFOufBc6jPL9BkqCTaBUOQyqC4J2aiYUhmbQjWMcx2CjkkLq_15fNDjSJdRYA7lw-Xhsv9Yah-xmiG1nNyYzd9q4JCZ7PKk74f5Irs0cdBy71QWKXx29EDiMlfN5vz_vL-UXOfkt3dZhEqdjRZDQcX7CJyKutp4UsfDYpA4zKetTNOrHqXG85dwJvLizGiaRkytr2_QCqB6TEW2VPtqGZ1x55JoJQNKXGDxszzFwvFoECILsZG9WATDCLXcvu91H_Bg7ISw3caKSMzPTFuegUpqlQzoOJuD2WAZT_aGQUCmlV2ZF8P7KyZa0QCtfTx_--VgYx_Kd_VFiK1UEqXdoNfl7rQxScbUEKRXUxbzyxeUsWDypM49r7wm79B4FQHkHxLqMYGA60iULjx1QnSVxixtWL1luZm8BAXawhv0iWNtZqZdfQwrkKkbE1B0UNkeULy_SJZnJAofFaI0A-nmzTgKS4RidfLZY_s4FISzs-52Z68x8_6r2FbcnEen2YLOTfSW3oI7aD7VBndxt3OYrPnfgi5OZA7bfoHVVuYh9XaVCrOUkBw2MClhdlbXQYrM9LHePD4ma2miHtSGPeiJD7RPxydt6-kBJCXATTT6bbb0O2zDt1Xl-lggUhKf2VM-G_4wfRL-Fy_xxHV90mq6CB3qyzKQCoc1rsnbYS1LFKe_QrwDHUdeaKfQTCeX2ExYpOonHoqgC75dbmQW5p8tKgvRgISruu9VtD7m6B74bl7qDKoPCq9cuhFfG8uMNKOrLOwfDyYF6dfntA"
    SEARCH_SAMESITE: "CgQI-J0B"
    __Secure-1PSIDTS: "sidts-CjIBjplskEirpvX_J-RCCDQN4l2w9VNyqkP-1YHBDPwahYwWqCbGYDrbcnljKd0JDjHoshAA"
    __Secure-3PSIDTS: "sidts-CjIBjplskEirpvX_J-RCCDQN4l2w9VNyqkP-1YHBDPwahYwWqCbGYDrbcnljKd0JDjHoshAA"
    AEC: "AVh_V2jVrCfmAU_BDXETdGsT9J4YnxegGEsK-wI0QvckzEA3QDFRObxlBw"
    SOCS: "CAISHAgCEhJnd3NfMjAyNTAzMTAtMF9SQzMaAmVuIAEaBgiAzN2-Bg"

  # Audio processing settings
  audio:
    sample_rate: 16000
    normalize_audio: true
    remove_silence: true

  # Scraping settings
  scraping:
    max_calls_per_session: 1000
    delay_between_downloads: 2.0  # seconds
    retry_attempts: 3

# Audio Transcription Configuration
transcription:
  # Whisper model settings
  whisper_model: "base"  # tiny, base, small, medium, large

  # Processing settings
  batch_size: 5
  enable_diarization: true
  min_confidence: 0.6

  # Audio preprocessing
  remove_silence: true
  normalize_audio: true

  # Performance settings
  use_gpu: true  # Use GPU if available
  device: "auto"  # auto, cpu, cuda, mps

# Database Configuration
database:
  type: "sqlite"  # sqlite, postgresql, mysql
  sqlite:
    path: "data/astral_substance.db"

  # Uncomment for PostgreSQL
  # postgresql:
  #   host: "localhost"
  #   port: 5432
  #   database: "astral_substance"
  #   username: "your_username"
  #   password: "your_password"

# Data Storage Configuration
storage:
  raw_data_dir: "data/raw"
  processed_data_dir: "data/processed"
  datasets_dir: "data/datasets"
  logs_dir: "logs"

  # Backup settings
  backup:
    enabled: true
    interval_hours: 24
    max_backups: 7

# Data Correlation Settings
correlation:
  timestamp_tolerance_minutes: 5
  duration_tolerance_percent: 10
  confidence_threshold: 0.6

  # Matching weights
  weights:
    timestamp: 0.7
    duration: 0.3

# LLM Dataset Configuration
dataset:
  formats:
    - "conversational"
    - "instruction_following"
    - "prompt_completion"

  # Output settings
  output:
    max_conversation_length: 10000  # characters
    include_metadata: true
    preserve_timestamps: true

  # Data splits
  splits:
    train: 0.8
    validation: 0.1
    test: 0.1

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

  # File logging
  file_logging:
    enabled: true
    rotation: "100 MB"
    retention: "30 days"

  # Console logging
  console_logging:
    enabled: true
    colorize: true

# Security Settings
security:
  encrypt_cookies: true
  encryption_key_file: "config/.encryption_key"

  # Data anonymization (post-processing)
  anonymization:
    enabled: false  # Set to true for PII removal
    methods:
      - "name_replacement"
      - "phone_masking"
      - "email_masking"

# Performance Settings
performance:
  batch_size: 50
  memory_limit_mb: 2048
  parallel_processing: true
  max_workers: 4

# Monitoring and Alerts
monitoring:
  enabled: true
  metrics:
    - "extraction_rate"
    - "error_rate"
    - "data_quality_score"

  # Health checks
  health_checks:
    interval_minutes: 15
    endpoints:
      - "keen_api_status"
      - "database_connection"
      - "disk_space"

# Development Settings
development:
  debug_mode: false
  sample_mode: false  # Extract only a small sample for testing
  sample_size: 10

  # Mock data for testing
  mock_data:
    enabled: false
    customers: 5
    conversations_per_customer: 3
