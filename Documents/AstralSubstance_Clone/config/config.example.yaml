# AstralSubstance Clone Configuration

# Keen.com API Configuration
keen:
  base_url: "https://www.keen.com"
  graphql_endpoint: "/api/graphqlv0"

  # Authentication - Update these with your actual session cookies
  cookies:
    session_token: "YOUR_SESSION_TOKEN_HERE"
    csrf_token: "YOUR_CSRF_TOKEN_HERE"
    # Add other required cookies as discovered

  # Request settings
  rate_limiting:
    base_delay: 2.5  # seconds between requests
    max_retries: 3
    backoff_factor: 2.0
    max_concurrent: 2

  # Pagination settings
  pagination:
    default_page_size: 100
    max_page_size: 500

# Google Voice Configuration
google_voice:
  recordings_directory: "data/raw/voice_recordings"
  supported_formats: [".wav", ".mp3", ".m4a", ".ogg", ".webm"]

  # Keen.com phone number for filtering calls
  keen_phone_number: "(*************"

  # Authentication - Update these with your Google Voice session cookies
  cookies:
    SID: "YOUR_SID_COOKIE_HERE"
    HSID: "YOUR_HSID_COOKIE_HERE"
    SSID: "YOUR_SSID_COOKIE_HERE"
    APISID: "YOUR_APISID_COOKIE_HERE"
    SAPISID: "YOUR_SAPISID_COOKIE_HERE"
    __Secure-1PSID: "YOUR_SECURE_1PSID_COOKIE_HERE"
    __Secure-3PSID: "YOUR_SECURE_3PSID_COOKIE_HERE"
    __Secure-1PAPISID: "YOUR_SECURE_1PAPISID_COOKIE_HERE"
    __Secure-3PAPISID: "YOUR_SECURE_3PAPISID_COOKIE_HERE"
    SIDCC: "YOUR_SIDCC_COOKIE_HERE"
    __Secure-1PSIDCC: "YOUR_SECURE_1PSIDCC_COOKIE_HERE"
    __Secure-3PSIDCC: "YOUR_SECURE_3PSIDCC_COOKIE_HERE"
    NID: "YOUR_NID_COOKIE_HERE"
    # Add other cookies as needed

  # Audio processing settings
  audio:
    sample_rate: 16000
    normalize_audio: true
    remove_silence: true

  # Scraping settings
  scraping:
    max_calls_per_session: 1000
    delay_between_downloads: 2.0  # seconds
    retry_attempts: 3

# Audio Transcription Configuration
transcription:
  # Whisper model settings
  whisper_model: "base"  # tiny, base, small, medium, large

  # Processing settings
  batch_size: 5
  enable_diarization: true
  min_confidence: 0.6

  # Audio preprocessing
  remove_silence: true
  normalize_audio: true

  # Performance settings
  use_gpu: true  # Use GPU if available
  device: "auto"  # auto, cpu, cuda, mps

# Database Configuration
database:
  type: "sqlite"  # sqlite, postgresql, mysql
  sqlite:
    path: "data/astral_substance.db"

  # Uncomment for PostgreSQL
  # postgresql:
  #   host: "localhost"
  #   port: 5432
  #   database: "astral_substance"
  #   username: "your_username"
  #   password: "your_password"

# Data Storage Configuration
storage:
  raw_data_dir: "data/raw"
  processed_data_dir: "data/processed"
  datasets_dir: "data/datasets"
  logs_dir: "logs"

  # Backup settings
  backup:
    enabled: true
    interval_hours: 24
    max_backups: 7

# Data Correlation Settings
correlation:
  timestamp_tolerance_minutes: 5
  duration_tolerance_percent: 10
  confidence_threshold: 0.6

  # Matching weights
  weights:
    timestamp: 0.7
    duration: 0.3

# LLM Dataset Configuration
dataset:
  formats:
    - "conversational"
    - "instruction_following"
    - "prompt_completion"

  # Output settings
  output:
    max_conversation_length: 10000  # characters
    include_metadata: true
    preserve_timestamps: true

  # Data splits
  splits:
    train: 0.8
    validation: 0.1
    test: 0.1

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

  # File logging
  file_logging:
    enabled: true
    rotation: "100 MB"
    retention: "30 days"

  # Console logging
  console_logging:
    enabled: true
    colorize: true

# Security Settings
security:
  encrypt_cookies: true
  encryption_key_file: "config/.encryption_key"

  # Data anonymization (post-processing)
  anonymization:
    enabled: false  # Set to true for PII removal
    methods:
      - "name_replacement"
      - "phone_masking"
      - "email_masking"

# Performance Settings
performance:
  batch_size: 50
  memory_limit_mb: 2048
  parallel_processing: true
  max_workers: 4

# Monitoring and Alerts
monitoring:
  enabled: true
  metrics:
    - "extraction_rate"
    - "error_rate"
    - "data_quality_score"

  # Health checks
  health_checks:
    interval_minutes: 15
    endpoints:
      - "keen_api_status"
      - "database_connection"
      - "disk_space"

# Development Settings
development:
  debug_mode: false
  sample_mode: false  # Extract only a small sample for testing
  sample_size: 10

  # Mock data for testing
  mock_data:
    enabled: false
    customers: 5
    conversations_per_customer: 3
