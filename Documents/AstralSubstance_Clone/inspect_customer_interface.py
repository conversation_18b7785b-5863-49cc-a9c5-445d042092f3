#!/usr/bin/env python3
"""
Inspect the customer management interface to find how it loads all customers
"""

import sys
from pathlib import Path
import requests
import json
import re
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import <PERSON>fi<PERSON><PERSON><PERSON><PERSON>

def inspect_customer_interface():
    """Inspect the customer management interface"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers for web requests
    web_headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    # Headers for API requests
    api_headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    base_url = keen_config['base_url']
    
    print("🔍 INSPECTING CUSTOMER MANAGEMENT INTERFACE")
    print("=" * 60)
    
    # Step 1: Load the customer management page and analyze it
    print("\n📄 Step 1: Loading customer management page...")
    customer_page_content = load_customer_page(session, web_headers, base_url)
    
    if customer_page_content:
        print("\n🔍 Step 2: Analyzing page for API endpoints...")
        api_endpoints = extract_api_endpoints_from_page(customer_page_content)
        
        print("\n📊 Step 3: Testing discovered API endpoints...")
        test_discovered_endpoints(session, api_headers, base_url, api_endpoints)
        
        print("\n🌐 Step 4: Monitoring network requests...")
        monitor_network_requests(session, api_headers, base_url)

def load_customer_page(session, headers, base_url):
    """Load the customer management page and extract useful information"""
    
    url = f"{base_url}/app/myaccount/customers"
    
    try:
        response = session.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            content = response.text
            print(f"  ✅ Page loaded successfully ({len(content)} characters)")
            
            # Save the page content for analysis
            with open('data/customer_page_content.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  💾 Page content saved to: data/customer_page_content.html")
            
            return content
        else:
            print(f"  ❌ Failed to load page: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ Error loading page: {e}")
        return None

def extract_api_endpoints_from_page(content):
    """Extract API endpoints from the page content"""
    
    print(f"  🔍 Analyzing page content...")
    
    # Look for various patterns that might indicate API endpoints
    patterns = [
        # GraphQL queries
        r'query[^{]*{[^}]*customer[^}]*}',
        # API URLs
        r'["\']([^"\']*api[^"\']*)["\']',
        # Fetch calls
        r'fetch\(["\']([^"\']*)["\']',
        # Axios calls
        r'axios\.[a-z]+\(["\']([^"\']*)["\']',
        # AJAX URLs
        r'url\s*:\s*["\']([^"\']*)["\']',
        # Customer-related endpoints
        r'["\']([^"\']*customer[^"\']*)["\']',
    ]
    
    found_endpoints = set()
    
    for pattern in patterns:
        matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
        for match in matches:
            if isinstance(match, str) and len(match) > 5:
                # Clean up the match
                cleaned = match.strip()
                if cleaned.startswith('/') or cleaned.startswith('http'):
                    found_endpoints.add(cleaned)
    
    # Filter for relevant endpoints
    relevant_endpoints = []
    for endpoint in found_endpoints:
        if any(keyword in endpoint.lower() for keyword in ['customer', 'api', 'graphql', 'user', 'advisor']):
            relevant_endpoints.append(endpoint)
    
    print(f"  📊 Found {len(relevant_endpoints)} potentially relevant endpoints:")
    for endpoint in relevant_endpoints[:10]:  # Show first 10
        print(f"    - {endpoint}")
    
    return relevant_endpoints

def test_discovered_endpoints(session, headers, base_url, endpoints):
    """Test the discovered endpoints"""
    
    for endpoint in endpoints[:5]:  # Test first 5 endpoints
        print(f"  Testing endpoint: {endpoint}")
        
        # Construct full URL
        if endpoint.startswith('http'):
            url = endpoint
        elif endpoint.startswith('/'):
            url = f"{base_url}{endpoint}"
        else:
            continue
        
        try:
            # Try GET first
            response = session.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                print(f"    ✅ GET successful ({len(response.content)} bytes)")
                
                # Check if it's JSON
                try:
                    data = response.json()
                    print(f"    📄 JSON response with keys: {list(data.keys())[:5]}")
                    
                    # Look for customer data
                    if has_customer_data_in_response(data):
                        print(f"    🎉 CONTAINS CUSTOMER DATA!")
                        
                        # Save this response for analysis
                        with open(f'data/endpoint_response_{endpoint.replace("/", "_")}.json', 'w') as f:
                            json.dump(data, f, indent=2)
                        print(f"    💾 Response saved for analysis")
                        
                except json.JSONDecodeError:
                    print(f"    📄 Non-JSON response")
                    
            elif response.status_code == 405:
                print(f"    ⚠️  Method not allowed, trying POST...")
                
                # Try POST with empty body
                post_response = session.post(url, headers=headers, json={}, timeout=15)
                if post_response.status_code == 200:
                    print(f"    ✅ POST successful ({len(post_response.content)} bytes)")
                else:
                    print(f"    ❌ POST failed: {post_response.status_code}")
                    
            else:
                print(f"    ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(1)

def has_customer_data_in_response(data):
    """Check if response contains customer data"""
    
    def search_for_customers(obj, depth=0):
        if depth > 5:  # Prevent infinite recursion
            return False
            
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'customer' in key.lower() and isinstance(value, (list, dict)):
                    return True
                if isinstance(value, (dict, list)):
                    if search_for_customers(value, depth + 1):
                        return True
        elif isinstance(obj, list):
            for item in obj:
                if search_for_customers(item, depth + 1):
                    return True
        return False
    
    return search_for_customers(data)

def monitor_network_requests(session, headers, base_url):
    """Try to simulate what the web interface does to load customers"""
    
    print(f"  🌐 Simulating web interface customer loading...")
    
    # Common endpoints that web interfaces use for customer data
    common_endpoints = [
        '/api/customers',
        '/api/customers/list',
        '/api/customers/search',
        '/api/advisor/customers',
        '/api/user/customers',
        '/api/graphql',
        '/api/graphql2',
        '/DomainOverrides/Advice/Customers/List.asmx',
        '/DomainOverrides/Advice/Customers/Search.asmx',
    ]
    
    for endpoint in common_endpoints:
        print(f"    Testing common endpoint: {endpoint}")
        
        url = f"{base_url}{endpoint}"
        
        try:
            # Try different request methods and payloads
            test_requests = [
                {'method': 'GET', 'data': None},
                {'method': 'POST', 'data': {}},
                {'method': 'POST', 'data': {'pageSize': 100, 'pageNumber': 1}},
                {'method': 'POST', 'data': {'first': 100}},
            ]
            
            for test_req in test_requests:
                try:
                    if test_req['method'] == 'GET':
                        response = session.get(url, headers=headers, timeout=10)
                    else:
                        response = session.post(url, headers=headers, json=test_req['data'], timeout=10)
                    
                    if response.status_code == 200:
                        print(f"      ✅ {test_req['method']} successful!")
                        
                        try:
                            data = response.json()
                            if has_customer_data_in_response(data):
                                print(f"      🎉 FOUND CUSTOMER DATA!")
                                
                                # Count potential customers
                                customer_count = count_potential_customers(data)
                                print(f"      👥 Potential customers: {customer_count}")
                                
                                if customer_count > 1000:
                                    print(f"      🚀 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                                    
                                    # Save this endpoint and data
                                    endpoint_info = {
                                        'endpoint': endpoint,
                                        'method': test_req['method'],
                                        'data': test_req['data'],
                                        'customer_count': customer_count,
                                        'response': data
                                    }
                                    
                                    with open('data/full_customer_endpoint.json', 'w') as f:
                                        json.dump(endpoint_info, f, indent=2)
                                    print(f"      💾 Full customer endpoint saved!")
                                    
                                    return endpoint_info
                                    
                        except json.JSONDecodeError:
                            pass
                            
                    elif response.status_code not in [404, 405]:
                        print(f"      ⚠️  {test_req['method']} returned {response.status_code}")
                        
                except Exception as e:
                    pass  # Ignore individual request errors
                    
        except Exception as e:
            print(f"      ❌ Error testing {endpoint}: {e}")
        
        time.sleep(0.5)
    
    return None

def count_potential_customers(data):
    """Count potential customers in response data"""
    
    def count_items(obj, depth=0):
        if depth > 5:
            return 0
            
        count = 0
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'customer' in key.lower() and isinstance(value, list):
                    count += len(value)
                elif isinstance(value, (dict, list)):
                    count += count_items(value, depth + 1)
        elif isinstance(obj, list):
            # If it's a list of objects that look like customers
            if obj and isinstance(obj[0], dict):
                # Check if items have customer-like fields
                first_item = obj[0]
                if any(field in first_item for field in ['id', 'name', 'email', 'username', 'customerId']):
                    count += len(obj)
            
            for item in obj:
                count += count_items(item, depth + 1)
                
        return count
    
    return count_items(data)

if __name__ == "__main__":
    inspect_customer_interface()
