#!/usr/bin/env python3
"""
Find the correct endpoint to access ALL 4,297 customers
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import <PERSON>fi<PERSON><PERSON><PERSON><PERSON>

def find_all_customers_endpoint():
    """Find the correct endpoint to access ALL customers"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
    }
    
    base_url = keen_config['base_url']
    
    print("🔍 FINDING ALL CUSTOMERS ENDPOINT")
    print("=" * 60)
    
    # Test different GraphQL endpoints and queries
    print("\n📊 Step 1: Testing different GraphQL endpoints...")
    test_graphql_endpoints(session, headers, base_url)
    
    print("\n🔍 Step 2: Testing advisor-specific queries...")
    test_advisor_queries(session, headers, base_url)
    
    print("\n📋 Step 3: Testing customer list queries...")
    test_customer_list_queries(session, headers, base_url)
    
    print("\n🌐 Step 4: Testing web interface endpoints...")
    test_web_interface_endpoints(session, headers, base_url)

def test_graphql_endpoints(session, headers, base_url):
    """Test different GraphQL endpoints"""
    
    endpoints = [
        '/api/graphql',
        '/api/graphql2',
        '/api/graphqlv1',
        '/api/graphqlv2',
        '/api/graphqlv3'
    ]
    
    for endpoint in endpoints:
        print(f"  Testing {endpoint}...")
        url = f"{base_url}{endpoint}"
        
        # Test with introspection to see available fields
        introspection_query = {
            "query": """
            query {
                __schema {
                    queryType {
                        fields {
                            name
                            type {
                                name
                            }
                        }
                    }
                }
            }
            """,
            "variables": {}
        }
        
        try:
            response = session.post(url, headers=headers, json=introspection_query, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and '__schema' in data['data']:
                    fields = data['data']['__schema']['queryType']['fields']
                    customer_fields = [f['name'] for f in fields if 'customer' in f['name'].lower()]
                    user_fields = [f['name'] for f in fields if 'user' in f['name'].lower()]
                    advisor_fields = [f['name'] for f in fields if 'advisor' in f['name'].lower()]
                    
                    print(f"    ✅ Working endpoint with {len(fields)} fields")
                    if customer_fields:
                        print(f"      👥 Customer fields: {customer_fields}")
                    if user_fields:
                        print(f"      👤 User fields: {user_fields}")
                    if advisor_fields:
                        print(f"      🎯 Advisor fields: {advisor_fields}")
                        
                    # Test specific customer queries on this endpoint
                    test_customer_queries_on_endpoint(session, headers, url, customer_fields + user_fields + advisor_fields)
                else:
                    print(f"    ❌ No schema data")
            else:
                print(f"    ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")

def test_customer_queries_on_endpoint(session, headers, url, relevant_fields):
    """Test customer-related queries on a specific endpoint"""
    
    # Test queries that might return all customers
    test_queries = []
    
    # Add queries based on available fields
    for field in relevant_fields:
        if 'customer' in field.lower():
            test_queries.append({
                'name': field,
                'query': f"""
                query {{
                    {field} {{
                        __typename
                    }}
                }}
                """
            })
    
    # Add some common customer queries
    common_queries = [
        {
            'name': 'currentUser.customers',
            'query': """
            query {
                currentUser {
                    ... on Advisor {
                        customers(first: 10) {
                            edges {
                                node {
                                    id
                                    userName
                                }
                            }
                        }
                    }
                }
            }
            """
        },
        {
            'name': 'advisor.customers',
            'query': """
            query {
                advisor(id: 56392386) {
                    customers(first: 10) {
                        edges {
                            node {
                                id
                                userName
                            }
                        }
                    }
                }
            }
            """
        }
    ]
    
    test_queries.extend(common_queries)
    
    for query_info in test_queries[:5]:  # Test first 5 queries
        print(f"      Testing query: {query_info['name']}")
        
        try:
            response = session.post(url, headers=headers, json={'query': query_info['query']}, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    error_msg = data['errors'][0]['message']
                    if 'does not exist' not in error_msg:
                        print(f"        ⚠️  {error_msg}")
                elif 'data' in data:
                    print(f"        ✅ Query successful!")
                    # Look for customer data in response
                    if has_customer_data(data['data']):
                        print(f"        🎉 FOUND CUSTOMER DATA!")
                        return True
            else:
                print(f"        ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"        ❌ Error: {e}")
        
        time.sleep(0.5)
    
    return False

def has_customer_data(data):
    """Check if response contains customer data"""
    
    def search_for_customers(obj):
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'customer' in key.lower() and isinstance(value, (list, dict)):
                    return True
                if isinstance(value, (dict, list)):
                    if search_for_customers(value):
                        return True
        elif isinstance(obj, list):
            for item in obj:
                if search_for_customers(item):
                    return True
        return False
    
    return search_for_customers(data)

def test_advisor_queries(session, headers, base_url):
    """Test advisor-specific queries that might have all customers"""
    
    graphql_url = f"{base_url}/api/graphql"
    
    # Test different ways to access advisor customer data
    advisor_queries = [
        {
            'name': 'advisor with large first parameter',
            'query': """
            query {
                advisor(id: 56392386) {
                    customers(first: 5000) {
                        edges {
                            node {
                                id
                                userName
                                customerId
                            }
                        }
                    }
                }
            }
            """
        },
        {
            'name': 'currentUser as advisor with large first',
            'query': """
            query {
                currentUser {
                    ... on Advisor {
                        customers(first: 5000) {
                            edges {
                                node {
                                    id
                                    userName
                                    customerId
                                }
                            }
                        }
                    }
                }
            }
            """
        },
        {
            'name': 'advisor customerLists',
            'query': """
            query {
                advisor(id: 56392386) {
                    customerLists {
                        edges {
                            node {
                                id
                                name
                                customers(first: 1000) {
                                    edges {
                                        node {
                                            id
                                            userName
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            """
        }
    ]
    
    for query_info in advisor_queries:
        print(f"  Testing: {query_info['name']}")
        
        try:
            response = session.post(graphql_url, headers=headers, json={'query': query_info['query']}, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"    ❌ Error: {data['errors'][0]['message']}")
                elif 'data' in data:
                    print(f"    ✅ Query successful!")
                    
                    # Count customers found
                    customer_count = count_customers_in_response(data['data'])
                    if customer_count > 0:
                        print(f"    🎉 Found {customer_count} customers!")
                        if customer_count > 200:  # If we found a lot of customers
                            print(f"    🚀 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                            return query_info
            else:
                print(f"    ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(1)
    
    return None

def count_customers_in_response(data):
    """Count customers in a GraphQL response"""
    
    def count_customers(obj):
        count = 0
        if isinstance(obj, dict):
            for key, value in obj.items():
                if key == 'edges' and isinstance(value, list):
                    count += len(value)
                elif isinstance(value, (dict, list)):
                    count += count_customers(value)
        elif isinstance(obj, list):
            for item in obj:
                count += count_customers(item)
        return count
    
    return count_customers(data)

def test_customer_list_queries(session, headers, base_url):
    """Test customer list specific queries"""
    
    graphql_url = f"{base_url}/api/graphql"
    
    # Test if there are predefined customer lists with all customers
    list_queries = [
        {
            'name': 'all customer lists',
            'query': """
            query {
                currentUser {
                    ... on Advisor {
                        customerLists(first: 100) {
                            edges {
                                node {
                                    id
                                    name
                                    customers {
                                        edges {
                                            node {
                                                id
                                                userName
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            """
        }
    ]
    
    for query_info in list_queries:
        print(f"  Testing: {query_info['name']}")
        
        try:
            response = session.post(graphql_url, headers=headers, json={'query': query_info['query']}, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"    ❌ Error: {data['errors'][0]['message']}")
                elif 'data' in data:
                    print(f"    ✅ Query successful!")
                    
                    # Analyze customer lists
                    if 'currentUser' in data['data'] and data['data']['currentUser']:
                        customer_lists = data['data']['currentUser'].get('customerLists', {})
                        if 'edges' in customer_lists:
                            for edge in customer_lists['edges']:
                                list_node = edge['node']
                                list_name = list_node.get('name', 'Unnamed')
                                customers = list_node.get('customers', {}).get('edges', [])
                                print(f"      📋 List '{list_name}': {len(customers)} customers")
            else:
                print(f"    ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")

def test_web_interface_endpoints(session, headers, base_url):
    """Test web interface endpoints that might have customer data"""
    
    # Test the customer management pages we found earlier
    customer_pages = [
        '/app/myaccount/customers',
        '/app/myaccount/customer-list',
        '/app/myaccount/conversations',
    ]
    
    for page in customer_pages:
        print(f"  Testing web page: {page}")
        
        try:
            url = f"{base_url}{page}"
            response = session.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                content = response.text
                print(f"    ✅ Page loaded ({len(content)} chars)")
                
                # Look for AJAX endpoints or data in the page
                if 'customers' in content.lower():
                    print(f"    👥 Contains customer references")
                
                # Look for API calls in JavaScript
                import re
                api_calls = re.findall(r'fetch\(["\']([^"\']*)["\']', content)
                graphql_calls = re.findall(r'query[^{]*{[^}]*customer[^}]*}', content, re.IGNORECASE)
                
                if api_calls:
                    print(f"    🔗 Found {len(api_calls)} API calls")
                    for call in api_calls[:3]:
                        print(f"      - {call}")
                
                if graphql_calls:
                    print(f"    🔍 Found {len(graphql_calls)} GraphQL queries with 'customer'")
                    for query in graphql_calls[:2]:
                        print(f"      - {query[:100]}...")
                        
            else:
                print(f"    ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")

if __name__ == "__main__":
    find_all_customers_endpoint()
