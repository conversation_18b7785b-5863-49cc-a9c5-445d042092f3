#!/usr/bin/env python3
"""
Test the currentUser and advisor fields to find customer data
"""

import sys
from pathlib import Path
import requests
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON><PERSON><PERSON>

def test_user_advisor_fields():
    """Test currentUser and advisor fields for customer data"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
    }
    
    base_url = keen_config['base_url']
    graphql_url = f"{base_url}/api/graphql"
    
    print("🔍 TESTING USER AND ADVISOR FIELDS")
    print("=" * 60)
    
    # Test currentUser field
    print("\n👤 Testing currentUser field...")
    test_current_user(session, headers, graphql_url)
    
    # Test advisor field
    print("\n🎯 Testing advisor field...")
    test_advisor_field(session, headers, graphql_url)
    
    # Get detailed schema for User and Advisor types
    print("\n📊 Getting detailed type schemas...")
    get_type_schemas(session, headers, graphql_url)

def test_current_user(session, headers, graphql_url):
    """Test the currentUser field"""
    
    # Start with basic currentUser query
    basic_query = {
        "query": """
        query {
            currentUser {
                __typename
                id
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=basic_query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Error: {data['errors'][0]['message']}")
            elif 'data' in data and data['data']['currentUser']:
                user_data = data['data']['currentUser']
                print(f"  ✅ currentUser found!")
                print(f"    Type: {user_data.get('__typename', 'Unknown')}")
                print(f"    ID: {user_data.get('id', 'Unknown')}")
                
                # Try to get more user fields
                expanded_query = {
                    "query": """
                    query {
                        currentUser {
                            __typename
                            id
                            name
                            email
                            username
                            customers {
                                id
                                name
                                email
                            }
                            advisorCustomers {
                                id
                                name
                                email
                            }
                        }
                    }
                    """,
                    "variables": {}
                }
                
                print(f"  🔍 Testing expanded currentUser query...")
                expanded_response = session.post(graphql_url, headers=headers, json=expanded_query, timeout=30)
                
                if expanded_response.status_code == 200:
                    expanded_data = expanded_response.json()
                    
                    if 'errors' in expanded_data:
                        print(f"    ⚠️  Some fields not available: {expanded_data['errors'][0]['message']}")
                    elif 'data' in expanded_data:
                        user_info = expanded_data['data']['currentUser']
                        print(f"    ✅ Expanded user data:")
                        for key, value in user_info.items():
                            if key != '__typename':
                                print(f"      {key}: {value}")
                
            else:
                print(f"  ❌ currentUser returned null")
                
    except Exception as e:
        print(f"  ❌ Error: {e}")

def test_advisor_field(session, headers, graphql_url):
    """Test the advisor field"""
    
    # The advisor field might need an ID argument
    # First try without arguments
    basic_query = {
        "query": """
        query {
            advisor {
                __typename
                id
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=basic_query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                error_msg = data['errors'][0]['message']
                print(f"  ❌ Error: {error_msg}")
                
                # If it requires an ID, try with the advisor ID from the working query
                if 'argument' in error_msg.lower() or 'required' in error_msg.lower():
                    print(f"  🔍 Trying with advisor ID...")
                    
                    advisor_id_query = {
                        "query": """
                        query($advisorId: Int!) {
                            advisor(id: $advisorId) {
                                __typename
                                id
                                name
                                username
                                customers {
                                    id
                                    name
                                    email
                                }
                            }
                        }
                        """,
                        "variables": {"advisorId": 56392386}  # From the working ratingsAndReviews query
                    }
                    
                    advisor_response = session.post(graphql_url, headers=headers, json=advisor_id_query, timeout=30)
                    
                    if advisor_response.status_code == 200:
                        advisor_data = advisor_response.json()
                        
                        if 'errors' in advisor_data:
                            print(f"    ❌ Still error: {advisor_data['errors'][0]['message']}")
                        elif 'data' in advisor_data and advisor_data['data']['advisor']:
                            advisor_info = advisor_data['data']['advisor']
                            print(f"    ✅ Advisor found!")
                            print(f"      Type: {advisor_info.get('__typename', 'Unknown')}")
                            print(f"      ID: {advisor_info.get('id', 'Unknown')}")
                            print(f"      Name: {advisor_info.get('name', 'Unknown')}")
                            
                            if 'customers' in advisor_info:
                                customers = advisor_info['customers']
                                print(f"      👥 Customers: {len(customers) if customers else 0}")
                        else:
                            print(f"    ❌ Advisor returned null")
                            
            elif 'data' in data and data['data']['advisor']:
                advisor_data = data['data']['advisor']
                print(f"  ✅ advisor found!")
                print(f"    Type: {advisor_data.get('__typename', 'Unknown')}")
                print(f"    ID: {advisor_data.get('id', 'Unknown')}")
            else:
                print(f"  ❌ advisor returned null")
                
    except Exception as e:
        print(f"  ❌ Error: {e}")

def get_type_schemas(session, headers, graphql_url):
    """Get detailed schemas for User and Advisor types"""
    
    schema_query = {
        "query": """
        query {
            __schema {
                types {
                    name
                    kind
                    fields {
                        name
                        type {
                            name
                            kind
                            ofType {
                                name
                                kind
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=schema_query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'data' in data and '__schema' in data['data']:
                types = data['data']['__schema']['types']
                
                # Find User and Advisor types
                for type_def in types:
                    if type_def['name'] in ['User', 'Advisor', 'AdvisorCustomer']:
                        print(f"\n📋 {type_def['name']} type fields:")
                        
                        if type_def['fields']:
                            for field in type_def['fields']:
                                field_name = field['name']
                                field_type = get_simple_type_name(field['type'])
                                print(f"  - {field_name}: {field_type}")
                                
                                # Look for customer-related fields
                                if 'customer' in field_name.lower():
                                    print(f"    👥 CUSTOMER FIELD FOUND!")
                        else:
                            print(f"  No fields available")
                            
    except Exception as e:
        print(f"  ❌ Error getting type schemas: {e}")

def get_simple_type_name(type_info):
    """Get a simple type name"""
    if type_info.get('name'):
        return type_info['name']
    elif type_info.get('ofType'):
        return get_simple_type_name(type_info['ofType'])
    else:
        return type_info.get('kind', 'Unknown')

if __name__ == "__main__":
    test_user_advisor_fields()
