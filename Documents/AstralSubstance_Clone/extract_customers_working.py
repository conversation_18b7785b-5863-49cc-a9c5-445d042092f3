#!/usr/bin/env python3
"""
Extract ALL customer data using the correct GraphQL connection structure
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON><PERSON><PERSON>

def extract_customers_working():
    """Extract ALL customer data using the correct GraphQL structure"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
    }
    
    base_url = keen_config['base_url']
    graphql_url = f"{base_url}/api/graphql"
    
    print("🚀 EXTRACTING ALL CUSTOMER DATA (WORKING)")
    print("=" * 60)
    
    # Extract customers with correct structure
    print("\n👥 Extracting all customers...")
    customers_data = extract_all_advisor_customers(session, headers, graphql_url)
    
    print("\n📋 Extracting customer lists...")
    customer_lists_data = extract_all_customer_lists(session, headers, graphql_url)
    
    print("\n📊 Final analysis...")
    analyze_extracted_data(customers_data, customer_lists_data)

def extract_all_advisor_customers(session, headers, graphql_url):
    """Extract all advisor customers using correct GraphQL structure"""
    
    # Start with a simple query to see what's available
    simple_query = {
        "query": """
        query {
            currentUser {
                ... on Advisor {
                    customers {
                        edges {
                            node {
                                id
                                customerId
                                userName
                                nickname
                                emailAddress
                                lastContactDate
                                since
                                pastEarnings {
                                    amount
                                    displayAmount
                                }
                                ratingsAverage
                                comments
                                blockedAt
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=simple_query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Error: {data['errors'][0]['message']}")
                return {}
            elif 'data' in data and data['data']['currentUser'] and 'customers' in data['data']['currentUser']:
                customers = data['data']['currentUser']['customers']
                edges = customers.get('edges', [])
                
                print(f"  ✅ Found {len(edges)} customers in first query!")
                
                # Show sample customer
                if edges:
                    sample_customer = edges[0]['node']
                    print(f"  👤 Sample customer:")
                    for key, value in sample_customer.items():
                        if value is not None:
                            if key == 'pastEarnings' and isinstance(value, dict):
                                print(f"    {key}: {value.get('displayAmount', 'N/A')}")
                            else:
                                print(f"    {key}: {value}")
                
                # Try to get more customers with pagination
                all_customers = edges.copy()
                
                # Check if there are more customers by trying pagination
                print(f"\n  🔄 Checking for more customers with pagination...")
                more_customers = get_more_customers_with_pagination(session, headers, graphql_url, len(edges))
                all_customers.extend(more_customers)
                
                print(f"  🎉 Total customers extracted: {len(all_customers)}")
                
                # Save customer data
                customer_data = {
                    'extractedCount': len(all_customers),
                    'customers': [edge['node'] for edge in all_customers]
                }
                
                with open('data/all_customers_working.json', 'w') as f:
                    json.dump(customer_data, f, indent=2)
                print(f"  💾 Customer data saved to: data/all_customers_working.json")
                
                return customer_data
            else:
                print(f"  ❌ No customer data in response")
                return {}
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {}

def get_more_customers_with_pagination(session, headers, graphql_url, initial_count):
    """Try different pagination approaches to get more customers"""
    
    more_customers = []
    
    # Try with first/after pagination
    pagination_approaches = [
        {"first": 50},
        {"first": 100},
        {"first": 200},
        {"last": 50},
        {"last": 100},
    ]
    
    for approach in pagination_approaches:
        print(f"    Testing pagination with {approach}...")
        
        query = {
            "query": """
            query($first: Int, $last: Int, $after: String, $before: String) {
                currentUser {
                    ... on Advisor {
                        customers(first: $first, last: $last, after: $after, before: $before) {
                            edges {
                                node {
                                    id
                                    customerId
                                    userName
                                    nickname
                                    emailAddress
                                    lastContactDate
                                    since
                                    pastEarnings {
                                        amount
                                        displayAmount
                                    }
                                    ratingsAverage
                                    comments
                                    blockedAt
                                }
                            }
                            pageInfo {
                                hasNextPage
                                hasPreviousPage
                                startCursor
                                endCursor
                            }
                        }
                    }
                }
            }
            """,
            "variables": approach
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"      ❌ Error: {data['errors'][0]['message']}")
                elif 'data' in data and data['data']['currentUser'] and 'customers' in data['data']['currentUser']:
                    customers = data['data']['currentUser']['customers']
                    edges = customers.get('edges', [])
                    page_info = customers.get('pageInfo', {})
                    
                    print(f"      ✅ Got {len(edges)} customers")
                    print(f"      📄 Page info: hasNext={page_info.get('hasNextPage')}, hasPrev={page_info.get('hasPreviousPage')}")
                    
                    # If we got more customers than the initial query, this pagination works
                    if len(edges) > initial_count:
                        print(f"      🎉 Found more customers! Adding {len(edges) - initial_count} new ones")
                        # Add only the new customers (skip the ones we already have)
                        new_customers = edges[initial_count:]
                        more_customers.extend(new_customers)
                        
                        # If there are more pages, get them
                        if page_info.get('hasNextPage'):
                            print(f"      🔄 Getting additional pages...")
                            additional_customers = get_additional_pages(session, headers, graphql_url, page_info.get('endCursor'), approach)
                            more_customers.extend(additional_customers)
                        
                        break  # Found a working pagination approach
                else:
                    print(f"      ❌ No data in response")
            else:
                print(f"      ❌ HTTP error: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
        
        time.sleep(1)
    
    return more_customers

def get_additional_pages(session, headers, graphql_url, start_cursor, pagination_params):
    """Get additional pages of customers"""
    
    additional_customers = []
    current_cursor = start_cursor
    page_num = 2
    
    while current_cursor and page_num <= 10:  # Limit to 10 pages for safety
        print(f"        📄 Getting page {page_num}...")
        
        params = pagination_params.copy()
        params['after'] = current_cursor
        
        query = {
            "query": """
            query($first: Int, $last: Int, $after: String, $before: String) {
                currentUser {
                    ... on Advisor {
                        customers(first: $first, last: $last, after: $after, before: $before) {
                            edges {
                                node {
                                    id
                                    customerId
                                    userName
                                    nickname
                                    emailAddress
                                    lastContactDate
                                    since
                                    pastEarnings {
                                        amount
                                        displayAmount
                                    }
                                    ratingsAverage
                                    comments
                                    blockedAt
                                }
                            }
                            pageInfo {
                                hasNextPage
                                endCursor
                            }
                        }
                    }
                }
            }
            """,
            "variables": params
        }
        
        try:
            response = session.post(graphql_url, headers=headers, json=query, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errors' in data:
                    print(f"          ❌ Error on page {page_num}: {data['errors'][0]['message']}")
                    break
                elif 'data' in data and data['data']['currentUser'] and 'customers' in data['data']['currentUser']:
                    customers = data['data']['currentUser']['customers']
                    edges = customers.get('edges', [])
                    page_info = customers.get('pageInfo', {})
                    
                    print(f"          ✅ Page {page_num}: {len(edges)} customers")
                    additional_customers.extend(edges)
                    
                    if page_info.get('hasNextPage'):
                        current_cursor = page_info.get('endCursor')
                        page_num += 1
                    else:
                        break
                else:
                    print(f"          ❌ No data on page {page_num}")
                    break
            else:
                print(f"          ❌ HTTP error on page {page_num}: {response.status_code}")
                break
                
        except Exception as e:
            print(f"          ❌ Error on page {page_num}: {e}")
            break
        
        time.sleep(1)
    
    return additional_customers

def extract_all_customer_lists(session, headers, graphql_url):
    """Extract all customer lists"""
    
    query = {
        "query": """
        query {
            currentUser {
                ... on Advisor {
                    customerLists {
                        edges {
                            node {
                                id
                                name
                                description
                            }
                        }
                    }
                }
            }
        }
        """,
        "variables": {}
    }
    
    try:
        response = session.post(graphql_url, headers=headers, json=query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'errors' in data:
                print(f"  ❌ Error: {data['errors'][0]['message']}")
                return {}
            elif 'data' in data and data['data']['currentUser'] and 'customerLists' in data['data']['currentUser']:
                customer_lists = data['data']['currentUser']['customerLists']
                edges = customer_lists.get('edges', [])
                
                print(f"  ✅ Found {len(edges)} customer lists!")
                
                for edge in edges:
                    list_node = edge['node']
                    print(f"    📋 List: {list_node.get('name', 'Unnamed')} (ID: {list_node.get('id')})")
                
                # Save customer lists
                with open('data/customer_lists_working.json', 'w') as f:
                    json.dump(customer_lists, f, indent=2)
                print(f"  💾 Customer lists saved to: data/customer_lists_working.json")
                
                return customer_lists
            else:
                print(f"  ❌ No customer lists in response")
                return {}
                
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {}

def analyze_extracted_data(customers_data, customer_lists_data):
    """Analyze the extracted data"""
    
    print(f"📊 EXTRACTION ANALYSIS")
    print("=" * 40)
    
    if customers_data:
        customers = customers_data.get('customers', [])
        
        print(f"👥 CUSTOMERS EXTRACTED: {len(customers)}")
        
        if customers:
            # Data quality analysis
            with_email = sum(1 for c in customers if c.get('emailAddress'))
            with_nickname = sum(1 for c in customers if c.get('nickname'))
            with_earnings = sum(1 for c in customers if c.get('pastEarnings', {}).get('amount'))
            with_ratings = sum(1 for c in customers if c.get('ratingsAverage'))
            blocked = sum(1 for c in customers if c.get('blockedAt'))
            
            print(f"\n📊 DATA QUALITY:")
            print(f"  With email: {with_email}/{len(customers)} ({with_email/len(customers)*100:.1f}%)")
            print(f"  With nickname: {with_nickname}/{len(customers)} ({with_nickname/len(customers)*100:.1f}%)")
            print(f"  With earnings: {with_earnings}/{len(customers)} ({with_earnings/len(customers)*100:.1f}%)")
            print(f"  With ratings: {with_ratings}/{len(customers)} ({with_ratings/len(customers)*100:.1f}%)")
            print(f"  Blocked: {blocked}/{len(customers)} ({blocked/len(customers)*100:.1f}%)")
            
            # Top customers by earnings
            customers_with_earnings = [c for c in customers if c.get('pastEarnings', {}).get('amount')]
            if customers_with_earnings:
                top_customers = sorted(customers_with_earnings, 
                                     key=lambda x: float(x['pastEarnings']['amount']), 
                                     reverse=True)[:5]
                
                print(f"\n💰 TOP 5 CUSTOMERS BY EARNINGS:")
                for i, customer in enumerate(top_customers, 1):
                    name = customer.get('userName', 'Unknown')
                    earnings = customer['pastEarnings']['displayAmount']
                    print(f"  {i}. {name}: {earnings}")
    
    if customer_lists_data:
        lists = customer_lists_data.get('edges', [])
        print(f"\n📋 CUSTOMER LISTS: {len(lists)}")
    
    print(f"\n🎉 EXTRACTION COMPLETE!")
    print(f"✅ Customer data ready for Google Voice correlation!")

if __name__ == "__main__":
    extract_customers_working()
