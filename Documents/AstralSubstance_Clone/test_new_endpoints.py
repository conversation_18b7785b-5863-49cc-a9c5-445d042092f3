#!/usr/bin/env python3
"""
Test the newly discovered API endpoints for more comprehensive data
"""

import sys
from pathlib import Path
import requests
import json
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON><PERSON><PERSON>

def test_new_endpoints():
    """Test newly discovered API endpoints"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Uid': '',
        'X-Domain-ID': '1',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://www.keen.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    base_url = keen_config['base_url']
    
    print("🔍 TESTING NEW API ENDPOINTS")
    print("=" * 60)
    
    # Test main API endpoints
    print("\n📊 Testing Main API Endpoints (/api)...")
    test_main_api_endpoints(session, headers, base_url)
    
    # Test Clover API endpoints
    print("\n🍀 Testing Clover API Endpoints...")
    test_clover_api_endpoints(session, headers)
    
    # Test call status endpoint
    print("\n📞 Testing Call Status Endpoint...")
    test_call_status_endpoint(session, headers, base_url)
    
    # Test other GraphQL endpoints
    print("\n🔍 Testing Other GraphQL Endpoints...")
    test_other_graphql_endpoints(session, headers, base_url)

def test_main_api_endpoints(session, headers, base_url):
    """Test main API endpoints"""
    
    endpoints_to_test = [
        '/api/customers',
        '/api/customers/list',
        '/api/sessions',
        '/api/sessions/list',
        '/api/conversations',
        '/api/conversations/list',
        '/api/calls',
        '/api/calls/list',
        '/api/user/customers',
        '/api/advisor/customers',
    ]
    
    for endpoint in endpoints_to_test:
        url = f"{base_url}{endpoint}"
        print(f"  Testing: {endpoint}")
        
        try:
            # Try GET first
            response = session.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                print(f"    ✅ GET Success: {len(response.content)} bytes")
                
                try:
                    data = response.json()
                    print(f"    📄 JSON response with keys: {list(data.keys())[:5]}")
                    
                    # Look for customer data
                    if 'customers' in data or 'customer' in data:
                        print(f"    👥 Contains customer data!")
                    if 'sessions' in data or 'session' in data:
                        print(f"    💬 Contains session data!")
                    if 'calls' in data or 'call' in data:
                        print(f"    📞 Contains call data!")
                        
                except json.JSONDecodeError:
                    print(f"    📄 Non-JSON response: {response.text[:100]}...")
                    
            elif response.status_code == 401:
                print(f"    🔐 Authentication required")
            elif response.status_code == 403:
                print(f"    🚫 Forbidden")
            elif response.status_code == 404:
                print(f"    ❌ Not found")
            elif response.status_code == 405:
                print(f"    ❌ Method not allowed - trying POST...")
                
                # Try POST for endpoints that might require it
                try:
                    post_response = session.post(url, headers=headers, json={}, timeout=15)
                    if post_response.status_code == 200:
                        print(f"    ✅ POST Success: {len(post_response.content)} bytes")
                    else:
                        print(f"    ❌ POST failed: {post_response.status_code}")
                except:
                    print(f"    ❌ POST error")
            else:
                print(f"    ❌ Status: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(0.5)  # Rate limiting

def test_clover_api_endpoints(session, headers):
    """Test Clover API endpoints"""
    
    clover_base = "https://clover-api.ingenio.com"
    
    endpoints_to_test = [
        '/api/customers',
        '/api/sessions',
        '/api/conversations',
        '/api/calls',
        '/api/graphql',
        '/api/graphql2',
    ]
    
    for endpoint in endpoints_to_test:
        url = f"{clover_base}{endpoint}"
        print(f"  Testing: {endpoint}")
        
        try:
            response = session.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                print(f"    ✅ Success: {len(response.content)} bytes")
                
                try:
                    data = response.json()
                    print(f"    📄 JSON response with keys: {list(data.keys())[:5]}")
                except:
                    print(f"    📄 Non-JSON response")
                    
            elif response.status_code in [401, 403]:
                print(f"    🔐 Authentication issue: {response.status_code}")
            elif response.status_code == 404:
                print(f"    ❌ Not found")
            else:
                print(f"    ❌ Status: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(0.5)

def test_call_status_endpoint(session, headers, base_url):
    """Test the call status endpoint"""
    
    endpoint = "/DomainOverrides/Advice/Calls/AjaxWs/CallStatus.asmx/GetStatus"
    url = f"{base_url}{endpoint}"
    
    print(f"  Testing call status endpoint...")
    
    try:
        # This looks like a SOAP/ASMX endpoint, might need different headers
        soap_headers = headers.copy()
        soap_headers['Content-Type'] = 'application/x-www-form-urlencoded'
        
        # Try GET first
        response = session.get(url, headers=headers, timeout=15)
        
        if response.status_code == 200:
            print(f"    ✅ GET Success: {len(response.content)} bytes")
            print(f"    📄 Response preview: {response.text[:200]}...")
        else:
            print(f"    ❌ GET failed: {response.status_code}")
            
            # Try POST with empty data
            post_response = session.post(url, headers=soap_headers, data={}, timeout=15)
            if post_response.status_code == 200:
                print(f"    ✅ POST Success: {len(post_response.content)} bytes")
                print(f"    📄 Response preview: {post_response.text[:200]}...")
            else:
                print(f"    ❌ POST failed: {post_response.status_code}")
                
    except Exception as e:
        print(f"    ❌ Error: {e}")

def test_other_graphql_endpoints(session, headers, base_url):
    """Test other GraphQL endpoints"""
    
    graphql_endpoints = [
        '/api/graphql',
        '/api/graphqlv1',
        '/api/graphqlv3',
    ]
    
    # Simple test query
    test_query = {
        "query": "{ __schema { types { name } } }",
        "variables": {}
    }
    
    for endpoint in graphql_endpoints:
        url = f"{base_url}{endpoint}"
        print(f"  Testing: {endpoint}")
        
        try:
            response = session.post(url, headers=headers, json=test_query, timeout=15)
            
            if response.status_code == 200:
                print(f"    ✅ Success: {len(response.content)} bytes")
                
                try:
                    data = response.json()
                    if 'data' in data and '__schema' in data['data']:
                        types = data['data']['__schema']['types']
                        print(f"    🔍 GraphQL schema with {len(types)} types")
                        
                        # Look for customer-related types
                        customer_types = [t['name'] for t in types if 'customer' in t['name'].lower()]
                        if customer_types:
                            print(f"    👥 Customer types: {customer_types}")
                    else:
                        print(f"    📄 Response keys: {list(data.keys())}")
                        
                except json.JSONDecodeError:
                    print(f"    📄 Non-JSON response")
                    
            elif response.status_code == 404:
                print(f"    ❌ Not found")
            elif response.status_code == 405:
                print(f"    ❌ Method not allowed")
            else:
                print(f"    ❌ Status: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        time.sleep(0.5)

if __name__ == "__main__":
    test_new_endpoints()
