#!/usr/bin/env python3
"""
Test Google Voice access with the provided cookies
"""

import sys
from pathlib import Path
import requests

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Config<PERSON><PERSON><PERSON>

def test_google_voice_access():
    """Test Google Voice access with the provided cookies"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    gvoice_config = config['google_voice']
    
    # Setup session with cookies
    session = requests.Session()
    
    # Add all cookies from config
    cookies = gvoice_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Setup headers (from the curl request)
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'cross-site',
        'Priority': 'u=0, i'
    }
    
    # Test access to Google Voice calls page
    url = 'https://voice.google.com/u/0/calls'
    print(f"Testing Google Voice access: {url}")
    print(f"Cookies loaded: {len(session.cookies)}")
    
    try:
        response = session.get(url, headers=headers, timeout=30)
        print(f"Response status: {response.status_code}")
        print(f"Response URL: {response.url}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Google Voice access successful!")
            
            # Check if we're actually logged in (not redirected to login)
            if 'voice.google.com' in response.url and 'accounts.google.com' not in response.url:
                print("✅ Successfully authenticated to Google Voice!")
                
                # Look for signs of call data in the response
                content = response.text
                print(f"Response content length: {len(content)}")
                
                # Look for common Google Voice patterns
                if 'calls' in content.lower():
                    print("✅ Found 'calls' in response content")
                if 'voice' in content.lower():
                    print("✅ Found 'voice' in response content")
                if 'recording' in content.lower():
                    print("✅ Found 'recording' in response content")
                
                # Look for the Keen.com phone number
                keen_phone = gvoice_config.get('keen_phone_number', '(*************')
                if keen_phone in content or '**********' in content or '************' in content:
                    print(f"✅ Found Keen.com phone number {keen_phone} in response!")
                else:
                    print(f"ℹ️  Keen.com phone number {keen_phone} not found in this page")
                
                # Show a snippet of the content
                print(f"\nContent preview (first 500 chars):")
                print(content[:500])
                print("...")
                
                return True
            else:
                print("❌ Redirected to login page - authentication failed")
                return False
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_google_voice_access()
    sys.exit(0 if success else 1)
