#!/usr/bin/env python3
"""
Scrape the full customer interface at https://www.keen.com/app/myaccount/customers
SAFETY: READ-ONLY operations only, no data modification
"""

import sys
from pathlib import Path
import json
import time
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from webdriver_manager.chrome import ChromeDriverManager

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class CustomerScraper:
    """Scrape customer data from the Keen.com customer management interface"""
    
    def __init__(self):
        self.driver = None
        self.customers = {}
        self.conversation_data = {}
        self.errors = []
        
    def setup_browser(self):
        """Setup Chrome browser with authentication"""
        
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Start browser
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Disable automation detection
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("🌐 Browser started successfully")
        
    def authenticate(self, keen_config):
        """Set authentication cookies"""
        
        base_url = keen_config['base_url']
        
        # Navigate to main page first
        self.driver.get(base_url)
        time.sleep(2)
        
        # Add authentication cookies
        cookies = keen_config['cookies']
        for name, value in cookies.items():
            if value and not value.startswith('YOUR_'):
                try:
                    self.driver.add_cookie({
                        'name': name,
                        'value': value,
                        'domain': '.keen.com'
                    })
                except Exception as e:
                    print(f"  ⚠️  Failed to add cookie {name}: {e}")
        
        print("🔐 Authentication cookies set")
        
    def navigate_to_customers(self, base_url):
        """Navigate to the customer management page"""
        
        customer_url = f"{base_url}/app/myaccount/customers"
        print(f"🌐 Navigating to: {customer_url}")
        
        self.driver.get(customer_url)
        
        # Wait for page to load
        wait = WebDriverWait(self.driver, 15)
        try:
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            print("✅ Customer page loaded")
            
            # Wait for customer list to load
            time.sleep(5)
            return True
            
        except TimeoutException:
            print("❌ Customer page failed to load")
            return False
            
    def extract_customer_list(self):
        """Extract the full customer list from the interface"""
        
        print("👥 Extracting customer list from interface...")
        
        # Try different selectors for customer elements
        customer_selectors = [
            'tr[data-customer-id]',  # Table rows with customer IDs
            '.customer-row',
            '.customer-item',
            '.list-item[data-id]',
            'tbody tr',  # Generic table rows
            '[data-test*="customer"]',
            '.customer-list-item',
        ]
        
        customers_found = []
        
        for selector in customer_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"  ✅ Found {len(elements)} elements with selector: {selector}")
                    
                    for element in elements:
                        customer_data = self.extract_customer_from_element(element)
                        if customer_data:
                            customers_found.append(customer_data)
                    
                    if customers_found:
                        break  # Found customers, use this selector
                        
            except Exception as e:
                print(f"  ⚠️  Error with selector {selector}: {e}")
        
        if not customers_found:
            # Try to find any clickable customer elements
            print("  🔍 Trying to find clickable customer elements...")
            customers_found = self.find_clickable_customers()
        
        print(f"  📊 Total customers found: {len(customers_found)}")
        return customers_found
        
    def extract_customer_from_element(self, element):
        """Extract customer data from a DOM element"""
        
        try:
            customer_data = {}
            
            # Try to get customer ID from various attributes
            customer_id = (
                element.get_attribute('data-customer-id') or
                element.get_attribute('data-id') or
                element.get_attribute('id')
            )
            
            if customer_id:
                customer_data['id'] = customer_id
            
            # Extract text content
            text = element.text.strip()
            if text:
                customer_data['display_text'] = text
                
                # Try to extract username/name from text
                lines = text.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('$') and len(line) > 2:
                        if 'username' not in customer_data:
                            customer_data['username'] = line
                        break
            
            # Store the element for later clicking
            customer_data['element'] = element
            customer_data['clickable'] = element.is_enabled() and element.is_displayed()
            
            return customer_data if customer_id or text else None
            
        except Exception as e:
            return None
            
    def find_clickable_customers(self):
        """Find clickable customer elements by looking for patterns"""
        
        customers = []
        
        # Look for any clickable elements that might be customers
        clickable_selectors = [
            'a[href*="customer"]',
            'button[data-customer]',
            '.clickable[data-id]',
            'tr[onclick]',
            'div[onclick]',
        ]
        
        for selector in clickable_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    customer_data = self.extract_customer_from_element(element)
                    if customer_data:
                        customers.append(customer_data)
            except:
                pass
        
        return customers
        
    def scrape_customer_details(self, customer_list):
        """Scrape detailed information for each customer"""
        
        print(f"📋 Scraping details for {len(customer_list)} customers...")
        
        for i, customer in enumerate(customer_list):
            print(f"\n  👤 Customer {i+1}/{len(customer_list)}: {customer.get('username', 'Unknown')}")
            
            try:
                # Click on the customer to view details
                if self.click_customer(customer):
                    # Extract customer details and conversations
                    details = self.extract_customer_details()
                    conversations = self.extract_conversations()
                    
                    # Store the data
                    customer_id = customer.get('id', f'customer_{i+1}')
                    self.customers[customer_id] = {
                        'basic_info': customer,
                        'details': details,
                        'conversations': conversations,
                        'extraction_timestamp': datetime.now().isoformat()
                    }
                    
                    print(f"    ✅ Extracted {len(conversations)} conversations")
                    
                    # Navigate back to customer list
                    self.navigate_back_to_list()
                else:
                    print(f"    ❌ Could not click on customer")
                    
            except Exception as e:
                error_msg = f"Error processing customer {i+1}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)
                
                # Try to recover by going back to customer list
                try:
                    self.navigate_back_to_list()
                except:
                    pass
            
            # Rate limiting
            time.sleep(2)
            
            # Save progress every 10 customers
            if (i + 1) % 10 == 0:
                self.save_progress()
        
        print(f"\n📊 Scraping complete! Extracted data for {len(self.customers)} customers")
        
    def click_customer(self, customer):
        """Click on a customer to view their details"""
        
        try:
            element = customer.get('element')
            if not element:
                return False
            
            # Scroll element into view
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(1)
            
            # Try to click
            element.click()
            
            # Wait for customer details page to load
            time.sleep(3)
            
            # Check if we're on a customer details page
            current_url = self.driver.current_url
            if 'customer' in current_url.lower() or 'user' in current_url.lower():
                return True
            else:
                # Try to find and click a more specific link
                return self.try_alternative_click(element)
                
        except ElementClickInterceptedException:
            # Try JavaScript click
            try:
                self.driver.execute_script("arguments[0].click();", element)
                time.sleep(3)
                return True
            except:
                return False
        except Exception as e:
            return False
            
    def try_alternative_click(self, parent_element):
        """Try alternative ways to click on customer"""
        
        try:
            # Look for clickable child elements
            clickable_children = parent_element.find_elements(By.CSS_SELECTOR, 'a, button, [onclick]')
            
            for child in clickable_children:
                try:
                    child.click()
                    time.sleep(3)
                    
                    current_url = self.driver.current_url
                    if 'customer' in current_url.lower() or 'user' in current_url.lower():
                        return True
                except:
                    continue
                    
        except:
            pass
        
        return False
        
    def extract_customer_details(self):
        """Extract detailed customer information from the current page"""
        
        details = {}
        
        try:
            # Extract customer name/username
            name_selectors = [
                'h1', 'h2', '.customer-name', '.user-name', 
                '[data-test="customer-name"]', '.profile-name'
            ]
            
            for selector in name_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.text.strip():
                        details['name'] = element.text.strip()
                        break
                except:
                    continue
            
            # Extract customer email
            email_selectors = [
                '[data-test="email"]', '.email', '.customer-email',
                'input[type="email"]', '[href^="mailto:"]'
            ]
            
            for selector in email_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    email_text = element.text or element.get_attribute('value') or element.get_attribute('href')
                    if email_text and '@' in email_text:
                        details['email'] = email_text.replace('mailto:', '')
                        break
                except:
                    continue
            
            # Extract other customer information
            info_selectors = [
                '.customer-info', '.user-info', '.profile-info',
                '.customer-details', '.user-details'
            ]
            
            for selector in info_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if text:
                            details['additional_info'] = details.get('additional_info', [])
                            details['additional_info'].append(text)
                except:
                    continue
            
            # Extract any data attributes
            body = self.driver.find_element(By.TAG_NAME, 'body')
            for attr in body.get_property('attributes'):
                attr_name = attr.get('name', '')
                if 'customer' in attr_name or 'user' in attr_name:
                    details[attr_name] = attr.get('value')
            
        except Exception as e:
            details['extraction_error'] = str(e)
        
        return details
        
    def extract_conversations(self):
        """Extract conversation/session data from the current customer page"""
        
        conversations = []
        
        try:
            # Look for conversation/session elements
            conversation_selectors = [
                '.conversation', '.session', '.chat', '.call',
                '.message-thread', '.conversation-item', '.session-item',
                'tr[data-session]', '[data-conversation-id]'
            ]
            
            for selector in conversation_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"      📞 Found {len(elements)} conversations with selector: {selector}")
                        
                        for element in elements:
                            conversation = self.extract_conversation_from_element(element)
                            if conversation:
                                conversations.append(conversation)
                        
                        break  # Found conversations, use this selector
                        
                except Exception as e:
                    continue
            
            # If no conversations found, look for any session/call data
            if not conversations:
                conversations = self.extract_session_data_alternative()
            
        except Exception as e:
            conversations.append({'extraction_error': str(e)})
        
        return conversations
        
    def extract_conversation_from_element(self, element):
        """Extract conversation data from a DOM element"""
        
        try:
            conversation = {}
            
            # Extract conversation ID
            conv_id = (
                element.get_attribute('data-conversation-id') or
                element.get_attribute('data-session-id') or
                element.get_attribute('data-id')
            )
            
            if conv_id:
                conversation['id'] = conv_id
            
            # Extract conversation text/content
            text = element.text.strip()
            if text:
                conversation['content'] = text
                
                # Try to extract timestamp
                timestamp_patterns = [
                    r'\d{1,2}/\d{1,2}/\d{4}',  # MM/DD/YYYY
                    r'\d{4}-\d{2}-\d{2}',      # YYYY-MM-DD
                    r'\d{1,2}:\d{2}',          # HH:MM
                ]
                
                for pattern in timestamp_patterns:
                    matches = re.findall(pattern, text)
                    if matches:
                        conversation['timestamp'] = matches[0]
                        break
                
                # Try to extract duration
                duration_pattern = r'(\d+:\d+|\d+\s*min)'
                duration_matches = re.findall(duration_pattern, text, re.IGNORECASE)
                if duration_matches:
                    conversation['duration'] = duration_matches[0]
            
            # Look for nested message elements
            message_elements = element.find_elements(By.CSS_SELECTOR, '.message, .chat-message, .conversation-message')
            if message_elements:
                messages = []
                for msg_elem in message_elements:
                    message_data = self.extract_message_from_element(msg_elem)
                    if message_data:
                        messages.append(message_data)
                
                if messages:
                    conversation['messages'] = self.resolve_message_order(messages)
            
            return conversation if conv_id or text else None
            
        except Exception as e:
            return {'extraction_error': str(e)}
            
    def extract_message_from_element(self, element):
        """Extract individual message data"""
        
        try:
            message = {}
            
            # Extract message text
            text = element.text.strip()
            if text:
                message['content'] = text
            
            # Extract timestamp
            timestamp_elem = element.find_element(By.CSS_SELECTOR, '.timestamp, .time, [data-time]')
            if timestamp_elem:
                message['timestamp'] = timestamp_elem.text or timestamp_elem.get_attribute('data-time')
            
            # Extract sender information
            sender_elem = element.find_element(By.CSS_SELECTOR, '.sender, .author, [data-sender]')
            if sender_elem:
                message['sender'] = sender_elem.text or sender_elem.get_attribute('data-sender')
            
            # Determine if message is from advisor or customer
            classes = element.get_attribute('class') or ''
            if 'advisor' in classes.lower() or 'psychic' in classes.lower():
                message['sender_type'] = 'advisor'
            elif 'customer' in classes.lower() or 'user' in classes.lower():
                message['sender_type'] = 'customer'
            
            return message if text else None
            
        except:
            return None
            
    def resolve_message_order(self, messages):
        """Resolve out-of-order messages in conversations"""
        
        print("      🔄 Resolving message order...")
        
        # Try to sort by timestamp if available
        timestamped_messages = [msg for msg in messages if msg.get('timestamp')]
        non_timestamped_messages = [msg for msg in messages if not msg.get('timestamp')]
        
        if timestamped_messages:
            try:
                # Sort by timestamp
                timestamped_messages.sort(key=lambda x: self.parse_timestamp(x['timestamp']))
                
                # Add sequence numbers
                for i, msg in enumerate(timestamped_messages):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = True
                
                print(f"        ✅ Ordered {len(timestamped_messages)} messages by timestamp")
                
            except Exception as e:
                print(f"        ⚠️  Could not sort by timestamp: {e}")
                # Fall back to original order
                for i, msg in enumerate(timestamped_messages):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = False
        
        # Handle non-timestamped messages
        for i, msg in enumerate(non_timestamped_messages):
            msg['sequence'] = len(timestamped_messages) + i + 1
            msg['order_resolved'] = False
        
        # Combine and return
        all_messages = timestamped_messages + non_timestamped_messages
        
        # Add conversation flow analysis
        self.analyze_conversation_flow(all_messages)
        
        return all_messages
        
    def parse_timestamp(self, timestamp_str):
        """Parse timestamp string to datetime for sorting"""
        
        # Try different timestamp formats
        formats = [
            '%m/%d/%Y %H:%M',
            '%Y-%m-%d %H:%M',
            '%m/%d/%Y',
            '%Y-%m-%d',
            '%H:%M',
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str, fmt)
            except:
                continue
        
        # If no format works, return current time (will sort to end)
        return datetime.now()
        
    def analyze_conversation_flow(self, messages):
        """Analyze conversation flow to identify potential ordering issues"""
        
        # Look for patterns that indicate out-of-order messages
        for i, msg in enumerate(messages):
            # Check for response patterns
            content = msg.get('content', '').lower()
            
            # Mark questions
            if '?' in content or any(word in content for word in ['what', 'how', 'when', 'where', 'why', 'can you']):
                msg['message_type'] = 'question'
            
            # Mark responses/answers
            elif any(phrase in content for phrase in ['yes', 'no', 'i think', 'i see', 'that means']):
                msg['message_type'] = 'response'
            
            # Mark greetings
            elif any(word in content for word in ['hello', 'hi', 'good morning', 'good evening']):
                msg['message_type'] = 'greeting'
            
            # Check for potential ordering issues
            if i > 0:
                prev_msg = messages[i-1]
                
                # Flag potential issues
                if (msg.get('message_type') == 'response' and 
                    prev_msg.get('message_type') == 'response'):
                    msg['potential_order_issue'] = 'consecutive_responses'
                
                if (msg.get('message_type') == 'question' and 
                    prev_msg.get('message_type') == 'question' and
                    msg.get('sender_type') == prev_msg.get('sender_type')):
                    msg['potential_order_issue'] = 'consecutive_questions_same_sender'
        
        return messages
