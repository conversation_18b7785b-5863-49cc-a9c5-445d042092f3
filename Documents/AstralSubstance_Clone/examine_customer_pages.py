#!/usr/bin/env python3
"""
Examine customer management pages to find additional API endpoints
"""

import sys
from pathlib import Path
import requests
import re
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Confi<PERSON><PERSON><PERSON><PERSON>

def examine_customer_pages():
    """Examine customer management pages for API endpoints"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    base_url = keen_config['base_url']
    
    print("🔍 EXAMINING CUSTOMER MANAGEMENT PAGES")
    print("=" * 60)
    
    # Pages to examine
    pages_to_examine = [
        '/app/myaccount/customers',
        '/app/myaccount/customer-list', 
        '/app/myaccount/conversations',
        '/app/myaccount/calls',
    ]
    
    for page_path in pages_to_examine:
        print(f"\n📄 Examining: {page_path}")
        examine_page(session, headers, base_url, page_path)

def examine_page(session, headers, base_url, page_path):
    """Examine a specific page for API endpoints and GraphQL queries"""
    
    url = f"{base_url}{page_path}"
    
    try:
        response = session.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            content = response.text
            print(f"  ✅ Loaded page ({len(content)} chars)")
            
            # Extract GraphQL queries
            graphql_queries = extract_graphql_queries(content)
            if graphql_queries:
                print(f"  🔍 Found {len(graphql_queries)} GraphQL queries:")
                for i, query in enumerate(graphql_queries[:3]):  # Show first 3
                    print(f"    Query {i+1}: {query[:100]}...")
            
            # Extract API endpoints
            api_endpoints = extract_api_endpoints(content)
            if api_endpoints:
                print(f"  🔗 Found {len(api_endpoints)} API endpoints:")
                for endpoint in api_endpoints[:5]:  # Show first 5
                    print(f"    - {endpoint}")
            
            # Extract JavaScript variables that might contain data
            js_variables = extract_js_variables(content)
            if js_variables:
                print(f"  📊 Found {len(js_variables)} JavaScript variables:")
                for var_name, var_value in list(js_variables.items())[:3]:
                    print(f"    - {var_name}: {str(var_value)[:50]}...")
            
            # Look for customer-related patterns
            customer_patterns = find_customer_patterns(content)
            if customer_patterns:
                print(f"  👥 Found customer-related patterns:")
                for pattern in customer_patterns[:5]:
                    print(f"    - {pattern}")
            
            # Save the page content for manual inspection
            save_path = f"data/page_content_{page_path.replace('/', '_')}.html"
            Path("data").mkdir(exist_ok=True)
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  💾 Saved content to: {save_path}")
            
        else:
            print(f"  ❌ Failed to load: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ Error: {e}")

def extract_graphql_queries(content):
    """Extract GraphQL queries from page content"""
    
    queries = []
    
    # Pattern 1: query strings in JavaScript
    query_patterns = [
        r'query\s*[`"\']\s*query\s*\([^`"\']*\)\s*\{[^`"\']*\}[`"\']',
        r'query:\s*[`"\']\s*query[^`"\']*[`"\']',
        r'gql\s*`\s*query[^`]*`',
        r'"query"\s*:\s*"[^"]*query[^"]*"',
    ]
    
    for pattern in query_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
        queries.extend(matches)
    
    # Clean up queries
    cleaned_queries = []
    for query in queries:
        # Remove quotes and backticks
        cleaned = re.sub(r'^[`"\']|[`"\']$', '', query.strip())
        if len(cleaned) > 20:  # Only keep substantial queries
            cleaned_queries.append(cleaned)
    
    return list(set(cleaned_queries))  # Remove duplicates

def extract_api_endpoints(content):
    """Extract API endpoints from page content"""
    
    endpoints = []
    
    # Pattern for API URLs
    api_patterns = [
        r'/api/[a-zA-Z0-9/_-]+',
        r'https?://[^/]+/api/[a-zA-Z0-9/_-]+',
        r'fetch\s*\(\s*[`"\']/[^`"\']*[`"\']',
        r'axios\.[a-z]+\s*\(\s*[`"\']/[^`"\']*[`"\']',
    ]
    
    for pattern in api_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        endpoints.extend(matches)
    
    # Clean up endpoints
    cleaned_endpoints = []
    for endpoint in endpoints:
        # Extract just the path
        cleaned = re.sub(r'^.*?(/api/[^`"\'\s)]+).*$', r'\1', endpoint)
        if cleaned.startswith('/api/'):
            cleaned_endpoints.append(cleaned)
    
    return list(set(cleaned_endpoints))  # Remove duplicates

def extract_js_variables(content):
    """Extract JavaScript variables that might contain useful data"""
    
    variables = {}
    
    # Pattern for variable assignments
    var_patterns = [
        r'var\s+(\w+)\s*=\s*(\{[^}]*\}|\[[^\]]*\]|"[^"]*"|\'[^\']*\'|\d+)',
        r'let\s+(\w+)\s*=\s*(\{[^}]*\}|\[[^\]]*\]|"[^"]*"|\'[^\']*\'|\d+)',
        r'const\s+(\w+)\s*=\s*(\{[^}]*\}|\[[^\]]*\]|"[^"]*"|\'[^\']*\'|\d+)',
        r'window\.(\w+)\s*=\s*(\{[^}]*\}|\[[^\]]*\]|"[^"]*"|\'[^\']*\'|\d+)',
    ]
    
    for pattern in var_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for var_name, var_value in matches:
            # Only keep variables that might be relevant
            if any(keyword in var_name.lower() for keyword in ['customer', 'user', 'api', 'config', 'data', 'endpoint']):
                try:
                    # Try to parse JSON values
                    if var_value.startswith(('{', '[')):
                        parsed_value = json.loads(var_value)
                        variables[var_name] = parsed_value
                    else:
                        variables[var_name] = var_value.strip('"\'')
                except:
                    variables[var_name] = var_value
    
    return variables

def find_customer_patterns(content):
    """Find patterns related to customer data"""
    
    patterns = []
    
    # Look for customer-related terms
    customer_terms = [
        r'customer[A-Z]\w*',  # camelCase customer variables
        r'Customer[A-Z]\w*',  # PascalCase customer classes
        r'customer[-_]\w+',   # kebab/snake case
        r'customerId',
        r'customerData',
        r'customerList',
        r'customerInfo',
        r'getCustomer',
        r'fetchCustomer',
        r'loadCustomer',
    ]
    
    for term_pattern in customer_terms:
        matches = re.findall(term_pattern, content)
        patterns.extend(matches)
    
    # Look for GraphQL field names
    graphql_fields = [
        r'customers\s*\{[^}]*\}',
        r'customer\s*\([^)]*\)\s*\{[^}]*\}',
        r'user\s*\{[^}]*\}',
        r'users\s*\{[^}]*\}',
    ]
    
    for field_pattern in graphql_fields:
        matches = re.findall(field_pattern, content, re.DOTALL)
        patterns.extend(matches)
    
    return list(set(patterns))  # Remove duplicates

if __name__ == "__main__":
    examine_customer_pages()
