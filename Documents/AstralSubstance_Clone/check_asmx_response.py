#!/usr/bin/env python3
"""
Check what the ASMX endpoints are actually returning
"""

import sys
from pathlib import Path
import requests
import xml.etree.ElementTree as ET

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import <PERSON>fi<PERSON><PERSON><PERSON><PERSON>

def check_asmx_response():
    """Check what the ASMX endpoints are returning"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    base_url = keen_config['base_url']
    
    print("🔍 CHECKING ASMX RESPONSE CONTENT")
    print("=" * 60)
    
    # Test the customer list endpoint
    endpoint = f"{base_url}/DomainOverrides/Advice/Customers/List.asmx"
    
    try:
        response = session.post(endpoint, headers=headers, json={}, timeout=30)
        
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type')}")
        print(f"Content-Length: {len(response.content)}")
        print(f"\nRaw Response:")
        print("=" * 40)
        print(response.text)
        print("=" * 40)
        
        # Try to parse as XML
        try:
            root = ET.fromstring(response.text)
            print(f"\n✅ Valid XML!")
            print(f"Root tag: {root.tag}")
            print(f"Root attributes: {root.attrib}")
            print(f"Root text: {root.text}")
            
            # Print all child elements
            for child in root:
                print(f"Child: {child.tag} = {child.text}")
                
        except ET.ParseError as e:
            print(f"\n❌ XML parsing error: {e}")
            
        # Also try different Content-Type headers for ASMX
        print(f"\n🔄 Trying with SOAP headers...")
        
        soap_headers = headers.copy()
        soap_headers['Content-Type'] = 'text/xml; charset=utf-8'
        soap_headers['SOAPAction'] = '""'
        
        soap_response = session.post(endpoint, headers=soap_headers, data='', timeout=30)
        
        print(f"SOAP Status: {soap_response.status_code}")
        print(f"SOAP Content-Length: {len(soap_response.content)}")
        print(f"SOAP Response:")
        print("=" * 40)
        print(soap_response.text)
        print("=" * 40)
        
        # Try GET request to see available methods
        print(f"\n🔄 Trying GET request to see available methods...")
        
        get_response = session.get(endpoint, headers=headers, timeout=30)
        
        print(f"GET Status: {get_response.status_code}")
        print(f"GET Content-Length: {len(get_response.content)}")
        
        if get_response.status_code == 200:
            print(f"GET Response (first 1000 chars):")
            print("=" * 40)
            print(get_response.text[:1000])
            print("=" * 40)
            
            # Look for method names in the response
            import re
            methods = re.findall(r'<a[^>]*>([^<]+)</a>', get_response.text)
            if methods:
                print(f"\n📋 Available methods:")
                for method in methods:
                    print(f"  - {method}")
                    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_asmx_response()
