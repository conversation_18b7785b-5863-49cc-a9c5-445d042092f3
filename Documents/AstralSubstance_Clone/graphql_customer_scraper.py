#!/usr/bin/env python3
"""
GraphQL-based customer scraper using the discovered endpoint
Fetches all customers using pagination (100 at a time)
"""

import sys
from pathlib import Path
import requests
import json
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class GraphQLCustomerScraper:
    """Scrape all customers using the GraphQL endpoint"""

    def __init__(self):
        self.customers = {}
        self.total_count = 0
        self.errors = []

    def run_graphql_scrape(self):
        """Run the complete GraphQL scraping process"""

        print("🚀 GRAPHQL CUSTOMER SCRAPER")
        print("=" * 60)
        print("📊 Using discovered GraphQL endpoint with pagination")
        print("📦 Fetching 100 customers at a time")
        print("=" * 60)

        # Load config
        config = ConfigLoader.load_config("config/config.yaml")
        keen_config = config['keen']

        # Setup session with cookies
        session = requests.Session()
        cookies = keen_config['cookies']
        for name, value in cookies.items():
            if value and not value.startswith('YOUR_'):
                session.cookies.set(name, value)

        # Setup headers (using the exact headers from your curl command)
        headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'X-EpcApi-ID': '3a5d5fd4-cb38-f011-bf3f-98f2b31428e6',
            'X-Uid': '',
            'X-Domain-ID': '1',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'Origin': 'https://www.keen.com',
            'Connection': 'keep-alive',
            'Referer': 'https://www.keen.com/app/myaccount/customers',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Priority': 'u=0',
            'TE': 'trailers'
        }

        # GraphQL endpoint
        graphql_url = f"{keen_config['base_url']}/api/graphqlv0"

        # First request to get total count
        print("\n📊 Getting total customer count...")
        total_count = self.get_total_customer_count(session, headers, graphql_url)

        if total_count > 0:
            print(f"✅ Found {total_count} total customers")

            # Fetch all customers with pagination
            print(f"\n👥 Fetching all {total_count} customers...")
            self.fetch_all_customers(session, headers, graphql_url, total_count)

            # Process and save data
            print(f"\n📊 Processing and saving customer data...")
            self.process_and_save_data()

        else:
            print("❌ Could not determine total customer count")

    def get_total_customer_count(self, session, headers, graphql_url):
        """Get the total number of customers"""

        # Use the exact working query from debug
        query = {
            "query": "query ($listId: Int, $first: Int, $after: String, $last: Int, $before: String, $filter: String, $sortBy: [String], $sortDescending: [Boolean], $fetch: Int, $offset: Int) { user { id customers(first: $first, after: $after, last: $last, before: $before, filter: $filter, sortBy: $sortBy, sortDescending: $sortDescending, listId: $listId, fetch: $fetch, offset: $offset) { totalCount pageInfo { hasNextPage hasPreviousPage } edges { cursor node { id userName nickname customerSince alerts { name } contacts { last { id activityId mailId masterTransactionId date amount { amount displayAmount(format: \"c2\") } type } } cumulativeSummary { totalCallCount totalChatCount totalPaidMails totalEarnings { amount displayAmount(format: \"c2\") } } list { id name } } } } } }",
            "variables": {
                "offset": 0,
                "fetch": 1,  # Just fetch 1 to get the count
                "filter": "",
                "sortBy": ["contacts.last.date"],
                "sortDescending": [True],
                "listId": 0
            }
        }

        try:
            response = session.post(graphql_url, headers=headers, json=query, timeout=30)

            if response.status_code == 200:
                data = response.json()

                if 'errors' in data:
                    print(f"❌ GraphQL errors: {data['errors']}")
                    return 0

                if 'data' in data and data['data']['user']['customers']:
                    total_count = data['data']['user']['customers']['totalCount']
                    return total_count
                else:
                    print(f"❌ Unexpected response structure: {data}")
                    return 0
            else:
                print(f"❌ HTTP error: {response.status_code}")
                return 0

        except Exception as e:
            print(f"❌ Error getting total count: {e}")
            return 0

    def fetch_all_customers(self, session, headers, graphql_url, total_count):
        """Fetch all customers using pagination"""

        # Use the exact working query from debug
        query_template = {
            "query": "query ($listId: Int, $first: Int, $after: String, $last: Int, $before: String, $filter: String, $sortBy: [String], $sortDescending: [Boolean], $fetch: Int, $offset: Int) { user { id customers(first: $first, after: $after, last: $last, before: $before, filter: $filter, sortBy: $sortBy, sortDescending: $sortDescending, listId: $listId, fetch: $fetch, offset: $offset) { totalCount pageInfo { hasNextPage hasPreviousPage } edges { cursor node { id userName nickname customerSince alerts { name } contacts { last { id activityId mailId masterTransactionId date amount { amount displayAmount(format: \"c2\") } type } } cumulativeSummary { totalCallCount totalChatCount totalPaidMails totalEarnings { amount displayAmount(format: \"c2\") } } list { id name } } } } } }",
            "variables": {
                "offset": 0,
                "fetch": 100,
                "filter": "",
                "sortBy": ["contacts.last.date"],
                "sortDescending": [True],
                "listId": 0
            }
        }

        offset = 0
        batch_size = 100
        customers_fetched = 0

        while customers_fetched < total_count:
            print(f"  📦 Fetching customers {customers_fetched + 1}-{min(customers_fetched + batch_size, total_count)} of {total_count}")

            # Update offset for this batch
            query = query_template.copy()
            query["variables"]["offset"] = offset

            try:
                response = session.post(graphql_url, headers=headers, json=query, timeout=30)

                if response.status_code == 200:
                    data = response.json()

                    if 'errors' in data:
                        print(f"    ❌ GraphQL errors: {data['errors']}")
                        self.errors.append(f"GraphQL errors at offset {offset}: {data['errors']}")
                        break

                    if 'data' in data and data['data']['user']['customers']:
                        customers_data = data['data']['user']['customers']
                        edges = customers_data.get('edges', [])

                        print(f"    ✅ Received {len(edges)} customers")

                        # Process customers from this batch
                        for edge in edges:
                            customer = edge['node']
                            customer_id = customer['id']

                            # Add batch info
                            customer['batch_offset'] = offset
                            customer['extraction_timestamp'] = datetime.now().isoformat()

                            self.customers[customer_id] = customer
                            customers_fetched += 1

                        # Check if we have more pages
                        has_next_page = customers_data.get('pageInfo', {}).get('hasNextPage', False)

                        if not edges or not has_next_page:
                            print(f"    ℹ️  No more customers to fetch")
                            break

                        # Move to next batch
                        offset += batch_size

                        # Rate limiting
                        time.sleep(1)

                    else:
                        print(f"    ❌ Unexpected response structure")
                        break

                else:
                    print(f"    ❌ HTTP error: {response.status_code}")
                    self.errors.append(f"HTTP error {response.status_code} at offset {offset}")
                    break

            except Exception as e:
                print(f"    ❌ Error fetching batch at offset {offset}: {e}")
                self.errors.append(f"Error at offset {offset}: {e}")
                break

        print(f"\n📊 Fetching complete! Retrieved {len(self.customers)} customers")

    def process_and_save_data(self):
        """Process and save the customer data"""

        if not self.customers:
            print("❌ No customer data to process")
            return

        # Analyze data quality
        customers_list = list(self.customers.values())

        with_username = sum(1 for c in customers_list if c.get('userName'))
        with_nickname = sum(1 for c in customers_list if c.get('nickname'))
        with_contacts = sum(1 for c in customers_list if c.get('contacts', {}).get('last'))
        with_earnings = sum(1 for c in customers_list if c.get('cumulativeSummary', {}).get('totalEarnings'))

        print(f"\n📊 DATA QUALITY ANALYSIS:")
        print(f"  Total customers: {len(customers_list)}")
        print(f"  With username: {with_username}/{len(customers_list)} ({with_username/len(customers_list)*100:.1f}%)")
        print(f"  With nickname: {with_nickname}/{len(customers_list)} ({with_nickname/len(customers_list)*100:.1f}%)")
        print(f"  With contact history: {with_contacts}/{len(customers_list)} ({with_contacts/len(customers_list)*100:.1f}%)")
        print(f"  With earnings data: {with_earnings}/{len(customers_list)} ({with_earnings/len(customers_list)*100:.1f}%)")

        # Show sample customers
        print(f"\n👤 SAMPLE CUSTOMERS:")
        for i, customer in enumerate(customers_list[:5], 1):
            customer_id = customer.get('id', 'Unknown ID')
            username = customer.get('userName', 'Unknown')
            nickname = customer.get('nickname', 'No nickname')
            earnings = customer.get('cumulativeSummary', {}).get('totalEarnings', {}).get('displayAmount', 'No earnings')
            print(f"  {i}. ID: {customer_id}, Username: {username}, Nickname: {nickname}, Earnings: {earnings}")

        # Create comprehensive dataset
        dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'graphql_pagination',
                'total_customers': len(customers_list),
                'source_endpoint': '/api/graphqlv0',
                'data_quality': {
                    'with_username': with_username,
                    'with_nickname': with_nickname,
                    'with_contacts': with_contacts,
                    'with_earnings': with_earnings
                }
            },
            'customers': self.customers,
            'errors': self.errors
        }

        # Save complete dataset
        dataset_file = Path("data/graphql_complete_customers.json")
        with open(dataset_file, 'w') as f:
            json.dump(dataset, f, indent=2)
        print(f"\n💾 Complete dataset saved to: {dataset_file}")

        # Create LLM-ready format
        self.create_llm_format(customers_list)

        # Generate summary report
        self.generate_summary_report(dataset)

    def create_llm_format(self, customers_list):
        """Create LLM training format"""

        print(f"\n🤖 Creating LLM training format...")

        # Format customers for LLM training
        llm_customers = []

        for customer in customers_list:
            # Extract key information for LLM training
            llm_customer = {
                'customer_id': customer.get('id'),
                'username': customer.get('userName'),
                'nickname': customer.get('nickname'),
                'customer_since': customer.get('customerSince'),
                'contact_history': customer.get('contacts', {}),
                'summary': customer.get('cumulativeSummary', {}),
                'alerts': customer.get('alerts', []),
                'list_info': customer.get('list', {})
            }

            # Add conversation context if available
            if customer.get('contacts', {}).get('last'):
                last_contact = customer['contacts']['last']
                llm_customer['last_interaction'] = {
                    'date': last_contact.get('date'),
                    'type': last_contact.get('type'),
                    'transaction_id': last_contact.get('masterTransactionId'),
                    'amount': last_contact.get('amount', {})
                }

            llm_customers.append(llm_customer)

        # Save LLM format
        llm_dataset = {
            'format': 'customer_profiles',
            'version': '1.0',
            'metadata': {
                'total_customers': len(llm_customers),
                'extraction_timestamp': datetime.now().isoformat(),
                'source': 'keen.com_graphql_api'
            },
            'customers': llm_customers
        }

        llm_file = Path("data/graphql_customers_llm_format.json")
        with open(llm_file, 'w') as f:
            json.dump(llm_dataset, f, indent=2)

        print(f"🤖 LLM format saved to: {llm_file}")
        print(f"📊 LLM customers: {len(llm_customers)}")

    def generate_summary_report(self, dataset):
        """Generate summary report"""

        metadata = dataset['metadata']
        quality = metadata['data_quality']

        report = f"""
GRAPHQL CUSTOMER EXTRACTION REPORT
==================================

EXTRACTION SUMMARY:
- Timestamp: {metadata['extraction_timestamp']}
- Method: {metadata['extraction_method']}
- Source: {metadata['source_endpoint']}
- Total Customers: {metadata['total_customers']}

DATA QUALITY METRICS:
- Customers with username: {quality['with_username']}/{metadata['total_customers']} ({quality['with_username']/metadata['total_customers']*100:.1f}%)
- Customers with nickname: {quality['with_nickname']}/{metadata['total_customers']} ({quality['with_nickname']/metadata['total_customers']*100:.1f}%)
- Customers with contact history: {quality['with_contacts']}/{metadata['total_customers']} ({quality['with_contacts']/metadata['total_customers']*100:.1f}%)
- Customers with earnings data: {quality['with_earnings']}/{metadata['total_customers']} ({quality['with_earnings']/metadata['total_customers']*100:.1f}%)

ERRORS ENCOUNTERED:
{chr(10).join(f"- {error}" for error in dataset['errors']) if dataset['errors'] else "- None"}

NEXT STEPS:
1. Use customer IDs and transaction IDs for Google Voice correlation
2. Extract call recordings using phone number (*************
3. Transcribe recordings with Whisper
4. Correlate conversations with customer profiles
5. Create comprehensive LLM training dataset

FILES CREATED:
- graphql_complete_customers.json: Complete customer data
- graphql_customers_llm_format.json: LLM-ready format
- graphql_extraction_report.txt: This report
"""

        # Save report
        report_file = Path("data/graphql_extraction_report.txt")
        with open(report_file, 'w') as f:
            f.write(report)

        print(f"\n📋 EXTRACTION COMPLETE!")
        print(f"📊 Successfully extracted {metadata['total_customers']} customers")
        print(f"📋 Full report saved to: {report_file}")

if __name__ == "__main__":
    scraper = GraphQLCustomerScraper()
    scraper.run_graphql_scrape()
