#!/usr/bin/env python3
"""
Whisper transcriber for Google Voice recordings
"""

import sys
from pathlib import Path
import json
import time
from datetime import datetime
import subprocess
import os

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

class WhisperTranscriber:
    """Transcribe Google Voice recordings using Whisper"""
    
    def __init__(self):
        self.transcriptions = {}
        self.errors = []
        self.progress_file = Path("data/whisper_progress.json")
        
    def run_transcription(self):
        """Run the complete transcription process"""
        
        print("🎤 WHISPER TRANSCRIBER")
        print("=" * 60)
        print("🎵 Transcribing Google Voice recordings")
        print("🤖 Using OpenAI Whisper for speech-to-text")
        print("=" * 60)
        
        # Check if Whisper is installed
        if not self.check_whisper_installation():
            print("❌ Whisper not installed. Installing...")
            self.install_whisper()
        
        # Load Google Voice data
        if not self.load_voice_data():
            print("❌ Could not load Google Voice data")
            return
        
        # Transcribe all recordings
        self.transcribe_all_recordings()
        
        # Save transcription results
        self.save_transcription_data()
    
    def check_whisper_installation(self):
        """Check if Whisper is installed"""
        
        try:
            result = subprocess.run(['whisper', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Whisper installed: {result.stdout.strip()}")
                return True
        except:
            pass
        
        return False
    
    def install_whisper(self):
        """Install Whisper"""
        
        try:
            print("📦 Installing Whisper...")
            
            # Install whisper
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', 'openai-whisper'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ Whisper installed successfully")
                return True
            else:
                print(f"❌ Error installing Whisper: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error installing Whisper: {e}")
            return False
    
    def load_voice_data(self):
        """Load Google Voice data"""
        
        voice_file = Path("data/google_voice_complete.json")
        
        if not voice_file.exists():
            print(f"❌ Google Voice data file not found: {voice_file}")
            return False
        
        try:
            with open(voice_file, 'r') as f:
                self.voice_data = json.load(f)
            
            recordings = self.voice_data.get('recordings', {})
            print(f"✅ Loaded {len(recordings)} recordings for transcription")
            return True
            
        except Exception as e:
            print(f"❌ Error loading voice data: {e}")
            return False
    
    def transcribe_all_recordings(self):
        """Transcribe all recordings"""
        
        recordings = self.voice_data.get('recordings', {})
        
        if not recordings:
            print("❌ No recordings to transcribe")
            return
        
        # Load progress
        progress = self.load_progress()
        completed_calls = set(progress.get('completed_calls', []))
        
        print(f"\n🎤 Transcribing {len(recordings)} recordings...")
        
        if completed_calls:
            print(f"🔄 Resuming transcription ({len(completed_calls)} already completed)")
        
        for i, (call_id, recording_data) in enumerate(recordings.items(), 1):
            if call_id in completed_calls:
                print(f"⏭️  Skipping call {i}/{len(recordings)}: {call_id} (already completed)")
                continue
            
            recording_file = recording_data.get('recording_file')
            
            if not recording_file or not Path(recording_file).exists():
                print(f"❌ Recording file not found for call {call_id}: {recording_file}")
                continue
            
            print(f"\n🎤 Transcribing call {i}/{len(recordings)}: {call_id}")
            print(f"    📁 File: {Path(recording_file).name}")
            
            try:
                # Transcribe the recording
                transcription = self.transcribe_recording(recording_file, call_id)
                
                if transcription:
                    self.transcriptions[call_id] = {
                        'call_id': call_id,
                        'recording_file': recording_file,
                        'transcription': transcription,
                        'call_data': recording_data.get('call_data', {}),
                        'transcription_timestamp': datetime.now().isoformat()
                    }
                    
                    print(f"    ✅ Transcription completed ({len(transcription.get('text', ''))} characters)")
                    
                    # Save progress
                    completed_calls.add(call_id)
                    self.save_progress(list(completed_calls))
                    
                else:
                    print(f"    ❌ Transcription failed")
                    
            except Exception as e:
                error_msg = f"Error transcribing call {call_id}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)
            
            # Rate limiting to avoid overwhelming the system
            time.sleep(2)
        
        print(f"\n📊 Transcription complete!")
        print(f"✅ Transcribed {len(self.transcriptions)} recordings")
    
    def transcribe_recording(self, recording_file, call_id):
        """Transcribe a single recording using Whisper"""
        
        try:
            # Create output directory for transcription files
            transcripts_dir = Path("data/transcripts")
            transcripts_dir.mkdir(exist_ok=True)
            
            # Output file for this transcription
            output_file = transcripts_dir / f"transcript_{call_id}.json"
            
            # Run Whisper transcription
            cmd = [
                'whisper',
                str(recording_file),
                '--model', 'base',  # Use base model for speed, can upgrade to 'large' for accuracy
                '--output_format', 'json',
                '--output_dir', str(transcripts_dir),
                '--language', 'en',
                '--task', 'transcribe'
            ]
            
            print(f"    🤖 Running Whisper...")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                # Find the generated transcript file
                # Whisper creates files with the original filename + extension
                recording_path = Path(recording_file)
                transcript_file = transcripts_dir / f"{recording_path.stem}.json"
                
                if transcript_file.exists():
                    # Load the transcription
                    with open(transcript_file, 'r') as f:
                        whisper_output = json.load(f)
                    
                    # Process the transcription
                    processed_transcription = self.process_whisper_output(whisper_output, call_id)
                    
                    return processed_transcription
                else:
                    print(f"      ❌ Transcript file not found: {transcript_file}")
                    return None
            else:
                print(f"      ❌ Whisper error: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print(f"      ❌ Whisper transcription timed out")
            return None
        except Exception as e:
            print(f"      ❌ Error running Whisper: {e}")
            return None
    
    def process_whisper_output(self, whisper_output, call_id):
        """Process Whisper output into structured format"""
        
        try:
            # Extract main transcription text
            full_text = whisper_output.get('text', '').strip()
            
            # Extract segments with timestamps
            segments = whisper_output.get('segments', [])
            
            # Process segments into messages
            messages = []
            
            for i, segment in enumerate(segments):
                segment_text = segment.get('text', '').strip()
                
                if len(segment_text) > 3:  # Skip very short segments
                    message = {
                        'sequence': i + 1,
                        'content': segment_text,
                        'start_time': segment.get('start', 0),
                        'end_time': segment.get('end', 0),
                        'confidence': segment.get('avg_logprob', 0),
                        'sender_type': self.determine_speaker_from_content(segment_text),
                        'source': 'whisper_transcription'
                    }
                    
                    messages.append(message)
            
            # Analyze conversation flow
            self.analyze_voice_conversation_flow(messages)
            
            processed_transcription = {
                'full_text': full_text,
                'messages': messages,
                'segments': segments,
                'language': whisper_output.get('language', 'en'),
                'duration': max([seg.get('end', 0) for seg in segments]) if segments else 0,
                'word_count': len(full_text.split()),
                'message_count': len(messages),
                'whisper_metadata': {
                    'model': 'base',
                    'task': 'transcribe',
                    'language': whisper_output.get('language', 'en')
                }
            }
            
            return processed_transcription
            
        except Exception as e:
            print(f"      ❌ Error processing Whisper output: {e}")
            return None
    
    def determine_speaker_from_content(self, text):
        """Determine speaker type from transcribed content"""
        
        text_lower = text.lower()
        
        # Advisor patterns (psychic/advisor language)
        advisor_patterns = [
            'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',
            'let me', 'i can help', 'what i\'m getting', 'i\'m seeing',
            'the universe', 'your guides', 'i\'m picking up', 'i\'m feeling',
            'what i\'m sensing', 'the energy around', 'your aura'
        ]
        
        # Customer patterns (questions and concerns)
        customer_patterns = [
            'will i', 'should i', 'when will', 'what about', 'can you tell me',
            'i want to know', 'my question', 'help me understand', 'what do you see',
            'i\'m wondering', 'can you help', 'what should i do', 'i need to know'
        ]
        
        # Greeting patterns (could be either)
        greeting_patterns = [
            'hello', 'hi', 'good morning', 'good afternoon', 'good evening',
            'thank you', 'thanks', 'bye', 'goodbye'
        ]
        
        advisor_score = sum(1 for pattern in advisor_patterns if pattern in text_lower)
        customer_score = sum(1 for pattern in customer_patterns if pattern in text_lower)
        greeting_score = sum(1 for pattern in greeting_patterns if pattern in text_lower)
        
        if greeting_score > 0 and advisor_score == 0 and customer_score == 0:
            return 'greeting'
        elif advisor_score > customer_score:
            return 'advisor'
        elif customer_score > advisor_score:
            return 'customer'
        else:
            return 'unknown'
    
    def analyze_voice_conversation_flow(self, messages):
        """Analyze voice conversation flow for patterns"""
        
        for i, msg in enumerate(messages):
            content = msg.get('content', '').lower()
            
            # Classify message types
            if '?' in content or any(word in content for word in ['what', 'how', 'when', 'where', 'why']):
                msg['message_type'] = 'question'
            elif any(word in content for word in ['yes', 'no', 'okay', 'right', 'exactly']):
                msg['message_type'] = 'response'
            elif any(word in content for word in ['hello', 'hi', 'good', 'thank', 'bye']):
                msg['message_type'] = 'greeting'
            else:
                msg['message_type'] = 'statement'
            
            # Check for potential issues
            if i > 0:
                prev_msg = messages[i-1]
                
                # Flag potential speaker identification issues
                if (msg.get('sender_type') == 'unknown' and 
                    prev_msg.get('sender_type') != 'unknown'):
                    msg['potential_issue'] = 'speaker_identification_unclear'
                
                # Flag very short segments that might be noise
                if len(msg.get('content', '')) < 10:
                    msg['potential_issue'] = 'very_short_segment'
        
        return messages
    
    def load_progress(self):
        """Load transcription progress"""
        
        try:
            if self.progress_file.exists():
                with open(self.progress_file, 'r') as f:
                    return json.load(f)
        except:
            pass
        
        return {}
    
    def save_progress(self, completed_calls):
        """Save transcription progress"""
        
        try:
            progress = {
                'completed_calls': completed_calls,
                'total_completed': len(completed_calls),
                'timestamp': datetime.now().isoformat()
            }
            
            with open(self.progress_file, 'w') as f:
                json.dump(progress, f, indent=2)
        except:
            pass
    
    def save_transcription_data(self):
        """Save all transcription data"""
        
        print(f"\n💾 Saving transcription data...")
        
        # Create comprehensive transcription dataset
        transcription_dataset = {
            'metadata': {
                'transcription_timestamp': datetime.now().isoformat(),
                'total_recordings': len(self.voice_data.get('recordings', {})),
                'successful_transcriptions': len(self.transcriptions),
                'transcription_method': 'openai_whisper',
                'whisper_model': 'base'
            },
            'transcriptions': self.transcriptions,
            'errors': self.errors
        }
        
        # Save complete transcription data
        transcription_file = Path("data/whisper_transcriptions_complete.json")
        with open(transcription_file, 'w') as f:
            json.dump(transcription_dataset, f, indent=2)
        
        print(f"💾 Transcription data saved to: {transcription_file}")
        
        # Create LLM training format
        self.create_transcription_llm_format(transcription_dataset)
        
        # Generate transcription summary
        self.generate_transcription_summary(transcription_dataset)
    
    def create_transcription_llm_format(self, transcription_dataset):
        """Create LLM training format for transcriptions"""
        
        print(f"🤖 Creating LLM training format for voice transcriptions...")
        
        llm_conversations = []
        
        for call_id, transcription_data in self.transcriptions.items():
            transcription = transcription_data['transcription']
            messages = transcription.get('messages', [])
            
            if len(messages) >= 2:  # Need at least 2 messages
                llm_conversation = {
                    'conversation_id': f"voice_{call_id}",
                    'call_id': call_id,
                    'source': 'google_voice_whisper',
                    'metadata': {
                        'duration': transcription.get('duration', 0),
                        'word_count': transcription.get('word_count', 0),
                        'message_count': len(messages),
                        'language': transcription.get('language', 'en'),
                        'full_text': transcription.get('full_text', '')
                    },
                    'messages': [
                        {
                            'role': msg.get('sender_type', 'unknown'),
                            'content': msg.get('content', ''),
                            'sequence': msg.get('sequence'),
                            'start_time': msg.get('start_time'),
                            'end_time': msg.get('end_time'),
                            'confidence': msg.get('confidence')
                        }
                        for msg in messages
                    ]
                }
                
                llm_conversations.append(llm_conversation)
        
        # Save LLM format
        llm_dataset = {
            'format': 'voice_transcription_training',
            'version': '1.0',
            'metadata': {
                'total_conversations': len(llm_conversations),
                'transcription_timestamp': datetime.now().isoformat(),
                'source': 'google_voice_whisper_transcription'
            },
            'conversations': llm_conversations
        }
        
        llm_file = Path("data/whisper_transcriptions_llm_format.json")
        with open(llm_file, 'w') as f:
            json.dump(llm_dataset, f, indent=2)
        
        print(f"🤖 LLM transcription format saved to: {llm_file}")
        print(f"📊 LLM voice conversations: {len(llm_conversations)}")
    
    def generate_transcription_summary(self, transcription_dataset):
        """Generate summary report for transcriptions"""
        
        metadata = transcription_dataset['metadata']
        
        total_duration = sum(
            t['transcription'].get('duration', 0) 
            for t in self.transcriptions.values()
        )
        
        total_words = sum(
            t['transcription'].get('word_count', 0) 
            for t in self.transcriptions.values()
        )
        
        report = f"""
WHISPER TRANSCRIPTION REPORT
============================

TRANSCRIPTION SUMMARY:
- Timestamp: {metadata['transcription_timestamp']}
- Total Recordings: {metadata['total_recordings']}
- Successful Transcriptions: {metadata['successful_transcriptions']}
- Success Rate: {metadata['successful_transcriptions']/max(metadata['total_recordings'], 1)*100:.1f}%
- Total Audio Duration: {total_duration:.1f} seconds ({total_duration/60:.1f} minutes)
- Total Words Transcribed: {total_words}

ERRORS ENCOUNTERED:
{chr(10).join(f"- {error}" for error in transcription_dataset['errors']) if transcription_dataset['errors'] else "- None"}

NEXT STEPS:
1. Correlate voice transcriptions with chat transcripts
2. Match conversations using customer IDs and timestamps
3. Combine all data sources into comprehensive dataset
4. Apply message ordering resolution across all conversations
5. Create final LLM training dataset

FILES CREATED:
- whisper_transcriptions_complete.json: Complete transcription data
- whisper_transcriptions_llm_format.json: LLM-ready format
- data/transcripts/: Directory with individual transcript files
- whisper_progress.json: Progress tracking
"""
        
        # Save report
        report_file = Path("data/whisper_transcription_report.txt")
        with open(report_file, 'w') as f:
            f.write(report)
        
        print(f"\n📋 WHISPER TRANSCRIPTION COMPLETE!")
        print(f"🎤 Transcribed {metadata['successful_transcriptions']} recordings")
        print(f"⏱️  Total audio: {total_duration/60:.1f} minutes")
        print(f"📝 Total words: {total_words}")
        print(f"📋 Full report saved to: {report_file}")

if __name__ == "__main__":
    transcriber = WhisperTranscriber()
    transcriber.run_transcription()
