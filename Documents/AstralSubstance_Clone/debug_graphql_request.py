#!/usr/bin/env python3
"""
Debug the GraphQL request to see what's causing the 400 error
"""

import sys
from pathlib import Path
import requests
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Confi<PERSON><PERSON><PERSON><PERSON>

def debug_graphql_request():
    """Debug the GraphQL request"""
    
    print("🔍 DEBUGGING GRAPHQL REQUEST")
    print("=" * 60)
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    print(f"🔐 Added {len([k for k, v in cookies.items() if v and not v.startswith('YOUR_')])} cookies")
    
    # Setup headers (exact from curl)
    headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'X-EpcApi-ID': '3a5d5fd4-cb38-f011-bf3f-98f2b31428e6',
        'X-Uid': '',
        'X-Domain-ID': '1',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': 'https://www.keen.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Priority': 'u=0',
        'TE': 'trailers'
    }
    
    # GraphQL endpoint
    graphql_url = f"{keen_config['base_url']}/api/graphqlv0"
    print(f"🌐 Endpoint: {graphql_url}")
    
    # Exact query from your curl command
    query_data = {
        "query": "query ($listId: Int, $first: Int, $after: String, $last: Int, $before: String, $filter: String, $sortBy: [String], $sortDescending: [Boolean], $fetch: Int, $offset: Int) { user { id customers(first: $first, after: $after, last: $last, before: $before, filter: $filter, sortBy: $sortBy, sortDescending: $sortDescending, listId: $listId, fetch: $fetch, offset: $offset) { totalCount pageInfo { hasNextPage hasPreviousPage } edges { cursor node { id userName nickname customerSince alerts { name } contacts { last { id activityId mailId masterTransactionId date amount { amount displayAmount(format: \"c2\") } type } } cumulativeSummary { totalCallCount totalChatCount totalPaidMails totalEarnings { amount displayAmount(format: \"c2\") } } list { id name } } } } } }",
        "variables": {
            "offset": 0,
            "fetch": 100,
            "filter": "",
            "sortBy": ["contacts.last.date"],
            "sortDescending": [True],
            "listId": 0
        }
    }
    
    print(f"📊 Query variables: {query_data['variables']}")
    
    try:
        print(f"\n🚀 Making GraphQL request...")
        response = session.post(graphql_url, headers=headers, json=query_data, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON response received")
                
                if 'errors' in data:
                    print(f"❌ GraphQL errors: {json.dumps(data['errors'], indent=2)}")
                
                if 'data' in data:
                    print(f"✅ Data received")
                    
                    if data['data'] and 'user' in data['data']:
                        user_data = data['data']['user']
                        print(f"✅ User data: {user_data.get('id', 'No ID')}")
                        
                        if 'customers' in user_data:
                            customers = user_data['customers']
                            total_count = customers.get('totalCount', 0)
                            edges_count = len(customers.get('edges', []))
                            
                            print(f"🎉 SUCCESS!")
                            print(f"📊 Total customers: {total_count}")
                            print(f"📊 Customers in this batch: {edges_count}")
                            
                            # Show sample customer
                            if customers.get('edges'):
                                sample_customer = customers['edges'][0]['node']
                                print(f"👤 Sample customer: {sample_customer.get('userName', 'Unknown')} (ID: {sample_customer.get('id')})")
                        else:
                            print(f"❌ No customers in user data")
                    else:
                        print(f"❌ No user data in response")
                else:
                    print(f"❌ No data in response")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"📄 Raw response: {response.text[:500]}...")
                
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"📄 Response text: {response.text[:500]}...")
            
            # Try to parse error response
            try:
                error_data = response.json()
                print(f"📄 Error JSON: {json.dumps(error_data, indent=2)}")
            except:
                pass
                
    except Exception as e:
        print(f"❌ Request error: {e}")
    
    # Also test the working endpoint we know
    print(f"\n🔄 Testing known working endpoint...")
    test_working_endpoint(session, headers, keen_config['base_url'])

def test_working_endpoint(session, headers, base_url):
    """Test the endpoint we know works"""
    
    working_url = f"{base_url}/api/graphql2"
    
    # Simple query that we know works
    working_query = {
        "query": """query($advisorId:Int $listingId:Int){
            ratingsAndReviews(advisorId:$advisorId listingId:$listingId pageSize:1 pageNumber:1){
                totalEdges
            }
        }""",
        "variables": {
            "listingId": "12471990",
            "advisorId": 56392386
        }
    }
    
    try:
        response = session.post(working_url, headers=headers, json=working_query, timeout=30)
        
        print(f"📊 Working endpoint status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and 'ratingsAndReviews' in data['data']:
                total_reviews = data['data']['ratingsAndReviews']['totalEdges']
                print(f"✅ Working endpoint confirmed: {total_reviews} reviews")
            else:
                print(f"⚠️  Working endpoint response: {data}")
        else:
            print(f"❌ Working endpoint failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Working endpoint error: {e}")

if __name__ == "__main__":
    debug_graphql_request()
