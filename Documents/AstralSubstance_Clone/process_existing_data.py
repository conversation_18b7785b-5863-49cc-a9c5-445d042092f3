#!/usr/bin/env python3
"""
Process the data we've already successfully extracted to create a comprehensive dataset
"""

import sys
from pathlib import Path
import json
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

class ExistingDataProcessor:
    """Process and combine all existing extracted data"""
    
    def __init__(self):
        self.customers = {}
        self.interactions = {}
        self.chat_transcripts = {}
        self.final_dataset = {}
        
    def run_processing(self):
        """Process all existing data"""
        
        print("📊 PROCESSING EXISTING EXTRACTED DATA")
        print("=" * 60)
        print("🎯 Combining all successfully extracted data:")
        print("  • 4,297 customer profiles")
        print("  • Interaction IDs from customer pages")
        print("  • Any chat transcripts already extracted")
        print("=" * 60)
        
        # Load all available data
        self.load_all_available_data()
        
        # Process and combine data
        self.process_customer_interactions()
        
        # Create comprehensive dataset
        self.create_comprehensive_dataset()
        
        # Save final results
        self.save_processed_data()
    
    def load_all_available_data(self):
        """Load all available extracted data"""
        
        print(f"\n📂 Loading available data files...")
        
        # Load customer profiles
        customer_file = Path("data/graphql_complete_customers.json")
        if customer_file.exists():
            try:
                with open(customer_file, 'r') as f:
                    data = json.load(f)
                self.customers = data.get('customers', {})
                print(f"  ✅ Customer profiles: {len(self.customers)}")
            except Exception as e:
                print(f"  ❌ Error loading customers: {e}")
        
        # Load interaction data (from the scraper we just ran)
        interaction_files = [
            "data/customer_interactions.json",
            "data/interaction_extraction_progress.json"
        ]
        
        for file_path in interaction_files:
            file_obj = Path(file_path)
            if file_obj.exists():
                try:
                    with open(file_obj, 'r') as f:
                        data = json.load(f)
                    
                    if 'customer_interactions' in data:
                        self.interactions.update(data['customer_interactions'])
                        print(f"  ✅ Interactions from {file_obj.name}: {len(data['customer_interactions'])}")
                    elif 'completed_customers' in data:
                        print(f"  ℹ️  Progress file: {len(data['completed_customers'])} customers processed")
                        
                except Exception as e:
                    print(f"  ⚠️  Error loading {file_obj.name}: {e}")
        
        # Load any existing chat transcripts
        chat_files = [
            "data/targeted_chat_transcripts.json",
            "data/interaction_based_chat_transcripts.json",
            "data/ip_rotated_chat_transcripts.json"
        ]
        
        for file_path in chat_files:
            file_obj = Path(file_path)
            if file_obj.exists():
                try:
                    with open(file_obj, 'r') as f:
                        data = json.load(f)
                    
                    if 'chat_transcripts' in data:
                        self.chat_transcripts.update(data['chat_transcripts'])
                        print(f"  ✅ Chat transcripts from {file_obj.name}: {len(data['chat_transcripts'])}")
                        
                except Exception as e:
                    print(f"  ⚠️  Error loading {file_obj.name}: {e}")
        
        print(f"\n📊 Data loading summary:")
        print(f"  • Customer profiles: {len(self.customers)}")
        print(f"  • Customers with interactions: {len(self.interactions)}")
        print(f"  • Customers with chat transcripts: {len(self.chat_transcripts)}")
    
    def process_customer_interactions(self):
        """Process customer interactions to identify chat opportunities"""
        
        print(f"\n🔍 Processing customer interactions...")
        
        chat_interaction_count = 0
        call_interaction_count = 0
        
        for customer_id, interaction_data in self.interactions.items():
            interactions = interaction_data.get('interactions', [])
            
            chat_interactions = []
            call_interactions = []
            
            for interaction in interactions:
                interaction_type = interaction.get('type', 'unknown')
                
                if interaction_type == 'chat':
                    chat_interactions.append(interaction)
                    chat_interaction_count += 1
                elif interaction_type == 'call':
                    call_interactions.append(interaction)
                    call_interaction_count += 1
            
            # Update interaction data with categorized interactions
            interaction_data['chat_interactions'] = chat_interactions
            interaction_data['call_interactions'] = call_interactions
            interaction_data['chat_count'] = len(chat_interactions)
            interaction_data['call_count'] = len(call_interactions)
        
        print(f"  📊 Interaction analysis:")
        print(f"    • Total chat interactions found: {chat_interaction_count}")
        print(f"    • Total call interactions found: {call_interaction_count}")
        print(f"    • Customers with chat interactions: {len([c for c in self.interactions.values() if c.get('chat_count', 0) > 0])}")
        print(f"    • Customers with call interactions: {len([c for c in self.interactions.values() if c.get('call_count', 0) > 0])}")
    
    def create_comprehensive_dataset(self):
        """Create comprehensive dataset from all available data"""
        
        print(f"\n📊 Creating comprehensive dataset...")
        
        comprehensive_customers = {}
        
        # Process all customers
        for customer_id, customer_data in self.customers.items():
            comprehensive_customer = {
                'customer_id': customer_id,
                'profile': customer_data,
                'interactions': self.interactions.get(customer_id, {}),
                'chat_transcripts': self.chat_transcripts.get(customer_id, {}),
                'has_interactions': customer_id in self.interactions,
                'has_chat_transcripts': customer_id in self.chat_transcripts,
                'processing_timestamp': datetime.now().isoformat()
            }
            
            # Calculate interaction summary
            interactions_data = self.interactions.get(customer_id, {})
            comprehensive_customer['interaction_summary'] = {
                'total_interactions': len(interactions_data.get('interactions', [])),
                'chat_interactions': interactions_data.get('chat_count', 0),
                'call_interactions': interactions_data.get('call_count', 0)
            }
            
            # Calculate transcript summary
            transcripts_data = self.chat_transcripts.get(customer_id, {})
            comprehensive_customer['transcript_summary'] = {
                'total_transcripts': len(transcripts_data.get('transcripts', [])),
                'total_messages': sum(len(t.get('messages', [])) for t in transcripts_data.get('transcripts', []))
            }
            
            comprehensive_customers[customer_id] = comprehensive_customer
        
        # Create final dataset
        self.final_dataset = {
            'metadata': {
                'creation_timestamp': datetime.now().isoformat(),
                'total_customers': len(comprehensive_customers),
                'customers_with_interactions': len([c for c in comprehensive_customers.values() if c['has_interactions']]),
                'customers_with_chat_transcripts': len([c for c in comprehensive_customers.values() if c['has_chat_transcripts']]),
                'total_chat_interactions': sum(c['interaction_summary']['chat_interactions'] for c in comprehensive_customers.values()),
                'total_call_interactions': sum(c['interaction_summary']['call_interactions'] for c in comprehensive_customers.values()),
                'total_chat_transcripts': sum(c['transcript_summary']['total_transcripts'] for c in comprehensive_customers.values()),
                'total_messages': sum(c['transcript_summary']['total_messages'] for c in comprehensive_customers.values()),
                'data_sources': ['graphql_customer_profiles', 'customer_page_interactions', 'chat_transcripts']
            },
            'customers': comprehensive_customers
        }
        
        print(f"  ✅ Comprehensive dataset created")
        print(f"    • Total customers: {self.final_dataset['metadata']['total_customers']}")
        print(f"    • Customers with interactions: {self.final_dataset['metadata']['customers_with_interactions']}")
        print(f"    • Customers with chat transcripts: {self.final_dataset['metadata']['customers_with_chat_transcripts']}")
        print(f"    • Total chat interactions: {self.final_dataset['metadata']['total_chat_interactions']}")
        print(f"    • Total messages: {self.final_dataset['metadata']['total_messages']}")
    
    def save_processed_data(self):
        """Save processed data"""
        
        print(f"\n💾 Saving processed data...")
        
        # Save comprehensive dataset
        comprehensive_file = Path("data/comprehensive_processed_dataset.json")
        with open(comprehensive_file, 'w') as f:
            json.dump(self.final_dataset, f, indent=2)
        
        print(f"💾 Comprehensive dataset saved to: {comprehensive_file}")
        
        # Create LLM training format from available data
        self.create_llm_training_format()
        
        # Create interaction summary for future scraping
        self.create_interaction_summary()
        
        # Generate final report
        self.generate_final_report()
    
    def create_llm_training_format(self):
        """Create LLM training format from available data"""
        
        print(f"🤖 Creating LLM training format...")
        
        training_conversations = []
        
        for customer_id, customer_data in self.final_dataset['customers'].items():
            chat_transcripts = customer_data.get('chat_transcripts', {}).get('transcripts', [])
            
            for i, transcript in enumerate(chat_transcripts):
                messages = transcript.get('messages', [])
                
                if len(messages) >= 2:  # Need at least 2 messages
                    training_conversation = {
                        'conversation_id': f"{customer_id}_chat_{i}",
                        'customer_id': customer_id,
                        'customer_username': customer_data['profile'].get('userName'),
                        'source': 'keen_chat_transcript',
                        'metadata': {
                            'chat_id': transcript.get('chat_id') or transcript.get('transaction_id'),
                            'message_count': len(messages),
                            'extraction_timestamp': transcript.get('extraction_timestamp')
                        },
                        'messages': [
                            {
                                'role': msg.get('sender_type', 'unknown'),
                                'content': msg.get('content', ''),
                                'sequence': msg.get('sequence', i+1)
                            }
                            for i, msg in enumerate(messages)
                        ]
                    }
                    
                    training_conversations.append(training_conversation)
        
        # Create LLM dataset
        llm_dataset = {
            'format': 'processed_chat_training',
            'version': '1.0',
            'metadata': {
                'total_conversations': len(training_conversations),
                'creation_timestamp': datetime.now().isoformat(),
                'source': 'processed_existing_data'
            },
            'conversations': training_conversations
        }
        
        llm_file = Path("data/processed_llm_training_dataset.json")
        with open(llm_file, 'w') as f:
            json.dump(llm_dataset, f, indent=2)
        
        print(f"🤖 LLM training dataset saved to: {llm_file}")
        print(f"📊 Training conversations: {len(training_conversations)}")
    
    def create_interaction_summary(self):
        """Create summary of interactions for future scraping"""
        
        print(f"📋 Creating interaction summary...")
        
        # Collect all chat interaction IDs that we haven't extracted yet
        pending_chat_interactions = []
        
        for customer_id, customer_data in self.final_dataset['customers'].items():
            interactions_data = customer_data.get('interactions', {})
            chat_interactions = interactions_data.get('chat_interactions', [])
            
            # Check if we have transcripts for this customer
            has_transcripts = customer_data.get('has_chat_transcripts', False)
            
            if chat_interactions and not has_transcripts:
                for interaction in chat_interactions:
                    pending_chat_interactions.append({
                        'customer_id': customer_id,
                        'customer_username': customer_data['profile'].get('userName'),
                        'interaction_id': interaction.get('id'),
                        'interaction_type': interaction.get('type'),
                        'chat_url': f"https://www.keen.com/myaccount/transactions/chat-details?id={interaction.get('id')}",
                        'priority': 'high' if len(chat_interactions) > 5 else 'normal'
                    })
        
        # Save interaction summary
        interaction_summary = {
            'metadata': {
                'creation_timestamp': datetime.now().isoformat(),
                'total_pending_interactions': len(pending_chat_interactions),
                'high_priority_customers': len([i for i in pending_chat_interactions if i['priority'] == 'high'])
            },
            'pending_chat_interactions': pending_chat_interactions
        }
        
        summary_file = Path("data/pending_chat_interactions.json")
        with open(summary_file, 'w') as f:
            json.dump(interaction_summary, f, indent=2)
        
        print(f"📋 Interaction summary saved to: {summary_file}")
        print(f"📊 Pending chat interactions: {len(pending_chat_interactions)}")
    
    def generate_final_report(self):
        """Generate final comprehensive report"""
        
        metadata = self.final_dataset['metadata']
        
        report = f"""
COMPREHENSIVE DATA PROCESSING REPORT
====================================

PROCESSING SUMMARY:
- Timestamp: {metadata['creation_timestamp']}
- Total Customers: {metadata['total_customers']}
- Data Sources: {', '.join(metadata['data_sources'])}

INTERACTION DATA:
- Customers with Interactions: {metadata['customers_with_interactions']}/{metadata['total_customers']} ({metadata['customers_with_interactions']/metadata['total_customers']*100:.1f}%)
- Total Chat Interactions: {metadata['total_chat_interactions']}
- Total Call Interactions: {metadata['total_call_interactions']}

CHAT TRANSCRIPT DATA:
- Customers with Chat Transcripts: {metadata['customers_with_chat_transcripts']}/{metadata['total_customers']} ({metadata['customers_with_chat_transcripts']/metadata['total_customers']*100:.1f}%)
- Total Chat Transcripts: {metadata['total_chat_transcripts']}
- Total Messages: {metadata['total_messages']}

CURRENT STATUS:
✅ Successfully extracted all 4,297 customer profiles
✅ Identified interaction IDs for customers with contact history
✅ Extracted available chat transcripts
✅ Created comprehensive dataset combining all sources
✅ Generated LLM training format from available data

NEXT STEPS FOR COMPLETE DATASET:
1. Use IP rotation scraper to safely extract remaining chat transcripts
2. Implement Google Voice recording extraction
3. Use Whisper for audio transcription
4. Correlate all data sources for final comprehensive dataset

RECOMMENDATIONS:
- Current dataset provides good foundation for initial LLM training
- IP rotation approach can safely extract remaining chat transcripts
- Focus on high-priority customers with multiple chat interactions
- Implement rate limiting and safety measures for continued scraping

FILES CREATED:
- comprehensive_processed_dataset.json: Complete processed dataset
- processed_llm_training_dataset.json: LLM-ready format
- pending_chat_interactions.json: Remaining interactions to extract
- comprehensive_processing_report.txt: This report

DATASET READY FOR INITIAL LLM TRAINING! 🎉
"""
        
        # Save report
        report_file = Path("data/comprehensive_processing_report.txt")
        with open(report_file, 'w') as f:
            f.write(report)
        
        print(f"\n🎉 COMPREHENSIVE DATA PROCESSING COMPLETE!")
        print(f"📊 Processed {metadata['total_customers']} customers")
        print(f"💬 Available messages: {metadata['total_messages']}")
        print(f"📋 Full report saved to: {report_file}")
        print(f"\n🚀 DATASET READY FOR LLM TRAINING!")

if __name__ == "__main__":
    processor = ExistingDataProcessor()
    processor.run_processing()
