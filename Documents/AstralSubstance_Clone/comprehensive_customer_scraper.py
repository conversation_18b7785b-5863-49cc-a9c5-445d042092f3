#!/usr/bin/env python3
"""
Comprehensive customer and conversation scraper based on visual exploration findings
SAFETY: READ-ONLY operations only, no data modification
"""

import sys
from pathlib import Path
import json
import time
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import ConfigLoader

class ComprehensiveCustomerScraper:
    """Comprehensive scraper for all customer data and conversations"""

    def __init__(self):
        self.driver = None
        self.customers = {}
        self.conversation_data = {}
        self.errors = []
        self.progress_file = Path("data/scraping_progress.json")

    def run_comprehensive_scrape(self):
        """Run the complete scraping process"""

        print("🚀 COMPREHENSIVE CUSTOMER & CONVERSATION SCRAPER")
        print("=" * 60)
        print("⚠️  SAFETY MODE: READ-ONLY OPERATIONS ONLY")
        print("📊 Based on visual exploration findings:")
        print("  - 48 customers found with .list-item selector")
        print("  - Customer detail pages have conversation tables")
        print("  - No pagination required")
        print("=" * 60)

        try:
            # Setup and authenticate
            self.setup_browser()
            self.authenticate()

            # Navigate to customer page
            if self.navigate_to_customers():
                # Extract customer list
                customer_list = self.extract_customer_list()

                if customer_list:
                    print(f"✅ Found {len(customer_list)} customers to scrape")

                    # Scrape each customer's details and conversations
                    self.scrape_all_customers(customer_list)

                    # Process and save final dataset
                    self.create_final_dataset()
                else:
                    print("❌ No customers found to scrape")
            else:
                print("❌ Failed to navigate to customer page")

        except Exception as e:
            print(f"❌ Critical error: {e}")
            self.errors.append(f"Critical error: {e}")
        finally:
            self.cleanup()

    def setup_browser(self):
        """Setup Chrome browser with optimal settings"""

        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)

        # Disable automation detection
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        print("🌐 Browser setup complete")

    def authenticate(self):
        """Setup authentication cookies"""

        config = ConfigLoader.load_config("config/config.yaml")
        keen_config = config['keen']
        base_url = keen_config['base_url']

        # Navigate to main page first
        self.driver.get(base_url)
        time.sleep(2)

        # Add authentication cookies
        cookies = keen_config['cookies']
        for name, value in cookies.items():
            if value and not value.startswith('YOUR_'):
                try:
                    self.driver.add_cookie({
                        'name': name,
                        'value': value,
                        'domain': '.keen.com'
                    })
                except Exception as e:
                    print(f"  ⚠️  Failed to add cookie {name}: {e}")

        print("🔐 Authentication complete")

    def navigate_to_customers(self):
        """Navigate to customer management page"""

        config = ConfigLoader.load_config("config/config.yaml")
        customer_url = f"{config['keen']['base_url']}/app/myaccount/customers"

        print(f"🌐 Navigating to: {customer_url}")
        self.driver.get(customer_url)

        # Wait for page load
        wait = WebDriverWait(self.driver, 15)
        try:
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(5)  # Let everything load

            # Verify we're on the right page
            if "customers" in self.driver.current_url.lower():
                print("✅ Customer page loaded successfully")
                return True
            else:
                print(f"❌ Unexpected page: {self.driver.current_url}")
                return False

        except TimeoutException:
            print("❌ Customer page failed to load")
            return False

    def extract_customer_list(self):
        """Extract the list of all customers from the main page"""

        print("👥 Extracting customer list...")

        # Based on visual exploration, use .list-item selector
        try:
            customer_elements = self.driver.find_elements(By.CSS_SELECTOR, '.list-item')
            print(f"  📋 Found {len(customer_elements)} customer elements")

            customers = []
            for i, element in enumerate(customer_elements):
                customer_data = self.extract_customer_basic_info(element, i)
                if customer_data:
                    customers.append(customer_data)

            print(f"  ✅ Extracted basic info for {len(customers)} customers")
            return customers

        except Exception as e:
            print(f"  ❌ Error extracting customer list: {e}")
            self.errors.append(f"Error extracting customer list: {e}")
            return []

    def extract_customer_basic_info(self, element, index):
        """Extract basic customer information from list element"""

        try:
            customer_data = {
                'index': index,
                'element_id': id(element),
                'extraction_timestamp': datetime.now().isoformat()
            }

            # Extract text content
            text = element.text.strip()
            if text:
                customer_data['display_text'] = text

                # Try to extract customer name/username from text
                lines = [line.strip() for line in text.split('\n') if line.strip()]
                if lines:
                    # First non-empty line is likely the customer name
                    customer_data['name'] = lines[0]

                    # Look for additional info in other lines
                    for line in lines[1:]:
                        if '@' in line:
                            customer_data['email'] = line
                        elif '$' in line:
                            customer_data['earnings'] = line
                        elif '/' in line or '-' in line:
                            customer_data['date'] = line

            # Extract any data attributes
            for attr in ['data-id', 'data-customer-id', 'data-user-id']:
                value = element.get_attribute(attr)
                if value:
                    customer_data['id'] = value
                    break

            # Store element reference for clicking
            customer_data['element'] = element

            return customer_data

        except Exception as e:
            print(f"    ⚠️  Error extracting customer {index}: {e}")
            return None

    def scrape_all_customers(self, customer_list):
        """Scrape detailed information for all customers"""

        print(f"\n📋 Starting detailed scraping for {len(customer_list)} customers...")

        # Load existing progress if available
        progress = self.load_progress()
        start_index = progress.get('last_completed_index', -1) + 1

        if start_index > 0:
            print(f"🔄 Resuming from customer {start_index + 1}")

        for i, customer in enumerate(customer_list[start_index:], start_index):
            print(f"\n👤 Customer {i+1}/{len(customer_list)}: {customer.get('name', 'Unknown')}")

            try:
                # Click on customer to view details
                if self.click_customer(customer):
                    # Extract detailed customer information
                    customer_details = self.extract_customer_details()

                    # Extract all conversations for this customer
                    conversations = self.extract_all_conversations()

                    # Store complete customer data
                    customer_id = customer.get('id', f'customer_{i}')
                    self.customers[customer_id] = {
                        'basic_info': customer,
                        'details': customer_details,
                        'conversations': conversations,
                        'conversation_count': len(conversations),
                        'extraction_timestamp': datetime.now().isoformat()
                    }

                    print(f"    ✅ Extracted {len(conversations)} conversations")

                    # Navigate back to customer list
                    self.navigate_back_to_list()

                    # Save progress
                    self.save_progress(i, len(customer_list))

                else:
                    print(f"    ❌ Could not access customer details")
                    self.errors.append(f"Could not access customer {i+1}")

            except Exception as e:
                error_msg = f"Error processing customer {i+1}: {e}"
                print(f"    ❌ {error_msg}")
                self.errors.append(error_msg)

                # Try to recover by going back to customer list
                try:
                    self.navigate_back_to_list()
                except:
                    # If we can't recover, restart from customer page
                    self.navigate_to_customers()

            # Rate limiting
            time.sleep(2)

        print(f"\n🎉 Scraping complete! Processed {len(self.customers)} customers")

    def click_customer(self, customer):
        """Click on a customer element to view their details"""

        try:
            element = customer.get('element')
            if not element:
                return False

            # Scroll element into view
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(1)

            # Get current URL
            original_url = self.driver.current_url

            # Try to click
            element.click()
            time.sleep(3)

            # Check if URL changed (indicating successful navigation)
            new_url = self.driver.current_url
            if new_url != original_url and 'customer' in new_url:
                return True
            else:
                # Try JavaScript click as fallback
                self.driver.execute_script("arguments[0].click();", element)
                time.sleep(3)

                final_url = self.driver.current_url
                return final_url != original_url and 'customer' in final_url

        except Exception as e:
            print(f"      ❌ Click error: {e}")
            return False

    def extract_customer_details(self):
        """Extract detailed customer information from detail page"""

        details = {
            'url': self.driver.current_url,
            'page_title': self.driver.title
        }

        try:
            # Extract customer name from page
            name_selectors = ['h1', 'h2', '.customer-name', '.profile-name']
            for selector in name_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.text.strip():
                        details['full_name'] = element.text.strip()
                        break
                except:
                    continue

            # Extract customer ID from URL
            url_match = re.search(r'/customers/(\d+)', self.driver.current_url)
            if url_match:
                details['customer_id'] = url_match.group(1)

            # Extract any visible customer information
            info_elements = self.driver.find_elements(By.CSS_SELECTOR, '.customer-info, .user-info, .profile-info')
            for element in info_elements:
                text = element.text.strip()
                if text:
                    details['additional_info'] = details.get('additional_info', [])
                    details['additional_info'].append(text)

        except Exception as e:
            details['extraction_error'] = str(e)

        return details

    def extract_all_conversations(self):
        """Extract all conversations/sessions from customer detail page"""

        print("      💬 Extracting conversations...")

        conversations = []

        try:
            # Based on visual exploration, conversations are in table elements
            table_elements = self.driver.find_elements(By.CSS_SELECTOR, 'table')

            for table in table_elements:
                # Check if this table contains conversation data
                if self.is_conversation_table(table):
                    table_conversations = self.extract_conversations_from_table(table)
                    conversations.extend(table_conversations)

            # Also look for other conversation containers
            other_selectors = ['.conversation', '.session', '.chat', '.call', '.message-thread']
            for selector in other_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        conv_data = self.extract_conversation_from_element(element)
                        if conv_data:
                            conversations.append(conv_data)
                except:
                    continue

            # Process and order all conversations
            if conversations:
                conversations = self.process_and_order_conversations(conversations)

            print(f"        ✅ Found {len(conversations)} conversations")

        except Exception as e:
            print(f"        ❌ Error extracting conversations: {e}")
            conversations = [{'extraction_error': str(e)}]

        return conversations

    def is_conversation_table(self, table):
        """Check if a table contains conversation data"""

        try:
            # Look for conversation-related headers or content
            headers = table.find_elements(By.CSS_SELECTOR, 'th')
            header_text = ' '.join([h.text.lower() for h in headers])

            conversation_indicators = [
                'date', 'time', 'duration', 'session', 'conversation',
                'call', 'chat', 'message', 'transcript'
            ]

            if any(indicator in header_text for indicator in conversation_indicators):
                return True

            # Check table content for conversation patterns
            rows = table.find_elements(By.CSS_SELECTOR, 'tr')
            if len(rows) > 1:  # Has data rows
                sample_text = ' '.join([row.text.lower() for row in rows[:3]])
                if any(indicator in sample_text for indicator in conversation_indicators):
                    return True

            return False

        except:
            return False

    def extract_conversations_from_table(self, table):
        """Extract conversations from a table element"""

        conversations = []

        try:
            rows = table.find_elements(By.CSS_SELECTOR, 'tbody tr, tr')

            for i, row in enumerate(rows):
                # Skip header rows
                if row.find_elements(By.CSS_SELECTOR, 'th'):
                    continue

                conversation = self.extract_conversation_from_row(row, i)
                if conversation:
                    conversations.append(conversation)

        except Exception as e:
            print(f"          ❌ Error extracting from table: {e}")

        return conversations

    def extract_conversation_from_row(self, row, index):
        """Extract conversation data from a table row"""

        try:
            conversation = {
                'row_index': index,
                'extraction_method': 'table_row'
            }

            # Get all cell data
            cells = row.find_elements(By.CSS_SELECTOR, 'td, th')
            cell_texts = [cell.text.strip() for cell in cells]

            if not any(cell_texts):  # Skip empty rows
                return None

            conversation['raw_cells'] = cell_texts

            # Try to identify conversation components
            for i, cell_text in enumerate(cell_texts):
                if not cell_text:
                    continue

                # Look for date/time patterns
                if re.search(r'\d{1,2}/\d{1,2}/\d{4}|\d{4}-\d{2}-\d{2}', cell_text):
                    conversation['date'] = cell_text

                # Look for time patterns
                if re.search(r'\d{1,2}:\d{2}', cell_text):
                    conversation['time'] = cell_text

                # Look for duration patterns
                if re.search(r'\d+:\d+|\d+\s*min', cell_text, re.IGNORECASE):
                    conversation['duration'] = cell_text

                # Look for session/conversation IDs
                if re.search(r'session|conv|call', cell_text, re.IGNORECASE) and re.search(r'\d+', cell_text):
                    conversation['session_id'] = cell_text

                # Look for conversation content/transcript
                if len(cell_text) > 50:  # Likely conversation content
                    conversation['content'] = cell_text

            # Try to click on row to get detailed conversation
            detailed_conversation = self.try_extract_detailed_conversation(row)
            if detailed_conversation:
                conversation['detailed_messages'] = detailed_conversation

            return conversation

        except Exception as e:
            return {'extraction_error': str(e), 'row_index': index}

    def try_extract_detailed_conversation(self, row):
        """Try to click on conversation row to get detailed messages"""

        try:
            # Check if row is clickable
            if (row.get_attribute('onclick') or
                row.find_elements(By.CSS_SELECTOR, 'a, button') or
                'clickable' in row.get_attribute('class', '').lower()):

                # Get current URL
                original_url = self.driver.current_url

                # Try to click
                row.click()
                time.sleep(2)

                # Check if we navigated to a conversation detail page
                new_url = self.driver.current_url
                if new_url != original_url:
                    # Extract detailed conversation messages
                    messages = self.extract_conversation_messages()

                    # Navigate back
                    self.driver.back()
                    time.sleep(2)

                    return messages

        except Exception as e:
            # If clicking fails, continue without detailed messages
            pass

        return None

    def extract_conversation_messages(self):
        """Extract individual messages from a conversation detail page"""

        messages = []

        try:
            # Look for message elements
            message_selectors = [
                '.message', '.chat-message', '.conversation-message',
                '.transcript-line', '.dialogue-line', 'p', 'div'
            ]

            for selector in message_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        message = self.extract_message_from_element(element)
                        if message:
                            messages.append(message)

                    if messages:  # Found messages with this selector
                        break

                except:
                    continue

            # If no structured messages found, try to extract from page text
            if not messages:
                messages = self.extract_messages_from_page_text()

        except Exception as e:
            messages = [{'extraction_error': str(e)}]

        return messages

    def extract_message_from_element(self, element):
        """Extract message data from a DOM element"""

        try:
            text = element.text.strip()
            if not text or len(text) < 3:
                return None

            message = {
                'content': text,
                'element_tag': element.tag_name,
                'element_class': element.get_attribute('class') or ''
            }

            # Try to determine sender (advisor vs customer)
            classes = message['element_class'].lower()
            if 'advisor' in classes or 'psychic' in classes:
                message['sender_type'] = 'advisor'
            elif 'customer' in classes or 'user' in classes:
                message['sender_type'] = 'customer'
            else:
                # Try to infer from content
                message['sender_type'] = self.infer_sender_from_content(text)

            # Look for timestamp
            timestamp_elem = element.find_element(By.CSS_SELECTOR, '.timestamp, .time, [data-time]')
            if timestamp_elem:
                message['timestamp'] = timestamp_elem.text or timestamp_elem.get_attribute('data-time')

            return message

        except:
            return None

    def extract_messages_from_page_text(self):
        """Extract messages from unstructured page text"""

        try:
            # Get all text from the page
            page_text = self.driver.find_element(By.TAG_NAME, 'body').text

            # Split into potential messages
            lines = [line.strip() for line in page_text.split('\n') if line.strip()]

            messages = []
            for i, line in enumerate(lines):
                if len(line) > 10:  # Skip very short lines
                    message = {
                        'content': line,
                        'line_number': i,
                        'sender_type': self.infer_sender_from_content(line),
                        'extraction_method': 'page_text'
                    }
                    messages.append(message)

            return messages

        except:
            return []

    def infer_sender_from_content(self, text):
        """Infer message sender from content patterns"""

        text_lower = text.lower()

        # Advisor patterns
        advisor_patterns = [
            'i see', 'i sense', 'i feel', 'the cards', 'spirit', 'energy',
            'let me', 'i can help', 'what i\'m getting', 'i\'m seeing'
        ]

        # Customer patterns
        customer_patterns = [
            'will i', 'should i', 'when will', 'what about', 'can you tell me',
            'i want to know', 'my question', 'help me understand'
        ]

        advisor_score = sum(1 for pattern in advisor_patterns if pattern in text_lower)
        customer_score = sum(1 for pattern in customer_patterns if pattern in text_lower)

        if advisor_score > customer_score:
            return 'advisor'
        elif customer_score > advisor_score:
            return 'customer'
        else:
            return 'unknown'

    def extract_conversation_from_element(self, element):
        """Extract conversation data from a generic element"""

        try:
            text = element.text.strip()
            if not text or len(text) < 10:
                return None

            conversation = {
                'content': text,
                'element_tag': element.tag_name,
                'element_class': element.get_attribute('class') or '',
                'extraction_method': 'generic_element'
            }

            # Look for conversation metadata
            for attr in ['data-id', 'data-session-id', 'data-conversation-id']:
                value = element.get_attribute(attr)
                if value:
                    conversation['id'] = value
                    break

            return conversation

        except:
            return None

    def process_and_order_conversations(self, conversations):
        """Process and order conversations, resolving message ordering issues"""

        print("        🔄 Processing and ordering conversations...")

        processed_conversations = []

        for i, conversation in enumerate(conversations):
            try:
                # Add conversation index
                conversation['conversation_index'] = i

                # Process detailed messages if available
                if 'detailed_messages' in conversation and conversation['detailed_messages']:
                    conversation['detailed_messages'] = self.resolve_message_order(conversation['detailed_messages'])

                # Extract timestamp for conversation ordering
                conversation['parsed_timestamp'] = self.extract_conversation_timestamp(conversation)

                processed_conversations.append(conversation)

            except Exception as e:
                print(f"          ⚠️  Error processing conversation {i}: {e}")
                conversation['processing_error'] = str(e)
                processed_conversations.append(conversation)

        # Sort conversations by timestamp if available
        try:
            timestamped_conversations = [c for c in processed_conversations if c.get('parsed_timestamp')]
            non_timestamped_conversations = [c for c in processed_conversations if not c.get('parsed_timestamp')]

            if timestamped_conversations:
                timestamped_conversations.sort(key=lambda x: x['parsed_timestamp'])
                print(f"          ✅ Ordered {len(timestamped_conversations)} conversations by timestamp")

            # Combine ordered and non-ordered conversations
            ordered_conversations = timestamped_conversations + non_timestamped_conversations

        except Exception as e:
            print(f"          ⚠️  Error ordering conversations: {e}")
            ordered_conversations = processed_conversations

        return ordered_conversations

    def resolve_message_order(self, messages):
        """Resolve out-of-order messages in conversations"""

        print("          🔄 Resolving message order...")

        # Try to sort by timestamp if available
        timestamped_messages = [msg for msg in messages if msg.get('timestamp')]
        non_timestamped_messages = [msg for msg in messages if not msg.get('timestamp')]

        if timestamped_messages:
            try:
                # Parse and sort by timestamp
                for msg in timestamped_messages:
                    msg['parsed_timestamp'] = self.parse_message_timestamp(msg['timestamp'])

                timestamped_messages.sort(key=lambda x: x.get('parsed_timestamp', datetime.min))

                # Add sequence numbers
                for i, msg in enumerate(timestamped_messages):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = True

                print(f"            ✅ Ordered {len(timestamped_messages)} messages by timestamp")

            except Exception as e:
                print(f"            ⚠️  Could not sort by timestamp: {e}")
                # Fall back to original order with sequence numbers
                for i, msg in enumerate(timestamped_messages):
                    msg['sequence'] = i + 1
                    msg['order_resolved'] = False

        # Handle non-timestamped messages
        for i, msg in enumerate(non_timestamped_messages):
            msg['sequence'] = len(timestamped_messages) + i + 1
            msg['order_resolved'] = False

        # Combine all messages
        all_messages = timestamped_messages + non_timestamped_messages

        # Analyze conversation flow for potential ordering issues
        self.analyze_conversation_flow(all_messages)

        return all_messages

    def extract_conversation_timestamp(self, conversation):
        """Extract timestamp from conversation data"""

        try:
            # Try different timestamp fields
            timestamp_fields = ['date', 'time', 'timestamp', 'created_at']

            for field in timestamp_fields:
                if field in conversation and conversation[field]:
                    return self.parse_message_timestamp(conversation[field])

            # Try to extract from content
            if 'content' in conversation:
                timestamp_match = re.search(r'\d{1,2}/\d{1,2}/\d{4}|\d{4}-\d{2}-\d{2}', conversation['content'])
                if timestamp_match:
                    return self.parse_message_timestamp(timestamp_match.group())

        except:
            pass

        return None

    def parse_message_timestamp(self, timestamp_str):
        """Parse timestamp string to datetime for sorting"""

        # Try different timestamp formats
        formats = [
            '%m/%d/%Y %H:%M:%S',
            '%Y-%m-%d %H:%M:%S',
            '%m/%d/%Y %H:%M',
            '%Y-%m-%d %H:%M',
            '%m/%d/%Y',
            '%Y-%m-%d',
            '%H:%M:%S',
            '%H:%M',
        ]

        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str, fmt)
            except:
                continue

        # If no format works, return current time (will sort to end)
        return datetime.now()

    def analyze_conversation_flow(self, messages):
        """Analyze conversation flow to identify potential ordering issues"""

        for i, msg in enumerate(messages):
            content = msg.get('content', '').lower()

            # Classify message types
            if '?' in content or any(word in content for word in ['what', 'how', 'when', 'where', 'why', 'can you']):
                msg['message_type'] = 'question'
            elif any(phrase in content for phrase in ['yes', 'no', 'i think', 'i see', 'that means']):
                msg['message_type'] = 'response'
            elif any(word in content for word in ['hello', 'hi', 'good morning', 'good evening']):
                msg['message_type'] = 'greeting'
            else:
                msg['message_type'] = 'statement'

            # Check for potential ordering issues
            if i > 0:
                prev_msg = messages[i-1]

                # Flag potential issues
                if (msg.get('message_type') == 'response' and
                    prev_msg.get('message_type') == 'response' and
                    msg.get('sender_type') == prev_msg.get('sender_type')):
                    msg['potential_order_issue'] = 'consecutive_responses_same_sender'

                if (msg.get('message_type') == 'question' and
                    prev_msg.get('message_type') == 'question' and
                    msg.get('sender_type') == prev_msg.get('sender_type')):
                    msg['potential_order_issue'] = 'consecutive_questions_same_sender'

                # Check for response without preceding question
                if (msg.get('message_type') == 'response' and
                    prev_msg.get('message_type') != 'question'):
                    msg['potential_order_issue'] = 'response_without_question'

        return messages

    def navigate_back_to_list(self):
        """Navigate back to the customer list"""

        try:
            # Try browser back button first
            self.driver.back()
            time.sleep(2)

            # Verify we're back on the customer list page
            if 'customers' in self.driver.current_url and 'customers/' not in self.driver.current_url:
                return True
            else:
                # If back button didn't work, navigate directly
                config = ConfigLoader.load_config("config/config.yaml")
                customer_url = f"{config['keen']['base_url']}/app/myaccount/customers"
                self.driver.get(customer_url)
                time.sleep(3)
                return True

        except Exception as e:
            print(f"      ⚠️  Error navigating back: {e}")
            return False

    def load_progress(self):
        """Load scraping progress from file"""

        try:
            if self.progress_file.exists():
                with open(self.progress_file, 'r') as f:
                    progress = json.load(f)
                print(f"📄 Loaded progress: {progress.get('completed_customers', 0)} customers completed")
                return progress
        except Exception as e:
            print(f"⚠️  Could not load progress: {e}")

        return {}

    def save_progress(self, current_index, total_customers):
        """Save current scraping progress"""

        try:
            progress = {
                'last_completed_index': current_index,
                'completed_customers': current_index + 1,
                'total_customers': total_customers,
                'timestamp': datetime.now().isoformat(),
                'customers_extracted': len(self.customers)
            }

            with open(self.progress_file, 'w') as f:
                json.dump(progress, f, indent=2)

            # Also save current customer data
            self.save_intermediate_data()

        except Exception as e:
            print(f"⚠️  Could not save progress: {e}")

    def save_intermediate_data(self):
        """Save intermediate customer data"""

        try:
            intermediate_file = Path("data/customers_intermediate.json")

            intermediate_data = {
                'extraction_timestamp': datetime.now().isoformat(),
                'customers_count': len(self.customers),
                'customers': self.customers,
                'errors': self.errors
            }

            with open(intermediate_file, 'w') as f:
                json.dump(intermediate_data, f, indent=2)

        except Exception as e:
            print(f"⚠️  Could not save intermediate data: {e}")

    def create_final_dataset(self):
        """Create the final dataset for LLM training"""

        print(f"\n📊 CREATING FINAL DATASET")
        print("=" * 40)

        if not self.customers:
            print("❌ No customer data to process")
            return

        # Analyze extracted data
        total_customers = len(self.customers)
        total_conversations = sum(len(customer.get('conversations', [])) for customer in self.customers.values())

        print(f"👥 Total customers: {total_customers}")
        print(f"💬 Total conversations: {total_conversations}")

        # Create comprehensive dataset
        dataset = {
            'metadata': {
                'extraction_timestamp': datetime.now().isoformat(),
                'total_customers': total_customers,
                'total_conversations': total_conversations,
                'extraction_method': 'comprehensive_web_scraping',
                'source': 'keen.com customer management interface',
                'data_quality': self.analyze_data_quality()
            },
            'customers': self.customers,
            'errors': self.errors
        }

        # Save complete dataset
        dataset_file = Path("data/keen_complete_dataset.json")
        with open(dataset_file, 'w') as f:
            json.dump(dataset, f, indent=2)
        print(f"💾 Complete dataset saved to: {dataset_file}")

        # Create LLM training format
        self.create_llm_training_format(dataset)

        # Generate summary report
        self.generate_summary_report(dataset)

    def analyze_data_quality(self):
        """Analyze the quality of extracted data"""

        quality_metrics = {
            'customers_with_conversations': 0,
            'customers_with_detailed_messages': 0,
            'conversations_with_timestamps': 0,
            'messages_with_sender_identification': 0,
            'potential_ordering_issues': 0
        }

        for customer in self.customers.values():
            conversations = customer.get('conversations', [])

            if conversations:
                quality_metrics['customers_with_conversations'] += 1

            for conversation in conversations:
                if conversation.get('detailed_messages'):
                    quality_metrics['customers_with_detailed_messages'] += 1

                if conversation.get('parsed_timestamp'):
                    quality_metrics['conversations_with_timestamps'] += 1

                for message in conversation.get('detailed_messages', []):
                    if message.get('sender_type') and message['sender_type'] != 'unknown':
                        quality_metrics['messages_with_sender_identification'] += 1

                    if message.get('potential_order_issue'):
                        quality_metrics['potential_ordering_issues'] += 1

        return quality_metrics

    def create_llm_training_format(self, dataset):
        """Create LLM training format from the dataset"""

        print(f"\n🤖 Creating LLM training format...")

        training_conversations = []

        for customer_id, customer_data in dataset['customers'].items():
            conversations = customer_data.get('conversations', [])

            for conversation in conversations:
                # Skip conversations without detailed messages
                if not conversation.get('detailed_messages'):
                    continue

                messages = conversation['detailed_messages']

                # Filter out messages with extraction errors
                valid_messages = [msg for msg in messages if 'extraction_error' not in msg]

                if len(valid_messages) < 2:  # Need at least 2 messages for a conversation
                    continue

                # Create training conversation
                training_conversation = {
                    'conversation_id': f"{customer_id}_{conversation.get('conversation_index', 0)}",
                    'customer_id': customer_id,
                    'metadata': {
                        'date': conversation.get('date'),
                        'duration': conversation.get('duration'),
                        'message_count': len(valid_messages),
                        'order_resolved': all(msg.get('order_resolved', False) for msg in valid_messages),
                        'potential_issues': [msg.get('potential_order_issue') for msg in valid_messages if msg.get('potential_order_issue')]
                    },
                    'messages': []
                }

                # Format messages for training
                for msg in valid_messages:
                    formatted_message = {
                        'role': 'advisor' if msg.get('sender_type') == 'advisor' else 'customer',
                        'content': msg.get('content', ''),
                        'sequence': msg.get('sequence'),
                        'timestamp': msg.get('timestamp')
                    }
                    training_conversation['messages'].append(formatted_message)

                training_conversations.append(training_conversation)

        # Save LLM training format
        llm_dataset = {
            'format': 'conversational_training',
            'version': '1.0',
            'metadata': {
                'total_conversations': len(training_conversations),
                'extraction_timestamp': datetime.now().isoformat(),
                'source': 'keen.com_comprehensive_scrape'
            },
            'conversations': training_conversations
        }

        llm_file = Path("data/keen_llm_training_dataset.json")
        with open(llm_file, 'w') as f:
            json.dump(llm_dataset, f, indent=2)

        print(f"🤖 LLM training dataset saved to: {llm_file}")
        print(f"📊 Training conversations: {len(training_conversations)}")

    def generate_summary_report(self, dataset):
        """Generate a summary report of the extraction"""

        print(f"\n📋 GENERATING SUMMARY REPORT")
        print("=" * 40)

        metadata = dataset['metadata']
        quality = metadata['data_quality']

        report = f"""
KEEN.COM CUSTOMER DATA EXTRACTION REPORT
========================================

EXTRACTION SUMMARY:
- Timestamp: {metadata['extraction_timestamp']}
- Total Customers: {metadata['total_customers']}
- Total Conversations: {metadata['total_conversations']}
- Extraction Method: {metadata['extraction_method']}

DATA QUALITY METRICS:
- Customers with conversations: {quality['customers_with_conversations']}/{metadata['total_customers']} ({quality['customers_with_conversations']/metadata['total_customers']*100:.1f}%)
- Customers with detailed messages: {quality['customers_with_detailed_messages']}/{metadata['total_customers']} ({quality['customers_with_detailed_messages']/metadata['total_customers']*100:.1f}%)
- Conversations with timestamps: {quality['conversations_with_timestamps']}/{metadata['total_conversations']} ({quality['conversations_with_timestamps']/metadata['total_conversations']*100:.1f}%)
- Messages with sender identification: {quality['messages_with_sender_identification']}
- Potential ordering issues detected: {quality['potential_ordering_issues']}

ERRORS ENCOUNTERED:
{chr(10).join(f"- {error}" for error in dataset['errors']) if dataset['errors'] else "- None"}

NEXT STEPS:
1. Review the LLM training dataset for quality
2. Correlate with Google Voice recordings using customer IDs
3. Use Whisper to transcribe audio recordings
4. Combine text and audio data for comprehensive training dataset

FILES CREATED:
- keen_complete_dataset.json: Complete raw extraction data
- keen_llm_training_dataset.json: Formatted for LLM training
- customers_intermediate.json: Intermediate progress data
- scraping_progress.json: Progress tracking
"""

        # Save report
        report_file = Path("data/extraction_report.txt")
        with open(report_file, 'w') as f:
            f.write(report)

        print(report)
        print(f"📋 Full report saved to: {report_file}")

    def cleanup(self):
        """Clean up resources"""

        if self.driver:
            try:
                self.driver.quit()
                print("🔒 Browser closed")
            except:
                pass

# Main execution
if __name__ == "__main__":
    scraper = ComprehensiveCustomerScraper()
    scraper.run_comprehensive_scrape()