#!/usr/bin/env python3
"""
Test the ASMX customer endpoints to extract all 4,297 customers
"""

import sys
from pathlib import Path
import requests
import json
import xml.etree.ElementTree as ET
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils.config_loader import Confi<PERSON><PERSON><PERSON><PERSON>

def test_asmx_endpoints():
    """Test the ASMX customer endpoints"""
    
    # Load config
    config = ConfigLoader.load_config("config/config.yaml")
    keen_config = config['keen']
    
    # Setup session with cookies
    session = requests.Session()
    cookies = keen_config['cookies']
    for name, value in cookies.items():
        if value and not value.startswith('YOUR_'):
            session.cookies.set(name, value)
    
    # Headers for ASMX requests
    asmx_headers = {
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:138.0) Gecko/******** Firefox/138.0',
        'Accept': '*/*',
        'Content-Type': 'application/json',
        'X-EpcApi-ID': '7f0d309c-be38-f011-bf3f-98f2b31428e6',
        'X-Domain-ID': '1',
        'Origin': 'https://www.keen.com',
        'Referer': 'https://www.keen.com/app/myaccount/customers',
    }
    
    base_url = keen_config['base_url']
    
    print("🔍 TESTING ASMX CUSTOMER ENDPOINTS")
    print("=" * 60)
    
    # Test customer list endpoint
    print("\n📋 Step 1: Testing customer list endpoint...")
    list_endpoint = f"{base_url}/DomainOverrides/Advice/Customers/List.asmx"
    test_customer_list_endpoint(session, asmx_headers, list_endpoint)
    
    # Test customer search endpoint
    print("\n🔍 Step 2: Testing customer search endpoint...")
    search_endpoint = f"{base_url}/DomainOverrides/Advice/Customers/Search.asmx"
    test_customer_search_endpoint(session, asmx_headers, search_endpoint)

def test_customer_list_endpoint(session, headers, endpoint):
    """Test the customer list endpoint with various parameters"""
    
    print(f"  Testing endpoint: {endpoint}")
    
    # ASMX endpoints often accept different parameter formats
    test_payloads = [
        {},  # Empty payload
        {'pageSize': 100, 'pageNumber': 1},
        {'pageSize': 1000, 'pageNumber': 1},
        {'pageSize': 5000, 'pageNumber': 1},
        {'first': 100},
        {'first': 1000},
        {'first': 5000},
        {'limit': 100, 'offset': 0},
        {'limit': 1000, 'offset': 0},
        {'limit': 5000, 'offset': 0},
        {'count': 100},
        {'count': 1000},
        {'count': 5000},
    ]
    
    for i, payload in enumerate(test_payloads):
        print(f"    Test {i+1}: {payload}")
        
        try:
            response = session.post(endpoint, headers=headers, json=payload, timeout=30)
            
            print(f"      Status: {response.status_code}")
            print(f"      Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            print(f"      Content-Length: {len(response.content)}")
            
            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '').lower()
                
                if 'json' in content_type:
                    try:
                        data = response.json()
                        print(f"      ✅ JSON response!")
                        analyze_json_response(data, payload)
                        
                        # If this looks like customer data, save it
                        if is_customer_data(data):
                            customer_count = count_customers_in_data(data)
                            print(f"      🎉 FOUND {customer_count} CUSTOMERS!")
                            
                            if customer_count > 1000:
                                print(f"      🚀 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                                save_customer_data(data, f"customer_list_payload_{i+1}.json")
                                return data
                                
                    except json.JSONDecodeError:
                        print(f"      ❌ Invalid JSON")
                        
                elif 'xml' in content_type:
                    print(f"      📄 XML response")
                    try:
                        xml_data = parse_xml_response(response.text)
                        if xml_data:
                            analyze_xml_response(xml_data, payload)
                            
                            if is_customer_data(xml_data):
                                customer_count = count_customers_in_data(xml_data)
                                print(f"      🎉 FOUND {customer_count} CUSTOMERS!")
                                
                                if customer_count > 1000:
                                    print(f"      🚀 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                                    save_customer_data(xml_data, f"customer_list_xml_{i+1}.json")
                                    return xml_data
                                    
                    except Exception as e:
                        print(f"      ❌ XML parsing error: {e}")
                        
                else:
                    print(f"      📄 Text response: {response.text[:200]}...")
                    
            elif response.status_code == 500:
                print(f"      ⚠️  Server error - might need different parameters")
            else:
                print(f"      ❌ Failed")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
        
        time.sleep(1)
    
    return None

def test_customer_search_endpoint(session, headers, endpoint):
    """Test the customer search endpoint"""
    
    print(f"  Testing endpoint: {endpoint}")
    
    # Search endpoints often accept search terms
    search_payloads = [
        {},  # Empty search
        {'search': ''},
        {'query': ''},
        {'searchTerm': ''},
        {'search': '*'},
        {'query': '*'},
        {'searchTerm': '*'},
        {'search': 'user'},
        {'pageSize': 100},
        {'pageSize': 1000},
        {'pageSize': 5000},
        {'search': '', 'pageSize': 1000},
        {'query': '', 'pageSize': 1000},
        {'searchTerm': '', 'pageSize': 1000},
    ]
    
    for i, payload in enumerate(search_payloads):
        print(f"    Search test {i+1}: {payload}")
        
        try:
            response = session.post(endpoint, headers=headers, json=payload, timeout=30)
            
            print(f"      Status: {response.status_code}")
            print(f"      Content-Length: {len(response.content)}")
            
            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '').lower()
                
                if 'json' in content_type:
                    try:
                        data = response.json()
                        print(f"      ✅ JSON response!")
                        analyze_json_response(data, payload)
                        
                        if is_customer_data(data):
                            customer_count = count_customers_in_data(data)
                            print(f"      🎉 FOUND {customer_count} CUSTOMERS!")
                            
                            if customer_count > 1000:
                                print(f"      🚀 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                                save_customer_data(data, f"customer_search_payload_{i+1}.json")
                                return data
                                
                    except json.JSONDecodeError:
                        print(f"      ❌ Invalid JSON")
                        
                elif 'xml' in content_type:
                    print(f"      📄 XML response")
                    try:
                        xml_data = parse_xml_response(response.text)
                        if xml_data:
                            analyze_xml_response(xml_data, payload)
                            
                            if is_customer_data(xml_data):
                                customer_count = count_customers_in_data(xml_data)
                                print(f"      🎉 FOUND {customer_count} CUSTOMERS!")
                                
                                if customer_count > 1000:
                                    print(f"      🚀 THIS MIGHT BE THE FULL CUSTOMER LIST!")
                                    save_customer_data(xml_data, f"customer_search_xml_{i+1}.json")
                                    return xml_data
                                    
                    except Exception as e:
                        print(f"      ❌ XML parsing error: {e}")
                        
                else:
                    print(f"      📄 Text response: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"      ❌ Error: {e}")
        
        time.sleep(1)
    
    return None

def parse_xml_response(xml_text):
    """Parse XML response and convert to dict"""
    
    try:
        root = ET.fromstring(xml_text)
        return xml_to_dict(root)
    except ET.ParseError:
        return None

def xml_to_dict(element):
    """Convert XML element to dictionary"""
    
    result = {}
    
    # Add attributes
    if element.attrib:
        result['@attributes'] = element.attrib
    
    # Add text content
    if element.text and element.text.strip():
        if len(element) == 0:
            return element.text.strip()
        else:
            result['text'] = element.text.strip()
    
    # Add child elements
    for child in element:
        child_data = xml_to_dict(child)
        
        if child.tag in result:
            # Convert to list if multiple elements with same tag
            if not isinstance(result[child.tag], list):
                result[child.tag] = [result[child.tag]]
            result[child.tag].append(child_data)
        else:
            result[child.tag] = child_data
    
    return result

def analyze_json_response(data, payload):
    """Analyze JSON response for customer data"""
    
    print(f"        📊 JSON keys: {list(data.keys())[:10]}")
    
    # Look for customer-related data
    customer_indicators = ['customer', 'user', 'client', 'member']
    
    for key, value in data.items():
        if any(indicator in key.lower() for indicator in customer_indicators):
            if isinstance(value, list):
                print(f"        👥 {key}: {len(value)} items")
            elif isinstance(value, dict):
                print(f"        👥 {key}: {type(value)} with keys {list(value.keys())[:5]}")
            else:
                print(f"        👥 {key}: {type(value)}")

def analyze_xml_response(data, payload):
    """Analyze XML response for customer data"""
    
    print(f"        📊 XML root keys: {list(data.keys())[:10]}")
    
    # Look for customer-related data in XML
    def find_customer_data(obj, path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                if any(indicator in key.lower() for indicator in ['customer', 'user', 'client', 'member']):
                    if isinstance(value, list):
                        print(f"        👥 {current_path}: {len(value)} items")
                    else:
                        print(f"        👥 {current_path}: {type(value)}")
                elif isinstance(value, (dict, list)):
                    find_customer_data(value, current_path)
        elif isinstance(obj, list):
            for i, item in enumerate(obj[:3]):  # Check first 3 items
                find_customer_data(item, f"{path}[{i}]")
    
    find_customer_data(data)

def is_customer_data(data):
    """Check if data contains customer information"""
    
    def search_for_customers(obj, depth=0):
        if depth > 5:
            return False
            
        if isinstance(obj, dict):
            for key, value in obj.items():
                # Look for customer-related keys
                if any(indicator in key.lower() for indicator in ['customer', 'user', 'client', 'member']):
                    if isinstance(value, (list, dict)) and value:
                        return True
                elif isinstance(value, (dict, list)):
                    if search_for_customers(value, depth + 1):
                        return True
        elif isinstance(obj, list):
            # If it's a list of objects with customer-like fields
            if obj and isinstance(obj[0], dict):
                first_item = obj[0]
                customer_fields = ['id', 'name', 'email', 'username', 'customerId', 'userName']
                if any(field in first_item for field in customer_fields):
                    return True
            
            for item in obj[:3]:  # Check first 3 items
                if search_for_customers(item, depth + 1):
                    return True
                    
        return False
    
    return search_for_customers(data)

def count_customers_in_data(data):
    """Count potential customers in the data"""
    
    def count_items(obj, depth=0):
        if depth > 5:
            return 0
            
        count = 0
        if isinstance(obj, dict):
            for key, value in obj.items():
                if any(indicator in key.lower() for indicator in ['customer', 'user', 'client', 'member']):
                    if isinstance(value, list):
                        count += len(value)
                elif isinstance(value, (dict, list)):
                    count += count_items(value, depth + 1)
        elif isinstance(obj, list):
            # Check if this looks like a list of customers
            if obj and isinstance(obj[0], dict):
                first_item = obj[0]
                customer_fields = ['id', 'name', 'email', 'username', 'customerId', 'userName']
                if any(field in first_item for field in customer_fields):
                    count += len(obj)
            
            for item in obj:
                count += count_items(item, depth + 1)
                
        return count
    
    return count_items(data)

def save_customer_data(data, filename):
    """Save customer data to file"""
    
    filepath = f"data/{filename}"
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2)
    print(f"        💾 Customer data saved to: {filepath}")

if __name__ == "__main__":
    test_asmx_endpoints()
